package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleParse;
import org.biosino.lf.pds.article.mapper.ArticleParseMapper;
import org.biosino.lf.pds.article.service.IArticleParseService;
import org.biosino.lf.pds.common.enums.ParseStatusEnums;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 文献解析元数据表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ArticleParseServiceImpl extends ServiceImpl<ArticleParseMapper, ArticleParse> implements IArticleParseService {

    @Override
    public List<ArticleParse> findUnprocessedRecords(int limit) {
        return list(new LambdaQueryWrapper<ArticleParse>()
                .eq(ArticleParse::getStatus, ParseStatusEnums.Pending.getCode())
                .orderByAsc(ArticleParse::getCreateTime)
                .last("LIMIT " + limit));
    }

    @Override
    public boolean updateStatus(ArticleParse articleParse, Long docId, Integer status, String errorMsg) {
        articleParse.setDocId(docId);
        articleParse.setStatus(status);
        articleParse.setErrorMsg(errorMsg);
        articleParse.setUpdateTime(new Date());
        return updateById(articleParse);
    }
}
