package org.biosino.lf.pds.web.controller.task;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.custbean.dto.JournalScriptDTO;
import org.biosino.lf.pds.article.custbean.dto.SelectJournalDTO;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelDTO;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelScriptDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.custbean.vo.SelectJournalVO;
import org.biosino.lf.pds.article.custbean.vo.SelectVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsScriptlabelVO;
import org.biosino.lf.pds.article.domain.TbDdsSite;
import org.biosino.lf.pds.common.annotation.Log;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.enums.BusinessType;
import org.biosino.lf.pds.task.service.ITbDdsScriptlabelService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 脚本标签Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/scriptLabel")
@RequiredArgsConstructor
public class TbDdsScriptlabelController extends BaseController {
    private final ITbDdsScriptlabelService tbDdsScriptlabelService;

    /*#################################### 脚本标签 ########################################*/

    /**
     * 查询脚本标签列表
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @GetMapping("/list")
    public TableDataInfo list(TbDdsScriptlabelDTO dto) {
        startPage();
        List<TbDdsScriptlabelVO> list = tbDdsScriptlabelService.selectTbDdsScriptlabelList(dto);
        return getDataTable(list);
    }

    /**
     * 新增/修改脚本标签
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "脚本标签", businessType = BusinessType.UPDATE)
    @PostMapping("/save")
    public AjaxResult saveScriptLabel(@RequestBody TbDdsScriptlabelDTO dto) {
        return tbDdsScriptlabelService.saveScriptLabel(dto, getUserId());
    }

    /**
     * 删除脚本标签
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "脚本标签", businessType = BusinessType.DELETE)
    @GetMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return tbDdsScriptlabelService.deleteTbDdsScriptlabelByIds(ids);
    }

    /**
     * 获取脚本标签对应站点信息
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @GetMapping("/site/{scriptlabelId}")
    public AjaxResult siteInfo(@PathVariable Integer scriptlabelId) {
        List<TbDdsSite> siteList = tbDdsScriptlabelService.siteInfo(scriptlabelId);
        return AjaxResult.success(siteList);
    }


    /*#################################### 批次脚本分配 ########################################*/

    /**
     * 批次：脚本标签--脚本关联数据，新增或修改
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "脚本标签配置", businessType = BusinessType.UPDATE)
    @PostMapping("/saveLabelAndScript")
    public AjaxResult saveLabelAndScript(@RequestBody TbDdsScriptlabelScriptDTO dto) {
        return tbDdsScriptlabelService.saveLabelAndScript(dto, getUserId());
    }


    /**
     * 批次：查询当前标签对应脚本列表
     */
    @GetMapping("/scriptListOfLabel/{scriptlabelId}")
    @PreAuthorize("@ss.hasAnyPermi('label:index')")
    public AjaxResult scriptListOfLabel(@PathVariable Integer scriptlabelId) {
        List<ScriptVO> list = tbDdsScriptlabelService.scriptListOfLabel(scriptlabelId);
        return AjaxResult.success(list);
    }


    /*#################################### 源刊、高校--期刊、脚本分配 ########################################*/

    /**
     * 根据名称查询出版社数据（组成下拉框数据）
     */
    @GetMapping("/findPublisherByName")
    @PreAuthorize("@ss.hasAnyPermi('label:index')")
    public AjaxResult findPublisherByName(String name) {
        List<SelectVO> list = tbDdsScriptlabelService.findPublisherByName(name);
        return AjaxResult.success(list);
    }


    /**
     * 源刊、高校待分配期刊列表
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @GetMapping("/toSelectJournalList")
    public TableDataInfo toSelectJournalList(SelectJournalDTO dto) {
        startPage();
        List<SelectJournalVO> list = tbDdsScriptlabelService.toSelectJournalList(dto);
        return getDataTable(list);
    }

    /**
     * 已分配期刊列表
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @GetMapping("/assignedJournalList")
    public TableDataInfo assignedJournalList(SelectJournalDTO dto) {
        startPage();
        List<SelectJournalVO> list = tbDdsScriptlabelService.assignedJournalList(dto);
        return getDataTable(list);
    }

    /**
     * 保存期刊与脚本关联
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "期刊脚本关联", businessType = BusinessType.UPDATE)
    @PostMapping("/saveJournalScript")
    public AjaxResult saveJournalScript(@RequestBody JournalScriptDTO dto) {
        return tbDdsScriptlabelService.saveJournalScript(dto);
    }

    /**
     * 移除期刊脚本关联
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "移除期刊脚本", businessType = BusinessType.UPDATE)
    @PostMapping("/removeJournalScript")
    public AjaxResult removeJournalScript(@RequestBody JournalScriptDTO dto) {
        return tbDdsScriptlabelService.removeJournalScript(dto);
    }

    /**
     * 应用期刊到标签
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "应用期刊到标签", businessType = BusinessType.UPDATE)
    @PostMapping("/applyJournalToLabel")
    public AjaxResult applyJournalToLabel(@RequestBody JournalScriptDTO dto) {
        return tbDdsScriptlabelService.applyJournalToLabel(dto, getUserId());
    }

    /**
     * 移除标签的期刊分配
     */
    @PreAuthorize("@ss.hasPermi('label:index')")
    @Log(title = "移除标签的期刊分配", businessType = BusinessType.DELETE)
    @PostMapping("/removeJournalApply")
    public AjaxResult removeJournalApply(@RequestBody JournalScriptDTO dto) {
        return tbDdsScriptlabelService.removeJournalApply(dto, getUserId());
    }

}
