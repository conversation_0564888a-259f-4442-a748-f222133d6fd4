package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleGene;
import org.biosino.lf.pds.article.domain.Gene;
import org.biosino.lf.pds.article.mapper.GeneMapper;
import org.biosino.lf.pds.article.service.IArticleGeneService;
import org.biosino.lf.pds.article.service.IGeneService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 基因信息表 服务实现类
 */
@Service
public class GeneServiceImpl extends ServiceImpl<GeneMapper, Gene> implements IGeneService {
    
    private final IArticleGeneService articleGeneService;
    
    public GeneServiceImpl(IArticleGeneService articleGeneService) {
        this.articleGeneService = articleGeneService;
    }
    
    @Override
    public List<Gene> findByGeneSymbolIn(Collection<String> geneSymbols) {
        Set<String> collect = geneSymbols.stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        if (CollUtil.isEmpty(collect)) {
            return CollUtil.newArrayList();
        }
        Wrapper<Gene> query = Wrappers.<Gene>lambdaQuery().in(Gene::getGeneSymbol, collect);
        return this.list(query);
    }

    @Override
    public List<Gene> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return this.list(
                Wrappers.<Gene>lambdaQuery()
                        .in(Gene::getId, ids)
        );
    }
    
    @Override
    public boolean removeUnusedByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        // 查找仍在使用的Gene IDs
        List<ArticleGene> articleGenes = articleGeneService.list(
                Wrappers.<ArticleGene>lambdaQuery()
                        .in(ArticleGene::getGeneId, ids)
        );
        
        if (CollUtil.isEmpty(articleGenes)) {
            // 如果没有引用，则删除所有指定的IDs
            return this.removeByIds(ids);
        } else {
            // 找出未被引用的IDs
            List<Long> usedIds = articleGenes.stream()
                    .map(ArticleGene::getGeneId)
                    .collect(Collectors.toList());
            
            List<Long> unusedIds = new ArrayList<>(ids);
            unusedIds.removeAll(usedIds);
            
            if (CollUtil.isNotEmpty(unusedIds)) {
                return this.removeByIds(unusedIds);
            }
            return true;
        }
    }
}
