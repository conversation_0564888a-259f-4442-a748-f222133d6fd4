package org.biosino.lf.plosp.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@RestController
@RequestMapping("/journal")
@RequiredArgsConstructor
public class JournalController extends BaseController {

    private final IJournalService journalService;

    @RequestMapping("/list")
    public TableDataInfo list(JournalQueryDTO queryDTO) {
        startPage();
        List<Journal> list = journalService.selectJournalList(queryDTO);
        return getDataTable(list);
    }
}
