package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.service.IJournalService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 期刊服务实现类
 *
 * <AUTHOR>
 */
@Service
public class IJournalServiceImpl extends ServiceImpl<JournalMapper, Journal> implements IJournalService {
    @Override
    public List<Journal> selectJournalList(JournalQueryDTO queryDTO) {
        Wrapper<Journal> qw = Wrappers.<Journal>lambdaQuery().like(StrUtil.isNotBlank(queryDTO.getTitle()), Journal::getTitle, queryDTO.getTitle());

        return this.list(qw);
    }

    @Override
    public List<Journal> findByTitleIn(List<String> journalNames) {
        journalNames = journalNames.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(journalNames)) {
            return CollUtil.newArrayList();
        }
        return this.list(Wrappers.<Journal>lambdaQuery().in(Journal::getTitle, journalNames));
    }
}
