package org.biosino.lf.pds.article.custbean.vo.api;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.biosino.lf.pds.article.custbean.dto.api.ScriptInfoDTO;

import java.util.List;

/**
 * 节点信息VO
 *
 * <AUTHOR>
 */
@Data
public class SiteInfoVO {
    /**
     * 站点ID
     */
    private Integer siteId;
    
    /**
     * 站点名称
     */
    private String siteName;
    
    /**
     * 站点简称
     */
    private String siteAddr;
    
    /**
     * 排序
     */
    private String ordinary;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 获取任务间隔（分钟）
     */
    private Integer obtainTaskInterval;
    
    /**
     * 任务线程数
     */
    private Integer taskThreadNum;

    /**
     * 脚本信息
     */
    private List<ScriptInfoDTO> script;

    private String msg;

}
