package org.biosino.lf.pds.article.custbean.vo;

import lombok.Data;

import java.util.Date;

/**
 * 脚本信息
 *
 * <AUTHOR>
 */
@Data
public class ScriptVO {
    /**
     * 脚本ID
     */
    private Integer scriptId;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本文件名称
     */
    private String scriptFileName;

    /**
     * 脚本文件id
     */
    private String scriptFileId;

    /**
     * 脚本类型（批次、源刊、高校等）
     */
    private String scriptTypeStr;

    /**
     * 脚本MD5
     */
    private String scriptMd5;

    /**
     * 状态（0-正常，1-停用）
     * 字典类型：sys_normal_disable
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 最后一次运行成功时间
     */
    private Date lastSuccessTime;

    /**
     * 排序序号
     */
    private Integer sort;
}
