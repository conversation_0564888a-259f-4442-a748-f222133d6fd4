package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * 文件上传DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FileUploadDTO {
    private File file;
    private MultipartFile multipartFile;
    private FileTypeEnum fileTypeEnum;
    private String fileMd5;
    private String originalFilename;
    private String source;
    private Long docId;
    private Integer createSiteId;
    private boolean checkExist = false;

    // File upload constructors
    public FileUploadDTO(File file, FileTypeEnum fileTypeEnum, String fileMd5, String originalFilename, boolean checkExist) {
        this.file = file;
        this.fileTypeEnum = fileTypeEnum;
        this.fileMd5 = fileMd5;
        this.originalFilename = originalFilename;
        this.checkExist = checkExist;
    }

    public FileUploadDTO(File file, FileTypeEnum fileTypeEnum, String fileMd5, String originalFilename, String source, Long docId, boolean checkExist) {
        this.file = file;
        this.fileTypeEnum = fileTypeEnum;
        this.fileMd5 = fileMd5;
        this.originalFilename = originalFilename;
        this.source = source;
        this.docId = docId;
        this.checkExist = checkExist;
    }

    public FileUploadDTO(File file, FileTypeEnum fileTypeEnum, String fileMd5, String originalFilename, String source, Long docId, Integer createSiteId, boolean checkExist) {
        this.file = file;
        this.fileTypeEnum = fileTypeEnum;
        this.fileMd5 = fileMd5;
        this.originalFilename = originalFilename;
        this.source = source;
        this.docId = docId;
        this.createSiteId = createSiteId;
        this.checkExist = checkExist;
    }

    // MultipartFile upload constructors
    public FileUploadDTO(MultipartFile multipartFile, FileTypeEnum fileTypeEnum, String fileMd5, String originalFilename, String source, Long docId) {
        this.multipartFile = multipartFile;
        this.fileTypeEnum = fileTypeEnum;
        this.fileMd5 = fileMd5;
        this.originalFilename = originalFilename;
        this.source = source;
        this.docId = docId;
    }

    public FileUploadDTO(MultipartFile multipartFile, FileTypeEnum fileTypeEnum, String fileMd5, String originalFilename, String source, Long docId, boolean checkExist) {
        this.multipartFile = multipartFile;
        this.fileTypeEnum = fileTypeEnum;
        this.fileMd5 = fileMd5;
        this.originalFilename = originalFilename;
        this.source = source;
        this.docId = docId;
        this.checkExist = checkExist;
    }
}
