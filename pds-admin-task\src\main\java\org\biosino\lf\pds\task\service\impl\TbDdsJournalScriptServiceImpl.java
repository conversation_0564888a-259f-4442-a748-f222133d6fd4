package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.custbean.dto.ScriptDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.mapper.TbDdsJournalScriptMapper;
import org.biosino.lf.pds.article.mapper.TbDdsScriptlabelScriptMapper;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.config.AppConfig;
import org.biosino.lf.pds.common.constant.DictTypeConstants;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.domain.entity.SysDictData;
import org.biosino.lf.pds.common.enums.DirectoryEnum;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.biosino.lf.pds.common.enums.task.ScriptTypeEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.MyHashUtil;
import org.biosino.lf.pds.common.utils.file.FileUtils;
import org.biosino.lf.pds.system.service.ISysDictTypeService;
import org.biosino.lf.pds.task.service.ITbDdsJournalScriptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * 脚本管理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsJournalScriptServiceImpl extends ServiceImpl<TbDdsJournalScriptMapper, TbDdsJournalScript> implements ITbDdsJournalScriptService {
    private final TbDdsJournalScriptMapper tbDdsJournalScriptMapper;
    private final ITbDdsFileService tbDdsFileService;
    private final ISysDictTypeService sysDictTypeService;
    private final TbDdsScriptlabelScriptMapper tbDdsScriptlabelScriptMapper;
    private final JournalMapper journalMapper;

    private static final String FILE_NAME_CONNECTOR = "_";
    private static final String SCRIPT_FILE_SUFFIX = ".pyscript";
    /**
     * 脚本文件过期时间(7天)，单位毫秒
     */
    public static final long SCRIPT_EXPIRE_TIME = 7L * 24 * 60 * 60 * 1000;

    private static final Map<String, Integer> STAT_CACHE_MAP = new ConcurrentHashMap<>();
    private static final String READ_SCRIPT_TMP = "read_script_tmp";

    @Override
    public List<ScriptVO> selectScriptList(ScriptDTO queryDto) {
        final List<TbDdsJournalScript> list = tbDdsJournalScriptMapper.selectScriptList(queryDto);
        final List<ScriptVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            final Map<String, String> scriptTypeMap = initDictValLabelMap(sysDictTypeService, DictTypeConstants.SCRIPT_TYPE);
            for (TbDdsJournalScript item : list) {
                final ScriptVO vo = new ScriptVO();
                vo.setScriptId(item.getId());
                vo.setScriptName(item.getScriptName());

                final Long fileId = item.getFileId();
                vo.setScriptFileId(fileId.toString());
                final TbDdsFile ddsFile = tbDdsFileService.getById(fileId);
                vo.setScriptFileName(ddsFile.getFileName());

                // 从字典读取脚本类型
                final List<String> type = item.getType();
                if (CollUtil.isNotEmpty(type)) {
                    vo.setScriptTypeStr(type.stream().map(scriptTypeMap::get).collect(Collectors.joining(",")));
                }

                vo.setScriptMd5(item.getScriptMd5());
                vo.setStatus(item.getStatus());
                vo.setRemark(item.getRemark());
                vo.setUploadTime(item.getCreateTime());
                vo.setUpdateTime(item.getUpdateTime());
                vo.setLastSuccessTime(item.getLastSuccessTime());
                result.add(vo);
            }
        }
        return result;
    }

    @Override
    public AjaxResult upload(MultipartFile file) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }

        // 检查文件扩展名是否为.pyscript
        final String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(SCRIPT_FILE_SUFFIX)) {
            return AjaxResult.error(StrUtil.format("只能上传{}格式的文件", SCRIPT_FILE_SUFFIX));
        }

        // 检查文件大小，限制为10MB
        if (file.getSize() > 10 * 1024 * 1024) {
            return AjaxResult.error("文件大小不能超过10MB");
        }
        final String tempFileName = IdUtil.fastSimpleUUID() + FILE_NAME_CONNECTOR + originalFilename;
        final File tempFile = new File(initTempDir(), tempFileName);
        try {
            // 将上传的文件保存到临时目录
            file.transferTo(tempFile);

            return AjaxResult.success("上传成功", tempFileName);
        } catch (Exception e) {
            // 异常处理
            log.error("上传脚本文件失败", e);
            return AjaxResult.error("上传失败：" + e.getMessage());
        }
    }

    private File initTempDir() {
        Integer c = STAT_CACHE_MAP.get(READ_SCRIPT_TMP);
        if (c == null) {
            c = 0;
        }
        final int currCount = c + 1;

        final File tempDir = AppConfig.initDataHome(DirectoryEnum.temp);
        final File tempScriptDir = new File(tempDir, "tmp_script");
        if (!tempScriptDir.exists()) {
            tempScriptDir.mkdirs();
        }

        // 调用此方法超过100次，则删除过期文件
        if (currCount > 100) {
            // 重置计数器
            STAT_CACHE_MAP.put(READ_SCRIPT_TMP, 0);

            // 读取tempScriptDir目录下所有文件并清理过期文件（7天前）
            final File[] files = tempScriptDir.listFiles(file -> isExpiredFile(file, SCRIPT_EXPIRE_TIME));
            if (ArrayUtil.isNotEmpty(files)) {
                for (File file : files) {
                    file.delete();
                }
            }
        } else {
            STAT_CACHE_MAP.put(READ_SCRIPT_TMP, currCount);
        }
        return tempScriptDir;
    }

    /**
     * 判断文件是否已过期
     */
    public static boolean isExpiredFile(final File file, final long expireTime) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        // 比较文件修改时间是否早于过期时间点
        return file.lastModified() < (System.currentTimeMillis() - expireTime);
    }

    @Override
    public void download(Integer scriptId, HttpServletResponse response) {
        OutputStream outputStream = null;
        try {
            // 根据ID获取脚本信息
            TbDdsJournalScript script = getById(scriptId);
            if (script == null) {
                throw new ServiceException("脚本不存在");
            }

            // 获取文件内容
            final TbDdsFileContent fileContent = tbDdsFileService.findContentById(script.getFileId());
            if (fileContent == null) {
                throw new ServiceException("脚本文件不存在");
            }

            // 获取文件二进制数据
            final byte[] fileData = fileContent.getFileData();
            if (fileData == null || fileData.length == 0) {
                throw new ServiceException("脚本文件内容为空");
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setContentLength(fileData.length);
            FileUtils.setAttachmentResponseHeader(response, script.getScriptName());

            // 写入响应流
            outputStream = response.getOutputStream();
            outputStream.write(fileData);
            outputStream.flush();
        } catch (Exception e) {
            log.error("下载脚本失败", e);
            /*log.error("下载脚本失败", e);
            try {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("下载失败，原因：" + e.getMessage());
            } catch (IOException ex) {
                log.error("写入下载失败信息异常", ex);
            }*/
            throw new ServiceException("下载失败，原因：" + e.getMessage());
        } finally {
            IoUtil.close(outputStream);
        }
    }

    /**
     * 新增/编辑脚本信息
     */
    @Override
    public AjaxResult saveScript(ScriptDTO dto, Long userId) {
        if (dto == null) {
            return AjaxResult.error("参数不能为空");
        }
        final List<String> scriptType = dto.getScriptType();
        if (scriptType == null || scriptType.isEmpty()) {
            return AjaxResult.error("脚本类型不能为空");
        }

        final List<String> allTypes = ScriptTypeEnum.allCode();
        for (String type : scriptType) {
            if (!allTypes.contains(type)) {
                throw new ServiceException("脚本类型错误");
            }
            if (ScriptTypeEnum.batch.getCode().equals(type)) {
                if (scriptType.size() > 1) {
                    throw new ServiceException("批次类型不能和其它类型同时选择");
                }
            }
        }

        try {
            // 检查脚本名称是否已存在
            TbDdsJournalScript existingByName = tbDdsJournalScriptMapper.findOne(Wrappers.lambdaQuery(TbDdsJournalScript.class)
                    .eq(TbDdsJournalScript::getScriptName, dto.getScriptName())
                    .ne(dto.getId() != null, TbDdsJournalScript::getId, dto.getId()));
            if (existingByName != null) {
                return AjaxResult.error("脚本名称已存在，请更换名称");
            }

            TbDdsJournalScript script;
            boolean isUpdate = dto.getId() != null;

            final Date now = new Date();
            if (isUpdate) {
                // 修改操作，先查询原记录
                script = getById(dto.getId());
                if (script == null) {
                    return AjaxResult.error("脚本不存在");
                }

                // 编辑操作时，不允许修改脚本类型
                if (!new TreeSet<>(scriptType).equals(new TreeSet<>(script.getType()))) {
                    return AjaxResult.error("编辑操作不允许修改脚本类型");
                }
            } else {
                // 新增操作
                script = new TbDdsJournalScript();
                script.setCreator(userId);
                script.setCreateTime(now);
            }
            script.setUpdateTime(now);

            // 处理文件
            Long fileId = null;
            if (StrUtil.isNotBlank(dto.getScriptFileId())) {
                // 使用已有文件，不需要重新上传
                fileId = Long.parseLong(dto.getScriptFileId());
            } else if (StrUtil.isNotBlank(dto.getTempFileName())) {
                // 获取临时文件
                final String tempFileName = dto.getTempFileName();
                // 报错临时文件到数据库
                fileId = saveFileToDb(tempFileName, script, isUpdate);
            } else if (!isUpdate) {
                // 新增时必须上传文件
                throw new ServiceException("请上传脚本文件");
            }

            // 设置文件ID
            if (fileId != null) {
                final boolean exists = tbDdsFileService.existsById(fileId);
                if (!exists) {
                    throw new ServiceException("文件数据不存在");
                }
                script.setFileId(fileId);
            } else {
                throw new ServiceException("文件ID不能为空");
            }

            // 设置其他属性
            script.setScriptName(dto.getScriptName());
            // 编辑操作时保留原有的脚本类型，新增操作时使用传入的脚本类型
            if (!isUpdate) {
                script.setType(scriptType);
            }
            // 简化标志位处理，删除脚本和脚本标签处理逻辑
            // script.setStatus(dto.getStatus());
            script.setStatus(StatusEnums.ENABLE.getCode().toString());
            script.setRemark(dto.getRemark());

            // 保存脚本记录
            boolean success = saveOrUpdate(script);
            if (!success) {
                return AjaxResult.error(isUpdate ? "修改失败" : "添加失败");
            }

            return AjaxResult.success(isUpdate ? "修改成功" : "添加成功", script.getId());
        } catch (Exception e) {
            log.error("保存脚本失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }

    private Long saveFileToDb(final String tempFileName, final TbDdsJournalScript script, final boolean isUpdate) {
        if (!tempFileName.toLowerCase().endsWith(SCRIPT_FILE_SUFFIX)) {
            throw new ServiceException(StrUtil.format("只能上传{}格式的文件", SCRIPT_FILE_SUFFIX));
        }

        final File tempFile = new File(initTempDir(), tempFileName);
        if (!tempFile.exists() || !tempFile.isFile()) {
            throw new ServiceException("临时文件不存在");
        }

        // 计算文件MD5值
        final String md5 = MyHashUtil.md5(tempFile);

        // 检查MD5是否已存在
        if (!isUpdate) {
            // 只有新增时才检查MD5是否重复
            final TbDdsJournalScript existingScript = tbDdsJournalScriptMapper.findOne(Wrappers.lambdaQuery(TbDdsJournalScript.class).eq(TbDdsJournalScript::getScriptMd5, md5));
            if (existingScript != null) {
                throw new ServiceException(StrUtil.format("该脚本文件已存在，脚本名称：{}，MD5值：{}", existingScript.getScriptName(), md5));
            }
        } else {

        }

        // 上传文件到文件系统
        String originalFilename = tempFile.getName();
        if (originalFilename.contains(FILE_NAME_CONNECTOR)) {
            originalFilename = originalFilename.substring(originalFilename.indexOf(FILE_NAME_CONNECTOR) + 1);
        }
        FileUploadDTO uploadDTO = new FileUploadDTO(tempFile, FileTypeEnum.SCRIPT, md5, originalFilename, false);
        final TbDdsFile tbDdsFile = tbDdsFileService.upload(uploadDTO);
        final Long fileId = tbDdsFile.getId();

        // 设置MD5
        script.setScriptMd5(md5);

        // 如果是更新操作且上传了新文件，删除旧文件
        if (isUpdate && script.getFileId() != null && !script.getFileId().equals(fileId)) {
            // 删除旧文件
            tbDdsFileService.delById(script.getFileId());
            log.info("脚本[{}]更新，删除旧文件ID: {}", script.getScriptName(), script.getFileId());
        }

        // 删除临时文件
        if (tempFile.exists()) {
            tempFile.delete();
        }
        return fileId;
    }

    public static Map<String, String> initDictValLabelMap(ISysDictTypeService sysDictTypeService, String type) {
        final List<SysDictData> sysDictData = sysDictTypeService.selectDictDataByType(type);
        final Map<String, String> map = new LinkedHashMap<>();
        for (SysDictData item : sysDictData) {
            map.put(item.getDictValue(), item.getDictLabel());
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteScript(Integer[] scriptIds) {
        if (ArrayUtil.isEmpty(scriptIds)) {
            return AjaxResult.error("脚本ID不能为空");
        }

        final List<TbDdsJournalScript> list = this.listByIds(CollUtil.toList(scriptIds));
        if (CollUtil.isEmpty(list)) {
            return AjaxResult.error("脚本不存在");
        }
        try {
            for (TbDdsJournalScript script : list) {
                Integer scriptId = script.getId();

                // 检查脚本是否被标签使用
                TbDdsScriptlabelScript scriptlabelScript = tbDdsScriptlabelScriptMapper.findOne(
                        Wrappers.lambdaQuery(TbDdsScriptlabelScript.class)
                                .eq(TbDdsScriptlabelScript::getScriptId, scriptId)
                                .select(TbDdsScriptlabelScript::getId));
                if (scriptlabelScript != null) {
                    return AjaxResult.error("脚本[" + script.getScriptName() + "]已被标签使用，无法删除");
                }

                // 检查脚本是否被期刊使用
                Journal journal = journalMapper.findOne(
                        Wrappers.lambdaQuery(Journal.class)
                                .eq(Journal::getScriptId, scriptId)
                                .select(Journal::getId));
                if (journal != null) {
                    return AjaxResult.error("脚本[" + script.getScriptName() + "]已被期刊使用，无法删除");
                }

                // 删除脚本记录
                removeById(scriptId);
                // 删除文件元数据和内容
                tbDdsFileService.delById(script.getFileId());
            }
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除脚本失败", e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

}
