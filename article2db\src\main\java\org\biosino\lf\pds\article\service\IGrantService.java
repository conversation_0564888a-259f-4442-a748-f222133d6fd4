package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Grant;

import java.util.Collection;
import java.util.List;

/**
 * 资助信息表 服务接口
 */
public interface IGrantService extends IService<Grant> {
    /**
     * 根据资助ID列表查询资助信息
     *
     * @param grantIds 资助ID列表
     * @return 资助信息列表
     */
    List<Grant> findByGrantIdIn(Collection<String> grantIds);

    /**
     * 根据ID集合查询资助信息
     *
     * @param ids ID集合
     * @return 资助信息列表
     */
    List<Grant> findByIds(Collection<Long> ids);

    /**
     * 删除未被引用的资助信息
     * 
     * @param ids 待检查的资助信息ID列表
     * @return 是否删除成功
     */
    boolean removeUnusedByIds(Collection<Long> ids);
}
