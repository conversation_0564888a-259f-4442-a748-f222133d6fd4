package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleAuthor;
import org.biosino.lf.pds.article.domain.Author;
import org.biosino.lf.pds.article.domain.Organization;
import org.biosino.lf.pds.article.mapper.ArticleAuthorMapper;
import org.biosino.lf.pds.article.service.IArticleAuthorService;
import org.biosino.lf.pds.article.service.IAuthorService;
import org.biosino.lf.pds.article.service.IOrganizationService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文章作者关联表 服务实现类
 */
@Service
public class ArticleAuthorServiceImpl extends ServiceImpl<ArticleAuthorMapper, ArticleAuthor> implements IArticleAuthorService {

    private final IAuthorService authorService;
    private final IOrganizationService organizationService;

    public ArticleAuthorServiceImpl(@Lazy IAuthorService authorService, IOrganizationService organizationService) {
        this.authorService = authorService;
        this.organizationService = organizationService;
    }

    @Override
    public List<ArticleAuthor> findByDocId(Long docId) {
        List<ArticleAuthor> list = this.list(
                Wrappers.<ArticleAuthor>lambdaQuery()
                        .eq(ArticleAuthor::getDocId, docId)
        );
        // 同时把关联的作者也返回
        List<Long> authorIds = list.stream().map(ArticleAuthor::getAuthorId).collect(Collectors.toList());
        List<Long> organizationIds = list.stream()
                .flatMap(x -> Optional.ofNullable(x.getOrganizationId()).orElseGet(Collections::emptyList).stream())
                .collect(Collectors.toList());

        List<Author> authors = new ArrayList<>();
        List<Organization> organizations = new ArrayList<>();
        if (CollUtil.isNotEmpty(authorIds)) {
            authors = authorService.listByIds(authorIds);
        }
        if (CollUtil.isNotEmpty(organizationIds)) {
            organizations = organizationService.listByIds(organizationIds);
        }

        Map<Long, Author> idAuthorMap = authors.stream().collect(Collectors.toMap(Author::getId, author -> author));

        Map<Long, Organization> idOrganizationMap = organizations.stream().collect(Collectors.toMap(Organization::getId, organization -> organization));

        list.forEach(articleAuthor -> {
            articleAuthor.setAuthor(idAuthorMap.get(articleAuthor.getAuthorId()));
            if (CollUtil.isNotEmpty(articleAuthor.getOrganizationId())) {
                Set<Organization> set = new LinkedHashSet<>();
                for (Long organizationId : articleAuthor.getOrganizationId()) {
                    set.add(idOrganizationMap.get(organizationId));
                }
                articleAuthor.setOrganizations(set);
            }
        });
        return list;
    }

    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(Wrappers.<ArticleAuthor>lambdaQuery()
                .eq(ArticleAuthor::getDocId, docId));
    }

    @Override
    public List<ArticleAuthor> selectByOrgIdIn(Collection<Long> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            return CollUtil.newArrayList();
        }
        return this.baseMapper.selectByOrgIdIn(orgIds);
    }
}
