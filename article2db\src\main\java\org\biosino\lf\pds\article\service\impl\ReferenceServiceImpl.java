package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.Reference;
import org.biosino.lf.pds.article.mapper.ReferenceMapper;
import org.biosino.lf.pds.article.service.IReferenceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 参考文献信息表 服务实现类
 */
@Service
public class ReferenceServiceImpl extends ServiceImpl<ReferenceMapper, Reference> implements IReferenceService {
    @Override
    public List<Reference> findByDocId(Long docId) {
        return this.list(
                Wrappers.<Reference>lambdaQuery()
                        .eq(Reference::getDocId, docId)
        );
    }

    @Override
    public boolean removeByDocId(Long id) {
        return this.remove(Wrappers.<Reference>lambdaQuery()
                .eq(Reference::getDocId, id));
    }
}
