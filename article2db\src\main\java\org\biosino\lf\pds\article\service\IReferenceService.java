package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Reference;

import java.util.List;

/**
 * 参考文献信息表 服务接口
 */
public interface IReferenceService extends IService<Reference> {
    /**
     * 根据文档ID查询参考文献信息
     *
     * @param docId 文档ID
     * @return 参考文献信息集合
     */
    List<Reference> findByDocId(Long docId);

    boolean removeByDocId(Long id);
}
