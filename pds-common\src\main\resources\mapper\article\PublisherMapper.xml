<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.PublisherMapper">

    <resultMap id="PublisherResult" type="org.biosino.lf.pds.article.domain.Publisher">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="ioc" column="ioc"/>
        <result property="alias" column="alias"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="sourceType" column="source_type"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="journalCount" column="journal_count"/>
    </resultMap>

    <sql id="selectSql">
        select p.id,
               p.name,
               p.ioc,
               p.alias,
               p.source_type,
               p.status,
               p.create_time,
               p.update_time,
               (select count(*) from tb_dds_journal j where j.publisher_id = p.id) as journal_count
        from tb_dds_publisher_250715 p
    </sql>

    <!-- 查询出版社列表 -->
    <select id="selectPublisherList" parameterType="org.biosino.lf.pds.article.dto.PublisherQueryDTO"
            resultMap="PublisherResult">
        <include refid="selectSql"/>
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="ioc != null and ioc != ''">
                and ioc like concat('%', #{ioc}, '%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND to_char(update_time,'yyyy-MM-dd')::date &gt;= to_date(#{beginTime},'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND to_char(update_time,'yyyy-MM-dd')::date &lt;= to_date(#{endTime},'yyyy-MM-dd')
            </if>
        </where>
        order by update_time desc
    </select>


</mapper>
