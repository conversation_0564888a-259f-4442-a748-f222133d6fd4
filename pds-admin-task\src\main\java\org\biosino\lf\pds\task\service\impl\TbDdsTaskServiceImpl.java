package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPublishDTO;
import org.biosino.lf.pds.article.custbean.dto.api.ScriptInfoDTO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.*;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.config.AppConfig;
import org.biosino.lf.pds.common.constant.DictTypeConstants;
import org.biosino.lf.pds.common.core.domain.entity.SysDictData;
import org.biosino.lf.pds.common.enums.DirectoryEnum;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.*;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.MyHashUtil;
import org.biosino.lf.pds.common.utils.task.JobIdCreator;
import org.biosino.lf.pds.system.service.ISysDictTypeService;
import org.biosino.lf.pds.task.service.ITbDdsJournalScriptService;
import org.biosino.lf.pds.task.service.ITbDdsTaskService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 文献传递任务服务实现
 * 处理任务创建、分配、状态更新等核心业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsTaskServiceImpl extends ServiceImpl<TbDdsTaskMapper, TbDdsTask> implements ITbDdsTaskService {

    private final static Pattern PMC_PATTERN = Pattern.compile("^PMC([0-9]+)$");

    private final TbDdsTaskMapper tbDdsTaskMapper;
    private final TbDdsTaskLogMapper tbDdsTaskLogMapper;
    private final TbDdsTaskPaperMapper tbDdsTaskPaperMapper;
    private final ArticleMapper articleMapper;
    private final TbDdsFileMapper tbDdsFileMapper;
    private final TbDdsFileContentMapper tbDdsFileContentMapper;
    private final JournalMapper journalMapper;

    private final ISysDictTypeService sysDictTypeService;


    private final TbDdsTaskScheduleMapper tbDdsTaskScheduleMapper;

    private final ITbDdsFileService tbDdsFileService;

    /*@Override
    public AjaxResult uploadIdExcel(MultipartFile file) {
        InputStream inputStream = null;
        ExcelReader reader = null;
        try {
            if (file == null) {
                return AjaxResult.error("请选择要上传的文件");
            }

            inputStream = file.getInputStream();
            // 使用hutool读取Excel文件
            reader = ExcelUtil.getReader(inputStream);
            final List<List<Object>> readAll = reader.read();

            // 处理数据，跳过表头行
            List<List<String>> resultData = new ArrayList<>();
            boolean skipHeader = false;
            final int size = CollUtil.size(readAll);
            if (size > 5000) {
                return AjaxResult.error("上传的Excel文件不能超过5000行");
            }

            for (List<Object> row : readAll) {
                // 检查是否是表头行
                if (!skipHeader && !row.isEmpty() &&
                        (StrUtil.containsIgnoreCase(String.valueOf(row.get(0)), "PMID") ||
                                StrUtil.containsIgnoreCase(String.valueOf(row.get(1)), "PMCID") ||
                                StrUtil.containsIgnoreCase(String.valueOf(row.get(2)), "DOI"))) {
                    skipHeader = true;
                    continue;
                }

                // 处理数据行
                List<String> dataRow = new ArrayList<>();
                for (Object cell : row) {
                    dataRow.add(cell == null ? StrUtil.EMPTY : StrUtil.trimToEmpty(String.valueOf(cell)));
                }
                resultData.add(dataRow);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("data", resultData);

            return AjaxResult.success("Excel文件解析成功", result);
        } catch (Exception e) {
            return AjaxResult.error("Excel文件解析失败: " + e.getMessage());
        } finally {
            IoUtil.close(reader);
            IoUtil.close(inputStream);
        }
        return null;
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TbDdsTask publishTask(TaskPublishDTO dto, Long userId, TaskSourceEnum taskSourceEnum) {
        // 获取顺序pmid > pmcid > doi
        final List<List<String>> literatureData = dto.getLiteratureData();
        final Set<String> idSet = new LinkedHashSet<>();
        for (List<String> item : literatureData) {
            String id = null;

            for (int i = 0; i < item.size(); i++) {
                final String s = StrUtil.trimToNull(item.get(i));
                int length = StrUtil.length(s);
                if (length > 0) {
                    if (length > 200) {
                        throw new ServiceException("太长的行，确定是 doi/pmid？ " + s);
                    }
                    if (i == 1) {
                        // pmcid
                        id = s.startsWith("PMC") ? s : "PMC" + s;
                    } else {
                        id = s;
                    }
                    break;
                }
            }

            for (String s : item) {
                s = StrUtil.trimToNull(s);
                int length = StrUtil.length(s);
                if (length > 0) {
                    if (length > 200) {
                        throw new ServiceException("太长的行，确定是 doi/pmid？ " + s);
                    }
                    id = s;
                    break;
                }
            }

            if (id != null) {
                idSet.add(id);
                if (idSet.size() > 5000) {
                    throw new ServiceException("文献标识符数据不能超过5000条");
                }
            }
        }
        if (CollUtil.isEmpty(idSet)) {
            throw new ServiceException("文献标识符数据不能为空");
        }

        // 校验下载模式
        final List<SysDictData> sysDictData = sysDictTypeService.selectDictDataByType(DictTypeConstants.TASK_DOWNLOAD_MODE);
        boolean isValid = false;
        for (SysDictData dictData : sysDictData) {
            if (dto.getDownloadMode().equals(dictData.getDictValue())) {
                isValid = true;
                break;
            }
        }
        if (!isValid) {
            throw new ServiceException("下载模式值错误");
        }

        // 校验节点类型
        final List<String> scriptTypeDictValues = sysDictTypeService.selectDictDataByType(DictTypeConstants.SCRIPT_TYPE)
                .stream().map(SysDictData::getDictValue).toList();
        final List<String> nodeTypes = dto.getNodeTypes();
        if (!CollUtil.containsAll(scriptTypeDictValues, nodeTypes)) {
            throw new ServiceException("节点类型错误");
        }

        // 校验通过
        final Date now = new Date();
        final String taskId = JobIdCreator.generateCode();
        final Long fileId = initTaskInputFile(taskId, now, idSet);

        // 创建任务
        TbDdsTask task = new TbDdsTask();
        task.setId(taskId);
        // 页面发布时任务名称为空
        // task.setName(generateTaskName());
        task.setDescription(dto.getTaskDesc());
        task.setFileId(fileId);

        task.setPriority(dto.getPriority().shortValue());
        // 设置支持的节点类型
        task.setSupportSiteType(nodeTypes);

        final Integer testFlag = dto.getTestFlag();
        task.setTestFlag(testFlag == null ? 0 : testFlag.shortValue());

        task.setDownloadMode(dto.getDownloadMode());
        task.setRetryInterval(dto.getRetryInterval());
        task.setStatus(TaskStatusEnum.create.name()); // 初始状态：create-待分配
        task.setCreator(userId);
        task.setCreateTime(now);
        task.setSource(taskSourceEnum.name());

        // 保存任务
        tbDdsTaskMapper.insert(task);
        return task;
    }

    /**
     * 分配任务
     * 将任务拆分成子任务并分配给相应的处理节点
     */
    @Override
    public void schedule(String taskId) {
        final TbDdsTask task = this.getById(taskId);
        if (task == null) {
            log.error("未找到任务：{}", taskId);
            return;
        }
        try {
            // 分配中
            appendTaskMessage(tbDdsTaskLogMapper, task.getId(), String.format("开始分配任务 %s ......", task.getId()));
            task.setStatus(TaskStatusEnum.assigning.name());
            this.updateById(task);

            //任务分配
            fillTaskSchedule(task);

            // 分配完了查看是否有需要执行的任务，如果没有则任务本任务已完成
            /*final Set<String> status = TaskPaperStatusEnum.getNotCompleteTaskpaperStatus().stream().map(Enum::name).collect(Collectors.toSet());
            long count = tbDdsTaskPaperMapper.selectCount(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                    .eq(TbDdsTaskPaper::getTaskId, taskId).in(TbDdsTaskPaper::getStatus, status));*/
            if (!hasPaperByTaskIdAndStatus(taskId, TaskPaperStatusEnum.getNotCompleteTaskpaperStatus())) {
                task.setStatus(TaskStatusEnum.complete.name());
            } else {
                task.setStatus(TaskStatusEnum.assigned.name());
            }

            this.updateById(task);
        } catch (Exception e) {
            log.error(e.getMessage(), e);

            task.setStatus(TaskStatusEnum.assign_error.name());

            appendTaskMessage(tbDdsTaskLogMapper, taskId, String.format("任务分配失败。<br>%s", e.toString()));
            throw e;
        }
    }

    @Override
    public boolean hasPaperByTaskIdAndStatus(String taskId, List<TaskPaperStatusEnum> statusList) {
        if (StrUtil.isBlank(taskId) || CollUtil.isEmpty(statusList)) {
            return false;
        }

        final Set<String> status = statusList.stream().map(Enum::name).collect(Collectors.toSet());
        final TbDdsTaskPaper one = tbDdsTaskPaperMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                .eq(TbDdsTaskPaper::getTaskId, taskId)
                .in(TbDdsTaskPaper::getStatus, status)
                .select(TbDdsTaskPaper::getId)
        );
        return one != null;
    }


    @Override
    public TbDdsTaskPaper findTaskPaper(String taskId, Long docId) {
        if (taskId == null || docId == null) {
            return null;
        }
        return tbDdsTaskPaperMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                .eq(TbDdsTaskPaper::getTaskId, taskId)
                .eq(TbDdsTaskPaper::getDocId, docId));
    }

    @Override
    public TbDdsTaskSchedule findTaskSchedule(Long paperId, Integer siteId) {
        if (paperId == null || siteId == null) {
            return null;
        }
        return tbDdsTaskScheduleMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskSchedule.class)
                .eq(TbDdsTaskSchedule::getPaperId, paperId).eq(TbDdsTaskSchedule::getSiteId, siteId));
    }

    /**
     * 处理任务分配的具体实现
     * 从数据库读取任务输入文件内容，逐行处理文献标识符，创建子任务
     *
     * @param task 要分配的任务对象
     */
    private void fillTaskSchedule(TbDdsTask task) {
        final Long fileId = task.getFileId();
        if (fileId == null) {
            log.error("任务输入文件ID为空: {}", task.getId());
            return;
        }

        // 从数据库获取文件内容
        final TbDdsFileContent fileContent = tbDdsFileService.findContentById(fileId);
        if (fileContent == null || fileContent.getFileData() == null) {
            log.error("任务输入文件内容不存在: fileId={}", fileId);
            return;
        }

        // 创建错误文件路径
        final String errorFilePath = "task/" + DateFormatUtils.format(task.getCreateTime(), "yyyyMMdd") + "/" + task.getId() + ".err";
        final File errorFile = new File(AppConfig.getTaskDir(), errorFilePath);
        if (errorFile.exists()) {
            errorFile.delete();
        }

        // 读取文件内容, 已分配数量、库中已存在不再分配数量、ID/DOI在库中未找到数量
        int assignNum = 0, existsNum = 0;

        List<String> notFoundLines = new ArrayList<>();

        // 将字节数组转换为字符串，然后按行分割
        final String fileContentStr = new String(fileContent.getFileData(), java.nio.charset.StandardCharsets.UTF_8);
        final List<String> lines = Arrays.asList(fileContentStr.split("\\r?\\n"));
        for (String line : lines) {
            line = StrUtil.trimToNull(line);
            if (line == null) {
                continue;
            }

            final Article article = getArticleInfo(line);

            if (article == null) {
                notFoundLines.add(line);
                log.info("文献 {} 的题录信息未找到，忽略。", line);
                continue;
            }

            // 相同的doc_id可分配多篇，因为优先级可能不同
            TbDdsTaskPaper taskPaper = findTaskPaper(task.getId(), article.getId());

            // 如果任务已经存在，直接跳过
            if (taskPaper != null) {
                log.warn("文献 {} 已经在该任务 {} 下，不再重复分配，忽略。", article.getPmid(), task.getId());
                continue;
            }

            taskPaper = createTaskPaper(task, article);

            // 文献已经存在，不再进行分配
            // 测试任务不做判断
            if (!TaskPaperStatusEnum.waiting.name().equalsIgnoreCase(taskPaper.getStatus())) {
                existsNum++;
                log.info("文献 {} 全文已存在，不再进行分配。", line);
                continue;
            }

            assignNum++;

            log.info("已分配 {} 篇文献, {}", assignNum, line);
        }

        task.setTotal(notFoundLines.size() + existsNum + assignNum);

        this.updateById(task);

        FileUtil.writeUtf8Lines(notFoundLines, errorFile);

        String errorDownload = "";
        if (errorFile.exists() && errorFile.length() > 0) {
            errorDownload = "<a href='" + AppConfig.getTaskDirPreUrl() + "/" + errorFilePath + "' target=\"_blank\" >[下载]</a>";
        }

        appendTaskMessage(
                tbDdsTaskLogMapper,
                task.getId(),
                String.format("任务分配完成，已分配 %d 篇，全文在库中已存在 %d 篇，库中没有找到 %d 篇&nbsp;&nbsp;%s", assignNum, existsNum, notFoundLines.size(), errorDownload)
        );
    }


    /**
     * 创建任务文献对象
     * 检查文献全文是否已存在，设置适当的状态
     *
     * @param task    任务对象
     * @param article 文献对象
     * @return 创建的任务文献对象
     */
    private TbDdsTaskPaper createTaskPaper(final TbDdsTask task, final Article article) {
        // 判断 PDF 是否已经存在了
        final List<TbDdsFile> pdfs = tbDdsFileMapper.selectList(Wrappers.lambdaQuery(TbDdsFile.class)
                .eq(TbDdsFile::getDocId, article.getId())
                .eq(TbDdsFile::getType, FileTypeEnum.PDF.name()).orderByDesc(TbDdsFile::getCreateTime));

        boolean fulltextExist = false;
        if (CollUtil.isNotEmpty(pdfs)) {
            for (TbDdsFile pdf : pdfs) {
                if (fileExist(tbDdsFileContentMapper, pdf.getId())) {
                    fulltextExist = true;
                    break;
                }
            }
        }

//        final String doi = articleOtherIdMapper.getArticleDoiByDocId(article.getId());
        final String doi = article.getDoi();

        Date curDate = new Date();

        final TbDdsTaskPaper paper = new TbDdsTaskPaper();
        paper.setTaskId(task.getId());
        paper.setDocId(article.getId());
        paper.setDoi(doi);
        paper.setStatus(fulltextExist && task.getTestFlag() == 0 ? TaskPaperStatusEnum.success_exist.name() : TaskPaperStatusEnum.waiting.name());
        paper.setCreateTime(curDate);
        paper.setUpdateTime(curDate);
        paper.setCreator(task.getCreator());
        final Long journalId = article.getJournalId();
        if (journalId != null) {
            final Journal journal = journalMapper.selectById(journalId);
            if (journal != null) {
                paper.setJournalId(journalId);
                paper.setPublisherId(journal.getPublisherId());
            } else {
                log.error("期刊ID错误： {}", journalId);
            }
        }

        tbDdsTaskPaperMapper.insert(paper);
        return paper;
    }

    /**
     * 根据提供的标识符获取文献信息
     * 支持PMC、PMID和DOI三种标识符格式
     *
     * @param line 文献标识符
     * @return 文献对象，未找到则返回null
     */
    private Article getArticleInfo(final String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }

        // 先判断 PMC?
        final Matcher matcher = PMC_PATTERN.matcher(line.toUpperCase());

        Article article = null;
        if (matcher.matches()) {
            final String group = matcher.group(1);
            long pmcId = Long.parseLong(group);
            final LambdaQueryWrapper<Article> lambdaQuery = Wrappers.lambdaQuery(Article.class)
                    .eq(Article::getPmcId, pmcId)
                    .orderByAsc(Article::getPmid);
            article = articleMapper.findOne(lambdaQuery);
        }

        if (article != null) {
            return article;
        }

        // 不是 PMC？
        try {
            // PMID
            Long pmid = Long.parseLong(line);
            final LambdaQueryWrapper<Article> lambdaQuery = Wrappers.lambdaQuery(Article.class)
                    .eq(Article::getPmid, pmid);
            article = articleMapper.findOne(lambdaQuery);
        } catch (NumberFormatException e) {
            // 不是数字类型作为doi处理
            /*final Long docId = articleOtherIdMapper.getArticleIdByDoi(line);
            article = articleMapper.selectById(docId);*/

            final LambdaQueryWrapper<Article> lambdaQuery = Wrappers.lambdaQuery(Article.class)
                    .eq(Article::getDoi, line);
            article = articleMapper.findOne(lambdaQuery);
        }

        return article;
    }


    /**
     * 检查文件是否存在于文件内容表中
     */
    private static boolean fileExist(TbDdsFileContentMapper mapper, Long fileId) {
        final LambdaQueryWrapper<TbDdsFileContent> lambdaQuery = Wrappers.lambdaQuery(TbDdsFileContent.class)
                .eq(TbDdsFileContent::getId, fileId)
                .select(TbDdsFileContent::getId);
        return mapper.findOne(lambdaQuery) != null;
    }

    /**
     * 初始化任务输入文件
     * 创建存储任务标识符的文件，上传到数据库并删除磁盘文件
     *
     * @param taskId 任务ID
     * @param now    创建时间
     * @param idSet  文献标识符集合
     * @return 文件ID
     */
    private Long initTaskInputFile(final String taskId, final Date now, final Set<String> idSet) {
        // 创建临时文件
        final String tempDir = DirectoryEnum.temp.name();
        final File dir = new File(taskInputDir(), tempDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        final File tempFile = new File(dir, taskId + ".in");
        if (tempFile.exists()) {
            throw new ServiceException("生成文献标识符数据失败");
        }

        try {
            // 写入临时文件
            FileUtil.writeUtf8Lines(idSet, tempFile);

            // 计算文件MD5
            final String fileMd5 = MyHashUtil.md5(tempFile);

            // 上传文件到数据库
            FileUploadDTO uploadDTO = new FileUploadDTO(
                    tempFile,
                    FileTypeEnum.TASK,
                    fileMd5,
                    taskId + ".in",
                    false
            );
            final TbDdsFile uploadedFile = tbDdsFileService.upload(uploadDTO);

            // 删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }

            return uploadedFile.getId();
        } catch (Exception e) {
            // 确保临时文件被删除
            if (tempFile.exists()) {
                tempFile.delete();
            }
            throw new ServiceException("上传任务输入文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务输入文件目录
     */
    private File taskInputDir() {
        return AppConfig.initDataHome(null);
    }

    /**
     * 生成任务编号
     * 格式：PDS + 年月日
     */
    private String generateTaskName() {
        return "PDS_" + DateFormatUtils.format(new Date(), "yyyyMMdd");
    }




}
