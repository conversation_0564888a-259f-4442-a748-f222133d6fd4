package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章补充MeSH关联表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_article_supplmesh", autoResultMap = true)
public class ArticleSupplMesh {
    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 补充MeSH ID
     */
    @TableField("supplmesh_id")
    private String supplmeshId;

    @TableField("type")
    private String type;

    @TableField("name")
    private String name;

}
