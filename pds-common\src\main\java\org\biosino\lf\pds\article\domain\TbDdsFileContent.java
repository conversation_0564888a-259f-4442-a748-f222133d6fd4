package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.ibatis.type.ByteArrayTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;

/**
 * <p>
 * 文件内容表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName(value = "tb_dds_file_content", autoResultMap = true)
public class TbDdsFileContent implements Serializable {
    /**
     * 主键ID，来自于tb_dds_file表的id字段，与tb_dds_file表一一对应
     */
    @TableId(value = "id", type = IdType.INPUT)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 文件二进制数据
     */
    @TableField(value = "file_data", typeHandler = ByteArrayTypeHandler.class, jdbcType = JdbcType.VARBINARY)
    private byte[] fileData;

}
