package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Organization;

import java.util.Collection;
import java.util.List;

/**
 * 机构信息表 服务接口
 */
public interface IOrganizationService extends IService<Organization> {
    /**
     * 根据机构ID列表查询机构信息
     *
     * @param ids 机构ID列表
     * @return 机构信息列表
     */
    List<Organization> findByIds(Collection<Long> ids);
    
    /**
     * 删除未被引用的机构
     * 
     * @param ids 待检查的机构ID列表
     * @return 是否删除成功
     */
    boolean removeUnusedByIds(Collection<Long> ids);
}
