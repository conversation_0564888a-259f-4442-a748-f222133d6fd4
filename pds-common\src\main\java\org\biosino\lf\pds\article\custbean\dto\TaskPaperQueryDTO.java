package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;
import org.biosino.lf.pds.common.enums.task.ScriptTypeEnum;

/**
 * 任务查询DTO
 *
 * <AUTHOR>
 */
@Data
public class TaskPaperQueryDTO {
    private int siteId;
    private int scriptlabelId;
    private String siteType;
    private String taskStatus;
    private String paperStatus;
    private String scheduleStatus;
    private String downloadMode;

    private Long startPmid;
    private Long endPmid;

    private final String typeOfBatch = ScriptTypeEnum.batch.getCode();
}
