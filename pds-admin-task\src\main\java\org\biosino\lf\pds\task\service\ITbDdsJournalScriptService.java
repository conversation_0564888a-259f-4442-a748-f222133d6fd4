package org.biosino.lf.pds.task.service;

import jakarta.servlet.http.HttpServletResponse;
import org.biosino.lf.pds.article.custbean.dto.ScriptDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.domain.TbDdsJournalScript;
import org.biosino.lf.pds.article.service.CommonService;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 脚本管理
 *
 * <AUTHOR>
 */
public interface ITbDdsJournalScriptService extends CommonService<TbDdsJournalScript> {

    /**
     * 查询脚本列表
     *
     * @param queryDto 查询条件
     * @return 脚本列表
     */
    List<ScriptVO> selectScriptList(ScriptDTO queryDto);

    /**
     * 上传脚本文件
     *
     * @param file 脚本文件
     * @return 上传结果
     */
    AjaxResult upload(MultipartFile file);

    /**
     * 下载脚本文件
     *
     * @param scriptId 脚本ID
     * @param response HTTP响应
     */
    void download(Integer scriptId, HttpServletResponse response);

    /**
     * 新增/编辑脚本信息
     */
    AjaxResult saveScript(ScriptDTO dto, Long userId);

    /**
     * 删除脚本
     *
     * @param scriptId 脚本ID
     * @return 操作结果
     */
    AjaxResult deleteScript(Integer[] scriptId);
}
