package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 脚本类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ScriptTypeEnum {
    batch("批次", "1"),
    journal("源刊", "2"),
    school("高校", "3");

    private final String text;
    private final String code;

    ScriptTypeEnum(String text, String code) {
        this.text = text;
        this.code = code;
    }

    public static List<String> allCode() {
        List<String> list = new ArrayList<>();
        ScriptTypeEnum[] values = values();
        for (ScriptTypeEnum value : values) {
            list.add(value.getCode());
        }
        return list;
    }
}
