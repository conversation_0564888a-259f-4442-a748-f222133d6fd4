package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_task_log")
public class TbDdsTaskLog implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String taskId;

    private String message;

    private Date createTime;
}
