package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 节点表
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_site")
public class TbDdsSite implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String siteName;

    private String siteAbbr;

    /**
     * 批次1、源刊2、高校3(单选)
     * 字典类型:script_type
     */
    private String siteType;

    private String status;

    private String siteGroup;

    private Integer obtainTaskInterval;

    private String unit;

    private String address;

    /**
     * API令牌，用于验证请求合法性
     */
    private String apiToken;

    /**
     * 脚本标签（tb_dds_scriptlabel）id
     */
    @TableField(value = "scriptlabel_id")
    private Integer scriptlabelId;

    private Long creator;

    private Date createTime;

    private Date updateTime;

    private Integer taskThreadNum;
}
