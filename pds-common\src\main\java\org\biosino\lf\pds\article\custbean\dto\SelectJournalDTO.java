package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 查询期刊DTO
 *
 * <AUTHOR>
 */
@Data
public class SelectJournalDTO extends BaseEntity {
    /**
     * 出版社ID列表
     */
    private List<Long> publisher;

    /**
     * 期刊名称
     */
    private String journalName;

    /**
     * ISSN Print
     */
    private String issnPrint;

    /**
     * ISSN Electronic
     */
    private String issnElectronic;

    /**
     * 是否有脚本 yes-有 no-无
     */
    private String hasScript;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本类型
     */
    private String scriptType;

    /**
     * 脚本标签ID
     */
    private Integer labelId;

    /**
     * 当前是否为已分配选项卡
     */
//    private Boolean assignedFlag;

   /* public void setPublisher(List<Long> publisher) {
        this.publisher = emptyListToNull(publisher);
    }*/
}
