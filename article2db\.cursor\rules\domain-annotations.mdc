---
description: 
globs: 
alwaysApply: true
---
# 领域模型注解指南

本项目的领域模型类使用多种注解来简化开发和定义数据库映射关系。

## 常用注解

### Lombok注解

项目广泛使用Lombok注解减少样板代码：

```java
@Data                       // 自动生成getter、setter、equals、hashCode和toString方法
@Builder                    // 提供构建器模式
@NoArgsConstructor          // 生成无参构造函数
@AllArgsConstructor         // 生成全参数构造函数
@RequiredArgsConstructor    // 生成包含final或@NonNull字段的构造函数
@Slf4j                      // 添加日志字段
```

### MyBatis-Plus注解

用于定义表和字段映射关系：

```java
@TableName("article")       // 指定表名
@TableId(value = "id", type = IdType.INPUT)  // 指定主键及生成策略
@TableField("doc_id")       // 指定字段名
@TableField(exist = false)  // 标记非数据库字段
```

## 领域模型示例

以下是一个完整的领域模型类示例：

```java
package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 文章实体类
 */
@Data
@TableName("article")
public class Article implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * PubMed ID
     */
    @TableField("pmid")
    private Long pmid;

    /**
     * PMC ID
     */
    @TableField("pmc_id")
    private Long pmcId;

    /**
     * 文章标题
     */
    @TableField("title")
    private String title;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private Date updateDate;

    /**
     * 非数据库字段，用于临时存储
     */
    @TableField(exist = false)
    private List<String> keywords;
}
```

## 关联模型注解

关联模型类通常使用以下注解模式：

```java
@Data
@TableName("article_author")
public class ArticleAuthor implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @TableField("doc_id")
    private Long docId;

    @TableField("author_id")
    private Long authorId;

    @TableField("author_order")
    private Integer authorOrder;

    @TableField("author_correspond")
    private String authorCorrespond;

    @TableField("organization_id")
    private Long organizationId;

    // 非数据库字段，用于关联查询
    @TableField(exist = false)
    private Author author;

    @TableField(exist = false)
    private Set<Organization> organizations;
}
```

## DTO类注解

数据传输对象（DTO）类通常只使用Lombok注解：

```java
@Data
public class ArticleInfoDTO {

    private Article article;

    private Set<ArticleDatabank> articleDatabanks;

    private Set<PubType> pubTypes;

    // 其他关联数据
}
```

## 注解最佳实践

1. 对所有领域模型类使用`@Data`注解简化getter/setter
2. 所有表实体类使用`@TableName`指定表名
3. 使用`@TableId`明确指定主键及生成策略
4. 使用`@TableField`映射字段名，特别是当Java命名和数据库命名不同时
5. 使用`@TableField(exist = false)`标记非数据库字段
6. 实现`Serializable`接口并定义`serialVersionUID`

