<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsTaskPaperMapper">

    <select id="getNextExecuteTaskPaperId" resultType="java.lang.Long"
            parameterType="org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO">
        select p.id from tb_dds_task_paper p
        inner join tb_dds_task t on (p.task_id = t.id and t.status = #{taskStatus})
        where p.status = #{paperStatus}
        and p.id not in (
        select s.paper_id from tb_dds_task_schedule s where s.paper_id = p.id and s.site_id = #{siteId}
        )
        <if test="startPmid != null and endPmid != null">
            and p.doc_id IN (SELECT art.id FROM tb_dds_article art
            WHERE art.pmid BETWEEN #{startPmid} AND #{endPmid})
        </if>

        <choose>
            <when test="siteType eq typeOfBatch">
                <!--批次节点-->
                and t.test_flag = 0
            </when>
            <otherwise>
                <!--源刊、高校节点-->
                and p.journal_id in (
                select j.journal_id from tb_dds_scriptlabel_journal j where j.scriptlabell_id = #{scriptlabelId}
                )
            </otherwise>
        </choose>
        order by t.priority desc, p.create_time asc, p.id asc
        limit 1
    </select>

    <select id="getNextExecuteTaskPaperIdByScriptlableId" resultType="java.lang.Long"
            parameterType="org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO">
        select p.id
        from tb_dds_task_paper p
        inner join tb_dds_task t on (t.download_mode = #{downloadMode} and p.task_id = t.id and t.status =
        #{taskStatus})
        inner join tb_dds_task_schedule ts on (p.id = ts.paper_id and ts.status = #{scheduleStatus})
        where p.status = #{paperStatus}
        and ts.site_id in (select id from tb_dds_site where scriptlabel_id = #{scriptlabelId} and id != #{siteId})

        <choose>
            <when test="siteType eq typeOfBatch">
                <!--批次节点-->
                and t.test_flag = 0
            </when>
            <otherwise>
                <!--源刊、高校节点-->
                and p.journal_id in (
                select j.journal_id from tb_dds_scriptlabel_journal j where j.scriptlabell_id = #{scriptlabelId}
                )
            </otherwise>
        </choose>
        order by t.priority desc, p.create_time asc, p.id asc
        LIMIT 1
    </select>

    <update id="updateNotCompleteTaskPagerStatusByDocId">
        update tb_dds_task_paper set status=#{changeToStatus}, update_time=now()
        <if test="completeFlag">
            ,time_complete=now()
        </if>
        where doc_id = #{docId} and status IN
        <foreach collection="notCompleteStatus" item="statusVal" open="(" separator="," close=")">
            #{statusVal}
        </foreach>
    </update>

</mapper>