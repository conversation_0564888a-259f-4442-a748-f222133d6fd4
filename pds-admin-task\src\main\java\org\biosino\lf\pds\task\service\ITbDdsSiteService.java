package org.biosino.lf.pds.task.service;

import org.biosino.lf.pds.article.custbean.dto.SiteDTO;
import org.biosino.lf.pds.article.custbean.dto.SiteStatusDTO;
import org.biosino.lf.pds.article.custbean.vo.SiteVO;
import org.biosino.lf.pds.article.domain.TbDdsSite;
import org.biosino.lf.pds.article.service.CommonService;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.enums.StatusEnums;

import java.util.List;

/**
 * 节点管理服务层
 *
 * <AUTHOR>
 */
public interface ITbDdsSiteService extends CommonService<TbDdsSite> {

    /**
     * 查询节点列表
     *
     * @param siteDTO 查询条件
     * @return 节点列表
     */
    List<SiteVO> selectSiteList(SiteDTO siteDTO);

    /**
     * 根据ID获取节点详情
     *
     * @param id 节点ID
     * @return 节点详情
     */
    SiteVO getSiteById(Integer id);

    /**
     * 保存节点（新增或修改）
     *
     * @param siteDTO 节点信息
     * @param userId  用户ID
     * @return 操作结果
     */
    AjaxResult saveSite(SiteDTO siteDTO, Long userId);

    /**
     * 删除节点
     *
     * @param id 需要删除的节点ID
     * @return 操作结果
     */
    AjaxResult deleteSiteById(Integer id);

    /**
     * 修改节点状态
     *
     * @param statusDTO 状态信息
     * @return 操作结果
     */
    AjaxResult changeStatus(SiteStatusDTO statusDTO);

    /**
     * 查询所有节点分组
     *
     * @return 分组列表
     */
    List<String> selectAllGroups();

    /**
     * 使用缓存获取站点信息
     *
     * @param siteId 站点ID
     * @return 站点信息
     */
    TbDdsSite findByIdWithCache(Integer siteId);

    /**
     * 查询可重试的节点ID
     */
    List<Integer> findReTrySiteIds(Long paperId, List<String> siteTypes, Integer scriptlabelId, Integer currSiteId);


}
