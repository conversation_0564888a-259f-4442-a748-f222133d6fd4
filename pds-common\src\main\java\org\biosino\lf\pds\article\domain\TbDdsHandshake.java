package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName(value = "tb_dds_handshake", autoResultMap = true)
public class TbDdsHandshake implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer siteId;

    private String siteIp;

    private String message;

    private String sysInfo;

    private Date signalDate;
}
