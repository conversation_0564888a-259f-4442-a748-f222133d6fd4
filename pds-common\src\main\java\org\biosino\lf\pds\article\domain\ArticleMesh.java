package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章MeSH关联表
 */
@Data
@TableName(value = "tb_dds_article_mesh", autoResultMap = true)
public class ArticleMesh {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 主题词ID
     */
    @TableField("descriptor_id")
    private String descriptorId;

    /**
     * 限定词1
     */
    @TableField("qualifier_id1")
    private String qualifierId1;

    /**
     * 限定词2
     */
    @TableField("qualifier_id2")
    private String qualifierId2;

    /**
     * 限定词3
     */
    @TableField("qualifier_id3")
    private String qualifierId3;
}
