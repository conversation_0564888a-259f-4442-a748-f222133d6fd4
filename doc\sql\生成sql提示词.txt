你是一名擅长springboot、mybatis-plus、postgresql的技术专家，我现在想让你跟我sql描述生成mybatis-plus的实体类，
以下是注意事项
1. 提供的建表sql不是一定正确的sql语法，需要你来理解sql的含义
2. 生成的mybatis plus实体类的表名需要与建表sql的一致，类名以表名tb_dds_后面的为主，实体类不用生成getter/setter方法使用lombok @Data注解就行了
3. 关于我提供的sql中，id和数值类型一律采用Long，array类型使用List<String> 然后字段上的的@TableField注解需要指定typeHandler = JacksonTypeHandler.class，json类型使用List<Object> , 然后字段上的的@TableField注解需要指定typeHandler = JacksonTypeHandler.class
4. 每个实体类的字段需要符合驼峰的命名规范，上面需要@TableField，column需要符合下划线的命名规范
