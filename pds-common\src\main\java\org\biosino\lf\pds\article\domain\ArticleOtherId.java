package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章其他ID关联表
 */
@Data
@TableName(value = "tb_dds_article_otherid", autoResultMap = true)
public class ArticleOtherId {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * PubMed ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 其他ID（如DOI/pmc id/publisher-id）
     */
    @TableField("other_id")
    private String otherId;

    /**
     * 来源
     */
    @TableField("source")
    private String source;
}
