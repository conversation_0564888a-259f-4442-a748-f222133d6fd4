package org.biosino.lf.pds.article.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 出版社合并参数
 */
@Data
public class PublisherMergeDTO {

    /**
     * 目标出版社ID（保留的出版社）
     */
    @NotNull
    private Long targetId;

    /**
     * 源出版社ID列表（要合并的出版社）
     */
    @NotNull
    @NotEmpty
    private List<Long> sourceIds;

    /**
     * 合并后的出版社名称
     */
    @NotBlank
    private String name;

    /**
     * 合并后的出版社地址
     */
    private String ioc;

    /**
     * 合并后的别名
     */
    private List<String> alias;
}
