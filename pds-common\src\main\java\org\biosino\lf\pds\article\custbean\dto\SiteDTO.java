package org.biosino.lf.pds.article.custbean.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;

/**
 * 节点DTO
 *
 * <AUTHOR>
 */
@Data
public class SiteDTO extends BaseEntity {
    /**
     * 节点ID
     */
    private Integer id;

    /**
     * 节点名称
     */
    @NotBlank(message = "节点名称不能为空")
    private String siteName;

    /**
     * 节点简写
     */
    private String siteAbbr;

    /**
     * 节点类型（批次1、源刊2、高校3）
     */
    @NotBlank(message = "节点类型不能为空")
    private String siteType;

    /**
     * 节点状态（0-正常，1-停用）
     */
    private String status;

    /**
     * 节点分组
     */
    private String siteGroup;

    /**
     * 获取任务间隔时间（秒）
     */
    @NotNull(message = "获取任务间隔时间不能为空")
    @Min(value = 1, message = "获取任务间隔时间不能小于1")
    @Max(value = 180, message = "获取任务间隔时间不能大于180")
    private Integer obtainTaskInterval;

    /**
     * 单位
     */
    private String unit;

    /**
     * 地址
     */
    private String address;

    /**
     * API令牌，用于验证请求合法性
     */
//    private String apiToken;

    /**
     * 脚本标签ID
     */
    @NotNull(message = "脚本标签不能为空")
    private Integer scriptlabelId;

    /**
     * 线程数量
     */
    private Integer taskThreadNum = 10;
} 