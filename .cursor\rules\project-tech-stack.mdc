---
description:
globs:
alwaysApply: true
---
# 项目技术栈

本项目是一个文献传递系统(PLOSP & PDS)，主要技术栈如下：

## 后端技术

- **核心框架**：Spring Boot 3.3.12
- **ORM框架**：MyBatis-Plus 3.5.12
- **数据库**：MySQL 8.2.0
- **工具库**：Hutool 5.8.37
- **搜索引擎**：Elasticsearch 8.13.4，Easy-ES 3.0.0
- **对象映射**：MapStruct 1.5.5
- **定时任务**：Quartz
- **安全框架**：Spring Security
- **接口文档**：SpringDoc OpenAPI 2.6.0
- **Excel处理**：Apache POI
- **序列化工具**：FastJSON 2
- **验证框架**：Jakarta Validation API

## 前端技术

- **核心框架**：Vue 3
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **构建工具**：Vite
- **组件库**：SplitPanes等

## 项目结构

本项目采用多模块架构，主要模块包括：

- pds-admin：管理后台模块
- pds-framework：框架核心模块
- pds-system：系统功能模块
- pds-quartz：定时任务模块
- pds-generator：代码生成模块
- pds-common：公共模块
- plosp-admin：PLOSP管理模块
- article2db：文章数据模块

