package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleXML;
import org.biosino.lf.pds.article.mapper.ArticleXmlMapper;
import org.biosino.lf.pds.article.service.IArticleXmlService;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 文件真实数据 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ArticleXmlServiceImpl extends ServiceImpl<ArticleXmlMapper, ArticleXML> implements IArticleXmlService {

    @Override
    public String getContentById(Long id) {
        ArticleXML articleXML = getById(id);
        if (articleXML != null && articleXML.getFileData() != null) {
            return new String(articleXML.getFileData(), StandardCharsets.UTF_8);
        }
        return null;
    }
}
