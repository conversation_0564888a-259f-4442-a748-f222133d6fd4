---
description:
globs:
alwaysApply: true
---
# 后端代码风格

本项目后端代码遵循以下编码规范和最佳实践：

## 整体架构

项目采用传统的分层架构：
- Controller层：负责处理HTTP请求，参数校验
- Service层：定义业务逻辑接口
- ServiceImpl层：实现业务逻辑
- Mapper层：数据访问层
- Domain层：实体类定义

## 依赖注入规范

本项目统一使用Lombok的`@RequiredArgsConstructor`进行依赖注入，配合final关键字声明所需依赖：

```java
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements IUserService {
    // 使用final标记，由@RequiredArgsConstructor自动注入
    private final UserMapper userMapper;
    private final RoleService roleService;

    // 业务方法...
}
```

这种方式相比`@Autowired`的优势：
1. 代码更简洁，不需要显式编写构造函数
2. 强制依赖不可变，更符合设计原则
3. 便于单元测试，依赖关系更明确
4. 避免循环依赖问题（通过@Lazy注解处理特殊情况）

## Controller层规范

1. 继承`BaseController`获取通用功能
2. 使用`@RestController`注解标记控制器
3. 使用`@RequestMapping`定义请求路径
4. 使用`@Log`注解记录操作日志
5. 返回统一的`AjaxResult`或`TableDataInfo`对象
6. 方法命名规范：
   - list：查询列表
   - export：导出数据
   - getInfo：获取详情
   - add：新增数据
   - edit：修改数据
   - remove：删除数据

示例:
```java
@RestController
@RequestMapping("/monitor/job")
@RequiredArgsConstructor
public class SysJobController extends BaseController
{
    private final ISysJobService jobService;

    @GetMapping("/list")
    public TableDataInfo list(SysJob sysJob)
    {
        startPage();
        List<SysJob> list = jobService.selectJobList(sysJob);
        return getDataTable(list);
    }
}
```

## Service层规范

本项目基于MyBatis-Plus框架，Service层采用以下模式：

```java
@Service
@RequiredArgsConstructor
public class ArticleAuthorServiceImpl extends ServiceImpl<ArticleAuthorMapper, ArticleAuthor> implements IArticleAuthorService {

    // 如果需要处理循环依赖，使用@Lazy注解
    @Lazy
    private final IAuthorService authorService;
    private final IOrganizationService organizationService;

    @Override
    public List<ArticleAuthor> selectByOrgIdIn(Collection<Long> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            return CollUtil.newArrayList();
        }
        return this.baseMapper.selectByOrgIdIn(orgIds);
    }
}
```

主要特点：
1. 接口以`I`为前缀，如`IArticleAuthorService`
2. 实现类继承自`ServiceImpl<M, T>`获取基本CRUD操作能力
3. 使用`@Service`注解标记服务类
4. 使用`@RequiredArgsConstructor`注入依赖，依赖字段使用final修饰
5. 使用`@Transactional`进行事务管理
6. 方法命名规范：
   - select*/find*：查询操作
   - insert*/save*：新增操作
   - update*：修改操作
   - delete*/remove*：删除操作
   - check*：校验操作

## 实体类规范

1. 使用Lombok注解简化代码：
   - `@Data`：自动生成getter/setter/equals/hashCode/toString
   - `@Builder`：构建器模式
   - `@NoArgsConstructor`：无参构造器
   - `@AllArgsConstructor`：全参构造器

2. 使用MyBatis-Plus注解：
   - `@TableName`：指定表名
   - `@TableId`：指定主键
   - `@TableField`：指定字段名
   - `@TableField(exist = false)`：非数据库字段

3. 使用数据校验注解：
   - `@NotBlank`：非空字符串
   - `@Size`：长度限制
   - `@NotNull`：非空对象

示例：
```java
@Data
@TableName(value = "tb_dds_article_author", autoResultMap = true)
public class ArticleAuthor {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 机构ID
     */
    @TableField(value = "organization_id", typeHandler = LongListArrayTypeHandler.class)
    private List<Long> organizationId;

    @TableField(exist = false)
    private Author author;
}
```

## Mapper层规范

1. 继承MyBatis-Plus的`BaseMapper`
2. 使用`@Mapper`注解标记Mapper接口
3. 自定义查询方法使用`@Param`注解参数

示例：
```java
@Mapper
public interface ArticleAuthorMapper extends BaseMapper<ArticleAuthor> {
    List<ArticleAuthor> selectByOrgIdIn(@Param("orgIds") Collection<Long> orgIds);
    List<ArticleAuthor> findByDocId(@Param("docId") Long docId);
}
```

## XML映射文件规范

1. 使用resultMap定义结果映射
2. 使用sql片段重用SQL语句
3. 复杂查询参数使用foreach标签处理集合

示例：
```xml
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleAuthorMapper">
    <resultMap id="ArticleAuthorResult" type="org.biosino.lf.pds.article.domain.ArticleAuthor">
        <id property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="organizationId" column="organization_id"
                typeHandler="org.biosino.lf.pds.article.config.LongListArrayTypeHandler"/>
    </resultMap>

    <sql id="selectSql">
        select a.id, a.doc_id, a.author_id, a.organization_id from tb_dds_article_author a
    </sql>

    <select id="selectByOrgIdIn" resultMap="ArticleAuthorResult">
        <include refid="selectSql"/>
        where a.organization_id &amp;&amp;
        <foreach item="id" index="index" collection="orgIds" open="ARRAY[" separator="," close="]">
            #{id}
        </foreach>
    </select>
</mapper>
```

## 工具类使用

1. 优先使用Hutool工具类
2. 使用`CollUtil`处理集合操作
3. 使用`StrUtil`处理字符串操作
4. 注重空值检查和边界条件处理








