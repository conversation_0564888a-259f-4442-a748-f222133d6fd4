package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 存放文件metadata元数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_file")
public class TbDdsFile implements Serializable {

    /**
     * 雪花算法id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 原始文件名称
     */
    private String fileName;

    /**
     * 文件名后缀(包含.)
     */
    private String contentType;

    /**
     * 大小
     */
    private Long fileSize;

    /**
     * 文件md5值
     */
    private String md5;

    /**
     * 相对路径(pdf放在磁盘中，才填充此字段, 其它文件以二进制形式存入tb_dds_file_content表)
     */
    private String filePath;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 文件类型（XML、IMG、JSON、SCRIPT、PDF等）
     *
     * @see org.biosino.lf.pds.common.enums.task.FileTypeEnum
     */
    private String type;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 来源，通过pds下载的为site_download。其它途径还包括pmc("由后台程序从pmc获得"),pubmed("由后台程序从pubmed获得"),sci_hub("由后台程序从sci-hub获得"),success_man("后台用户上传"), success_plosp("PLOSP用户上传"), site_download("由站点下载")等;
     */
    private String source;

    @TableField(value = "create_site_id")
    private Integer createSiteId;
}
