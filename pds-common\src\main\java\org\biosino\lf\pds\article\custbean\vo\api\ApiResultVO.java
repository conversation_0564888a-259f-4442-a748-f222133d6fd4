package org.biosino.lf.pds.article.custbean.vo.api;

import lombok.Data;

import java.io.Serializable;

/**
 * api请求结果VO
 *
 * <AUTHOR>
 */
@Data
public class ApiResultVO implements Serializable {
    private String status;
    private String msg;
    private Object data;

    public ApiResultVO() {
    }

    public ApiResultVO(String status, String msg) {
        this(status, msg, null);
    }

    public ApiResultVO(String status, String msg, Object data) {
        this.status = status;
        this.msg = msg;
        this.data = data;
    }

    public static ApiResultVO error(String msg) {
        return new ApiResultVO("error", msg);
    }

    public static ApiResultVO success(String msg) {
        return new ApiResultVO("success", msg);
    }
}
