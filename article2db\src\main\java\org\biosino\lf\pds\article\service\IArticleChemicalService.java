package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleChemical;

import java.util.List;

/**
 * 文章化学物质关联表 服务接口
 */
public interface IArticleChemicalService extends IService<ArticleChemical> {
    /**
     * 根据文档ID查询文章化学物质关联信息
     *
     * @param docId 文档ID
     * @return 文章化学物质关联信息列表
     */
    List<ArticleChemical> findByDocId(Long docId);
    
    /**
     * 根据文档ID删除文章化学物质关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);
}
