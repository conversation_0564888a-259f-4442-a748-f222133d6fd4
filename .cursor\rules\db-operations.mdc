---
description:
globs:
alwaysApply: true
---
# 数据库操作指南

本项目使用MyBatis-Plus作为ORM框架，以下是数据库操作的最佳实践：

## 基础CRUD操作

利用MyBatis-Plus提供的基础CRUD方法简化开发：

```java
// 插入单条记录
boolean success = userService.save(user);

// 批量插入
boolean success = userService.saveBatch(userList);

// 根据ID查询
User user = userService.getById(1L);

// 条件查询
List<User> users = userService.list(
    Wrappers.<User>lambdaQuery()
        .eq(User::getStatus, "0")
        .like(User::getUsername, "admin")
);

// 分页查询
Page<User> page = new Page<>(1, 10);
IPage<User> userPage = userService.page(page,
    Wrappers.<User>lambdaQuery().eq(User::getStatus, "0")
);

// 更新
boolean success = userService.updateById(user);

// 删除
boolean success = userService.removeById(1L);

// 批量删除
boolean success = userService.removeByIds(CollUtil.newArrayList(1L, 2L, 3L));
```

## 条件构造器

使用Lambda条件构造器构建查询条件：

```java
// 1. 基本用法
Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
    .eq(User::getStatus, "0")
    .like(User::getUserName, "zhang")
    .orderByDesc(User::getCreateTime);

// 2. 带条件的查询（null值不加入条件）
Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
    .eq(StrUtil.isNotEmpty(user.getStatus()), User::getStatus, user.getStatus())
    .like(StrUtil.isNotEmpty(user.getUserName()), User::getUserName, user.getUserName());

// 3. OR条件
Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
    .eq(User::getStatus, "0")
    .or()
    .eq(User::getStatus, "1");

// 4. 嵌套条件
Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
    .eq(User::getStatus, "0")
    .and(w -> w.eq(User::getRole, "admin").or().eq(User::getRole, "user"));
```

## 自定义SQL

对于复杂查询，使用自定义SQL：

1. 在Mapper接口中声明方法：
```java
public interface ArticleAuthorMapper extends BaseMapper<ArticleAuthor> {
    List<ArticleAuthor> selectByOrgIdIn(@Param("orgIds") Collection<Long> orgIds);
}
```

2. 在XML中实现SQL：
```xml
<select id="selectByOrgIdIn" resultMap="ArticleAuthorResult">
    <include refid="selectSql"/>
    where a.organization_id &amp;&amp;
    <foreach item="id" index="index" collection="orgIds" open="ARRAY[" separator="," close="]">
        #{id}
    </foreach>
</select>
```

## 事务管理

使用`@Transactional`注解管理事务：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void saveArticleInfo(ArticleInfoDTO dto) {
    // 保存文章
    Article article = dto.getArticle();
    this.save(article);

    // 保存关联作者
    List<ArticleAuthor> authors = dto.getArticleAuthors();
    if (CollUtil.isNotEmpty(authors)) {
        articleAuthorService.saveBatch(authors);
    }

    // 保存关联引用
    List<Reference> references = dto.getReferences();
    if (CollUtil.isNotEmpty(references)) {
        referenceService.saveBatch(references);
    }
}
```

注意：
- 使用`rollbackFor = Exception.class`确保所有异常都触发事务回滚
- 对大型事务进行拆分，避免长时间锁定表

## 批量操作

优先使用批量操作提高性能：

```java
// 批量保存
if (CollUtil.isNotEmpty(authorList)) {
    authorService.saveBatch(authorList);
}

// 批量更新
if (CollUtil.isNotEmpty(updateList)) {
    authorService.updateBatchById(updateList);
}

// 批量删除
if (CollUtil.isNotEmpty(removeIds)) {
    authorService.removeByIds(removeIds);
}
```

## 自定义类型处理器

对于复杂类型（如PostgreSQL的数组类型），使用自定义类型处理器：

1. 定义类型处理器：
```java
public class LongListArrayTypeHandler extends BaseTypeHandler<List<Long>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType)
            throws SQLException {
        if (parameter != null) {
            Array array = ps.getConnection().createArrayOf("bigint", parameter.toArray(new Long[0]));
            ps.setArray(i, array);
        }
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return arrayToList(array);
    }

    // 其他方法...
}
```

2. 在实体类中使用：
```java
@TableField(value = "organization_id", typeHandler = LongListArrayTypeHandler.class)
private List<Long> organizationId;
```

3. 在Mapper XML中使用：
```xml
<result property="organizationId" column="organization_id"
        typeHandler="org.biosino.lf.pds.article.config.LongListArrayTypeHandler"/>
```

## 性能优化

1. 使用分页查询处理大数据：
```java
Page<Article> page = new Page<>(pageNum, pageSize);
IPage<Article> result = articleMapper.selectPage(page, wrapper);
```

2. 使用`selectCount`代替`select().size()`：
```java
// 好的做法
long count = userMapper.selectCount(wrapper);

// 避免的做法
int count = userMapper.selectList(wrapper).size();
```

3. 使用动态SQL避免全表扫描：
```xml
<select id="selectUserList" resultType="User">
    select * from sys_user
    <where>
        <if test="userName != null and userName != ''">
            AND user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </where>
</select>
```

## 空值处理

使用Hutool的`CollUtil`和`StrUtil`进行空值检查：

```java
// 集合为空检查
if (CollUtil.isEmpty(orgIds)) {
    return CollUtil.newArrayList();
}

// 字符串为空检查
if (StrUtil.isBlank(userName)) {
    return null;
}

// 对象判空
if (ObjectUtil.isNull(user)) {
    return;
}

// 安全地获取集合中的元素
String firstItem = CollUtil.get(list, 0);

// 安全地转换类型
Integer value = Convert.toInt(obj);
```

## 复杂关联查询

对于复杂关联查询，可使用以下方式：

1. 使用多次单表查询组装数据：
```java
// 1. 查询主表数据
List<Article> articles = articleMapper.selectList(wrapper);

// 2. 提取ID列表
List<Long> articleIds = articles.stream()
    .map(Article::getId)
    .collect(Collectors.toList());

// 3. 查询关联表数据
List<ArticleAuthor> authors = articleAuthorService.list(
    Wrappers.<ArticleAuthor>lambdaQuery()
        .in(ArticleAuthor::getDocId, articleIds)
);

// 4. 组装数据
Map<Long, List<ArticleAuthor>> authorMap = authors.stream()
    .collect(Collectors.groupingBy(ArticleAuthor::getDocId));

articles.forEach(article -> {
    article.setAuthors(authorMap.getOrDefault(article.getId(), CollUtil.newArrayList()));
});
```

2. 使用JOIN查询（适用于简单关联）：
```xml
<select id="selectArticleWithAuthor" resultMap="ArticleWithAuthorResult">
    SELECT a.*, aa.*, au.*
    FROM tb_dds_article a
    LEFT JOIN tb_dds_article_author aa ON a.id = aa.doc_id
    LEFT JOIN tb_dds_author au ON aa.author_id = au.id
    WHERE a.id = #{articleId}
</select>
```

## Hutool工具类常用操作

```java
// 集合创建
List<Long> ids = CollUtil.newArrayList(1L, 2L, 3L);
Set<String> names = CollUtil.newHashSet("张三", "李四");
Map<String, Object> map = CollUtil.newHashMap();

// 字符串处理
boolean isEmpty = StrUtil.isEmpty(str);
boolean hasText = StrUtil.isNotBlank(str);
String trimmed = StrUtil.trim(str);
String formatted = StrUtil.format("用户名: {}", username);

// 文件操作
File file = FileUtil.file("data.txt");
String content = FileUtil.readUtf8String(file);
FileUtil.writeUtf8String("新内容", file);

// 日期处理
Date now = DateUtil.date();
String formatted = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
Date tomorrow = DateUtil.tomorrow();

// 数字处理
boolean isNumber = NumberUtil.isNumber(str);
double round = NumberUtil.round(value, 2).doubleValue();

// 加密解密
String md5 = SecureUtil.md5(str);
String base64 = Base64.encode(str);
```





