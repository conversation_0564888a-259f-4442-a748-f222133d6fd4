package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.service.IPublisherService;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 出版社服务实现类
 */
@Service
@RequiredArgsConstructor
public class PublisherServiceImpl extends ServiceImpl<PublisherMapper, Publisher> implements IPublisherService {

    @Override
    public List<Publisher> selectPublisherList(PublisherQueryDTO queryDTO) {
        return this.baseMapper.selectPublisherList(queryDTO);
    }

    @Override
    public Publisher selectPublisherById(Long id) {
        return this.getById(id);
    }


    @Override
    public boolean updatePublisher(Publisher publisher) {
        Publisher existPublisher = this.getOptById(publisher.getId()).orElseThrow(() -> new ServiceException("出版社不存在"));

        BeanUtil.copyProperties(publisher, existPublisher);
        existPublisher.setUpdateTime(new Date());

        return this.updateById(existPublisher);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean mergePublishers(PublisherMergeDTO mergeDTO) {
        // 检查目标出版社是否存在
        Publisher targetPublisher = this.getOptById(mergeDTO.getTargetId()).orElseThrow(() -> new ServiceException("目标出版社不存在"));

        // 检查源出版社是否存在
        List<Publisher> sourcePublishers = this.listByIds(mergeDTO.getSourceIds());
        if (CollUtil.isEmpty(sourcePublishers)) {
            throw new ServiceException("部分源出版社不存在");
        }

        // 更新目标出版社信息
        BeanUtil.copyProperties(mergeDTO, targetPublisher);
        targetPublisher.setUpdateTime(new Date());

        // 删除源出版社
        this.removeByIds(mergeDTO.getSourceIds());

        return this.updateById(targetPublisher);
    }

    @Override
    public boolean updatePublisherStatus(Long id, Integer status) {
        return this.update(Wrappers.<Publisher>lambdaUpdate().eq(Publisher::getId, id).set(Publisher::getStatus, status));
    }
}
