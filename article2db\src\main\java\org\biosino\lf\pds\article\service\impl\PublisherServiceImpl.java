package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.service.PublisherService;
import org.biosino.lf.pds.common.enums.JournalSourceTypeEnums;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 出版商服务实现类
 */
@Slf4j
@Service
public class PublisherServiceImpl extends ServiceImpl<PublisherMapper, Publisher> implements PublisherService {

    @Override
    public Publisher savePublisher(Publisher publisher) {
        if (publisher == null || publisher.getName() == null) {
            return null;
        }

        // 根据文章ID查询是否存在
        LambdaQueryWrapper<Publisher> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Publisher::getName, publisher.getName());

        Publisher existingPublisher = this.getOne(queryWrapper);

        if (existingPublisher != null) {
            // 更新出版商信息
            if (JournalSourceTypeEnums.system.name().equals(publisher.getSourceType())) {
                publisher.setId(existingPublisher.getId());
                publisher.setUpdateTime(new Date());
                updateById(publisher);
            }
            existingPublisher.setInsert(false);
            return existingPublisher;
        } else {
            // 新增出版商信息
            publisher.setId(IdUtils.getSnowflakeNextId());
            publisher.setCreateTime(new Date());
            publisher.setUpdateTime(new Date());
            publisher.setSourceType(JournalSourceTypeEnums.system.name());
            publisher.setStatus(StatusEnums.ENABLE.getCode());
            save(publisher);
            publisher.setInsert(true);
            return publisher;
        }
    }

    @Override
    public void deletedById(Long publisherId) {
        if (publisherId != null) {
            removeById(publisherId);
        }
    }
}
