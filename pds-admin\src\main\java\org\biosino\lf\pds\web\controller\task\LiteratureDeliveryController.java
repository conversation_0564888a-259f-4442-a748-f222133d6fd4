package org.biosino.lf.pds.web.controller.task;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.common.annotation.Log;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.enums.BusinessType;
import org.biosino.lf.pds.common.enums.task.TaskSourceEnum;
import org.biosino.lf.pds.article.custbean.dto.TaskPublishDTO;
import org.biosino.lf.pds.task.service.ITbDdsTaskService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文献传递控制层
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
@RestController
@RequestMapping("/task")
@RequiredArgsConstructor
public class LiteratureDeliveryController extends BaseController {
    private final ITbDdsTaskService tbDdsTaskService;


    //@Log(title = "文献传递任务", businessType = BusinessType.IMPORT)
    /*@PostMapping("/uploadIdExcel")
    public AjaxResult uploadIdExcel(MultipartFile file) {
        return tbDdsTaskService.uploadIdExcel(file);
    }*/

    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "文献传递任务", businessType = BusinessType.INSERT)
    @PostMapping("/publishTask")
    public AjaxResult publishTask(@Validated @RequestBody TaskPublishDTO dto) {
        tbDdsTaskService.publishTask(dto, getUserId(), TaskSourceEnum.PDS);
        return AjaxResult.success("任务发布成功");
    }
}
