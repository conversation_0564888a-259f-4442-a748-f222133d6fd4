package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文件真实数据
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_article_xml", autoResultMap = true)
public class ArticleXML {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件的真实内容
     */
    @TableField("file_data")
    private byte[] fileData;
}
