---
description: 
globs: 
alwaysApply: true
---
# 服务层指南

本项目的服务层遵循接口和实现分离的设计模式，为领域模型提供业务逻辑和持久化操作。

## 核心服务

### 文章服务

[IArticleService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleService.java)是核心服务接口，提供文章的CRUD操作以及合并和处理功能：
- 保存文章及其关联数据
- 合并来自不同来源的文章信息
- 查询和检索文章

### XML解析服务

XML解析服务负责将不同格式的XML文件解析为统一的领域模型：
- [XmlParseService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/XmlParseService.java) - 解析接口
- [PubmedXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/PubmedXmlParse.java) - PubMed XML解析实现
- [PmcXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/PmcXmlParse.java) - PMC XML解析实现
- [BioRxivXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/BioRxivXmlParse.java) - BioRxiv XML解析实现

## 关联服务

项目包含多个关联服务，用于处理文章的各个方面：

### 作者和机构

- [IAuthorService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IAuthorService.java) - 作者服务
- [IOrganizationService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IOrganizationService.java) - 机构服务
- [IArticleAuthorService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleAuthorService.java) - 文章作者关联服务

### 引用文献

[IReferenceService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IReferenceService.java) - 引用文献服务

### 主题和分类

- [IArticleMeshService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleMeshService.java) - MeSH术语服务
- [IArticleSupplMeshService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleSupplMeshService.java) - 补充MeSH术语服务
- [IPubTypeService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IPubTypeService.java) - 出版类型服务
- [IArticlePubTypeService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticlePubTypeService.java) - 文章出版类型关联服务

### 其他实体

- [IChemicalService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IChemicalService.java) - 化学物质服务
- [IArticleChemicalService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleChemicalService.java) - 文章化学物质关联服务
- [IGeneService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IGeneService.java) - 基因服务
- [IArticleGeneService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleGeneService.java) - 文章基因关联服务
- [IGrantService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IGrantService.java) - 资助服务
- [IArticleGrantService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleGrantService.java) - 文章资助关联服务
- [IArticleOtherIdService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleOtherIdService.java) - 文章其他ID服务
- [IArticleDatabankService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleDatabankService.java) - 文章数据库关联服务

## 事务管理

服务层实现了事务管理，确保数据的一致性和完整性：
- 使用`@Transactional`注解管理事务
- 在保存和合并操作中确保所有关联数据的一致性
- 处理异常并回滚事务

