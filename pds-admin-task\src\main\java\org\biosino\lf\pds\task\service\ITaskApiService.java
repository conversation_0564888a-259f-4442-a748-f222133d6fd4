package org.biosino.lf.pds.task.service;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.biosino.lf.pds.article.custbean.dto.api.HandshakeDTO;
import org.biosino.lf.pds.article.custbean.dto.api.NoticeApiDTO;
import org.biosino.lf.pds.article.custbean.vo.api.ApiResultVO;
import org.biosino.lf.pds.article.custbean.vo.api.SiteInfoVO;
import org.biosino.lf.pds.article.domain.TbDdsTaskSchedule;
import org.biosino.lf.pds.article.service.CommonService;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 小蚂蚁请求接口api服务层
 *
 * <AUTHOR>
 */
public interface ITaskApiService extends CommonService<TbDdsTaskSchedule> {
    ApiResultVO sendHandshake(HandshakeDTO dto, HttpServletRequest sysInfo);

    JSONObject getNextTaskInfo(String siteId, String startPmid, String endPmid);

    void getTaskScriptFile(String scriptIdStr, HttpServletResponse response);

    SiteInfoVO getSiteBaseinfo(String siteId);

    void sendTaskMessage(String taskId, String msg);
    
    /**
     * 上传任务结果文件
     * 
     * @param siteId 站点ID
     * @param taskId 任务ID
     * @param pmid 文献PMID
     * @param resultFile 结果文件（ZIP格式）
     * @param md5File MD5文件
     * @param docId 文档ID（可选）
     * @return 上传结果
     */
    AjaxResult uploadTaskResult(String siteId, String taskId, String pmid, MultipartFile resultFile, MultipartFile md5File, Long docId);

    ApiResultVO notice2UpdateTask(Integer siteId, NoticeApiDTO taskInfo);

}
