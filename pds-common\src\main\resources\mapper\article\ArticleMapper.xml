<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleMapper">

    <resultMap id="ArticleResult" type="org.biosino.lf.pds.article.domain.Article">
        <id property="id" column="id"/>
        <result property="customId" column="custom_id"/>
        <result property="pmid" column="pmid"/>
        <result property="pmcId" column="pmc_id"/>
        <result property="doi" column="doi"/>
        <result property="pubStatus" column="pub_status"/>
        <result property="language" column="language"/>
        <result property="vernacularTitle" column="vernacular_title"/>
        <result property="title" column="title"/>
        <result property="publishedYear" column="published_year"/>
        <result property="publishedMonth" column="published_month"/>
        <result property="publishedDay" column="published_day"/>
        <result property="journalId" column="journal_id"/>
        <result property="journalName" column="journal_name"/>
        <result property="year" column="year"/>
        <result property="volume" column="volume"/>
        <result property="issue" column="issue"/>
        <result property="page" column="page"/>
        <result property="articleAbstract" column="abstract"/>
        <result property="otherAbstract" column="other_abstract"/>
        <result property="copyright" column="copyright"/>
        <result property="hitNum" column="hit_num"/>
        <result property="download" column="download"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="source" column="source"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="author" column="author"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="affiliation" column="affiliation"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="keywords" column="keywords"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="otherDate" column="other_date"
                typeHandler="org.biosino.lf.pds.article.config.PubMedPubDateListTypeHandler"/>
        <result property="existPdf" column="exist_pdf"/>
    </resultMap>

    <sql id="selectArticleVo">
        select a.id,
               a.custom_id,
               a.pmid,
               a.pmc_id,
               a.doi,
               a.source,
               a.pub_status,
               a.language,
               a.vernacular_title,
               a.title,
               a.published_year,
               a.published_month,
               a.published_day,
               a.other_date,
               a.journal_id,
               j.title as journal_name,
               a.year,
               a.volume,
               a.issue,
               a.page,
               a.author,
               a.affiliation,
               a.keywords,
               a.abstract,
               a.other_abstract,
               a.copyright,
               a.hit_num,
               a.download,
               a.create_time,
               a.update_time
        from tb_dds_article a
                 left join tb_dds_journal j on a.journal_id = j.id
    </sql>

    <select id="selectArticleList" parameterType="org.biosino.lf.pds.article.dto.ArticleQueryDTO"
            resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        <where>
            <if test="pmid != null">
                a.pmid = #{pmid}
            </if>
            <if test="pmcId != null">
                and a.pmc_id = #{pmcId}
            </if>
            <if test="customId != null">
                and a.custom_id = #{customId}
            </if>
            <if test="doi != null and doi != ''">
                and a.doi = #{doi}
            </if>
            <if test="title != null and title != ''">
                and a.title like concat('%', #{title}, '%')
            </if>
            <if test="volume != null and volume != ''">
                and a.volume = #{volume}
            </if>
            <if test="journalName != null and journalName != ''">
                and j.title like concat('%', #{journalName}, '%')
            </if>
            <if test="yearStart != null">
                and a.published_year <![CDATA[>=]]> #{yearStart}
            </if>
            <if test="yearEnd != null">
                and a.published_year <![CDATA[<=]]> #{yearEnd}
            </if>
            <if test="monthStart != null">
                and a.published_month <![CDATA[>=]]> #{monthStart}
            </if>
            <if test="monthEnd != null">
                and a.published_month <![CDATA[<=]]> #{monthEnd}
            </if>
            <if test="dayStart != null">
                and a.published_day <![CDATA[>=]]> #{dayStart}
            </if>
            <if test="dayEnd != null">
                and a.published_day <![CDATA[<=]]> #{dayEnd}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectArticleById" parameterType="Long" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        <where>
            a.id = #{id}
        </where>
    </select>
</mapper>
