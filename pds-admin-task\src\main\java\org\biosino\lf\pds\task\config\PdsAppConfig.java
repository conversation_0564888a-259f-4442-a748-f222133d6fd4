package org.biosino.lf.pds.task.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PDS yml配置数据
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "pds")
public class PdsAppConfig {
    /**
     * 分配期刊脚本时，一次添加的脚本的最大期刊数量
     */
    private static Integer maxAddScriptNum = 10000;
    private static String defaultApiToken;
    private static Integer handshakeInterval = 3;
    private static Integer paperMaxRetryTimes = 3;

    public static Integer getMaxAddScriptNum() {
        return maxAddScriptNum;
    }

    public void setMaxAddScriptNum(Integer maxAddScriptNum) {
        PdsAppConfig.maxAddScriptNum = (maxAddScriptNum == null ? 10000 : maxAddScriptNum);
    }

    public static String getDefaultApiToken() {
        return defaultApiToken;
    }

    public void setDefaultApiToken(String defaultApiToken) {
        PdsAppConfig.defaultApiToken = defaultApiToken;
    }

    public static Integer getHandshakeInterval() {
        return handshakeInterval;
    }

    public void setHandshakeInterval(Integer handshakeInterval) {
        PdsAppConfig.handshakeInterval = handshakeInterval;
    }

    public static Integer getPaperMaxRetryTimes() {
        return paperMaxRetryTimes;
    }

    public void setPaperMaxRetryTimes(Integer paperMaxRetryTimes) {
        PdsAppConfig.paperMaxRetryTimes = paperMaxRetryTimes;
    }
}
