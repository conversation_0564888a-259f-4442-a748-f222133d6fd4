FROM registry.cn-hangzhou.aliyuncs.com/samjoy_public/tomcat:11.0.6-jdk17-temurin-jammy

#docker build -t dev.biosino.org/tbics/java_python_img:1.0.0 .
#docker push dev.biosino.org/tbics/java_python_img:1.0.0
#docker pull dev.biosino.org/tbics/java_python_img:1.0.0

ENV TZ=Asia/Shanghai
# Default to UTF-8 file.encoding
ENV LANG='C.UTF-8'
#ENV LANGUAGE='en_US:en'
ENV LC_ALL='C.UTF-8'

RUN set -eux; \
    cp -a /etc/apt/sources.list /etc/apt/sources.list.bak; \
    sed -i "s@http://.*archive.ubuntu.com@http://repo.huaweicloud.com@g" /etc/apt/sources.list; \
    sed -i "s@http://.*security.ubuntu.com@http://repo.huaweicloud.com@g" /etc/apt/sources.list; \
    DEBIAN_FRONTEND=noninteractive apt update;

#安装python环境
RUN set -eux; \
    apt update; \
    apt install -y apt-utils; \
    apt install -y python3 python3-dev python3-setuptools; \
    apt install -y python3-pip; \
    pip3 install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/;

RUN pip3 config --user set global.index https://mirrors.huaweicloud.com/repository/pypi
RUN pip3 config --user set global.index-url https://mirrors.huaweicloud.com/repository/pypi/simple
RUN pip3 config --user set global.trusted-host mirrors.huaweicloud.com

# 安装依赖
RUN DEBIAN_FRONTEND=noninteractive apt update && apt install -y --no-install-recommends apt-utils rsync vim time locales-all libpq5

RUN set -eux; \
    pip3 install --upgrade setuptools; \
    pip3 install psycopg;

EXPOSE 8080

# upstream eclipse-temurin-provided entrypoint script caused https://github.com/docker-library/tomcat/issues/77 to come back as https://github.com/docker-library/tomcat/issues/302; use "/entrypoint.sh" at your own risk
ENTRYPOINT []

CMD ["catalina.sh", "run"]

