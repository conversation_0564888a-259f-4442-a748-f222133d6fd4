package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.Author;
import org.biosino.lf.pds.article.mapper.AuthorMapper;
import org.biosino.lf.pds.article.service.IArticleAuthorService;
import org.biosino.lf.pds.article.service.IAuthorService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 作者信息表 服务实现类
 */
@Service
public class AuthorServiceImpl extends ServiceImpl<AuthorMapper, Author> implements IAuthorService {

    private final IArticleAuthorService articleAuthorService;

    public AuthorServiceImpl(@Lazy IArticleAuthorService articleAuthorService) {
        this.articleAuthorService = articleAuthorService;
    }

    @Override
    public List<Author> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return this.list(
                Wrappers.<Author>lambdaQuery()
                        .in(Author::getId, ids)
        );
    }


}
