package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * tb_dds_scriptlabel表和tb_dds_journal表关联表
 * 源刊（"2"）、高校（"3"）类型的标签关联中间表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_scriptlabel_journal")
public class TbDdsScriptlabelJournal implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * tb_dds_journal表id
     */
    private Long journalId;

    /**
     * tb_dds_scriptlabel表id
     */
    private Integer scriptlabellId;

    private Long creator;

    private Date createTime;

}
