---
description: 
globs: 
alwaysApply: true
---
# 字符串处理工具

本项目中包含多个用于处理字符串的工具方法，特别是在XML解析过程中使用。

## 句点处理

在处理作者姓名和其他文本字段时，有专门的方法处理句点：

```java
/**
 * 如果字符串最后一个字符是句点，则移除它
 * @param str 输入字符串
 * @return 处理后的字符串
 */
private String removeLastDot(String str) {
    if (StrUtil.isBlank(str)) {
        return str;
    }
    return str.endsWith(".") ? str.substring(0, str.length() - 1) : str;
}

/**
 * 检查字符串中的单个大写字母，如果后面没有句点则添加句点
 * 例如：Alexander A Pieper -> Alexander A. Pieper
 * @param str 输入字符串
 * @return 处理后的字符串
 */
private String addLastDot(String str) {
    if (StrUtil.isBlank(str)) {
        return str;
    }
    
    // 按空格分割字符串
    String[] parts = str.split("\\s+");
    StringBuilder result = new StringBuilder();
    
    for (int i = 0; i < parts.length; i++) {
        String part = parts[i];
        // 如果当前部分是单个大写字母，且后面没有句点，则添加句点
        if (part.length() == 1 && Character.isUpperCase(part.charAt(0)) && !part.endsWith(".")) {
            part = part + ".";
        }
        
        // 添加处理后的部分
        result.append(part);
        // 如果不是最后一部分，添加空格
        if (i < parts.length - 1) {
            result.append(" ");
        }
    }
    
    return result.toString();
}
```

## 其他字符串工具

项目中使用了Hutool工具库中的`StrUtil`类来处理字符串：

- `StrUtil.isBlank(str)` - 检查字符串是否为空或仅包含空白字符
- `StrUtil.isNotBlank(str)` - 检查字符串是否不为空且不仅包含空白字符
- `StrUtil.trimToNull(str)` - 去除字符串两端空白，如果结果为空字符串则返回null

## 日期字符串处理

在处理日期时，使用`MonthUtil`工具类将月份名称转换为数字：

```java
// 如果月份是文本形式，转换为数字
if (StrUtil.isNumeric(month)) {
    article.setPublishedMonth(Integer.parseInt(month));
} else {
    article.setPublishedMonth(MonthUtil.getMonth(month));
}
```

