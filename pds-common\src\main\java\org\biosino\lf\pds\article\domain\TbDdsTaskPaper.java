package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_task_paper")
public class TbDdsTaskPaper implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String taskId;

    private Long docId;

    private Long journalId;

    private Long publisherId;

    private String doi;

    private String status;

    private Date createTime;

    private Date updateTime;

    private Long creator;

    private Date timeExecute;

    private Date timeComplete;

    /**
     * 批次节点重试次数
     */
    private Integer batchRetryTimes;
    /**
     * 源刊节点重试次数
     */
    private Integer journalRetryTimes;
    /**
     * 高校节点重试次数
     */
    private Integer schoolRetryTimes;

}
