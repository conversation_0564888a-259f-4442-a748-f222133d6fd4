package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleMesh;

import java.util.List;

/**
 * 文章MeSH术语关联表 服务接口
 */
public interface IArticleMeshService extends IService<ArticleMesh> {
    /**
     * 根据文档ID查询文章MeSH术语关联信息
     *
     * @param docId 文档ID
     * @return 文章MeSH术语关联信息列表
     */
    List<ArticleMesh> findByDocId(Long docId);
    
    /**
     * 根据文档ID删除文章MeSH术语关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);
}
