import logging
import os
import random
import time
from datetime import datetime, timedelta
from typing import Generator, Tuple


# 创建带时区的时间字符串（如"2025-04-16 00:29:56+08"）
def get_timestamp_with_tz():
    # 获取当前时间（带本地时区） 格式化为带时区的字符串
    # now = datetime.now(timezone.utc).astimezone()
    # return now.strftime("%Y-%m-%d %H:%M:%S%z")
    # return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return datetime.now()


def init_default_logger(log_file_name: str, curr_logger: logging.Logger = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)
    # log_output_path = os.path.abspath(log_output_path)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
logger = init_default_logger('insert_sharding.log')


class SnowflakeGenerator:
    def __init__(self, worker_id: int = 1, datacenter_id: int = 1):
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1
        self.epoch = 1609459200000  # 2021-01-01 00:00:00

    def _current_time(self) -> int:
        return int(time.time() * 1000)

    def generate_id(self) -> int:
        timestamp = self._current_time()
        if timestamp < self.last_timestamp:
            raise Exception("Clock moved backwards!")
        if timestamp == self.last_timestamp:
            self.sequence = (self.sequence + 1) & 0xFFF
            if self.sequence == 0:
                timestamp = self._til_next_millis(self.last_timestamp)
        else:
            self.sequence = 0
        self.last_timestamp = timestamp
        return ((timestamp - self.epoch) << 22) | (self.datacenter_id << 17) | (self.worker_id << 12) | self.sequence

    def _til_next_millis(self, last_timestamp: int) -> int:
        timestamp = self._current_time()
        while timestamp <= last_timestamp:
            timestamp = self._current_time()
        return timestamp


class ArticleDataGenerator:
    def __init__(self):
        self.faker = Faker()
        self.snowflake = SnowflakeGenerator()
        self.pmid_set = set()
        self.pmc_set = set()

    def generate_unique_pmid(self) -> int:
        while True:
            pmid = random.randint(1000000, 9999999)
            if pmid not in self.pmid_set:
                self.pmid_set.add(pmid)
                return pmid

    def generate_unique_pmc(self) -> int:
        while True:
            pmc = random.randint(10000, 99999)
            if pmc not in self.pmc_set:
                self.pmc_set.add(pmc)
                return pmc

    def _short_str(self, max_length=8) -> str:
        """Generate a random string with max 8 characters"""
        return self.faker.pystr(max_chars=max_length)

    def _short_text(self, max_length=8) -> str:
        """Generate short text with max 8 characters"""
        return self._short_str(max_length)

    def generate_article_data(self, count: int) -> Generator[Tuple, None, None]:
        article_types = ['ResArt', 'Review', 'CaseRp', 'Edit', 'Letter']
        languages = ['en', 'zh', 'fr', 'de', 'es', 'ja']
        pub_statuses = ['ahead', 'epub', 'ppub']
        e_location_types = ['doi', 'pii', 'pmc']

        for _ in range(count):
            order_id = self.snowflake.generate_id()
            pmid = self.generate_unique_pmid()
            pmc_id = self.generate_unique_pmc()

            pub_date = self.faker.date_between(start_date='-10y', end_date='today')
            received_date = pub_date - timedelta(days=random.randint(30, 180))

            yield (
                order_id,
                pmid,
                pmc_id,
                random.choice(article_types),
                self._short_str(6),  # subject_type
                self._short_str(6),  # source
                random.choice(pub_statuses),
                random.choice(languages),
                self._short_str(8) if random.random() > 0.3 else None,  # vernacular_title
                self._short_str(8),  # title
                pub_date.strftime('%Y-%m-%d'),
                received_date.strftime('%Y-%m-%d'),
                pub_date.year,
                pub_date.month,
                pub_date.day,
                pub_date.year if random.random() > 0.2 else None,
                pub_date.month if random.random() > 0.2 else None,
                pub_date.day if random.random() > 0.2 else None,
                random.randint(100, 999),
                f"{pub_date.year % 100}{random.choice(['Spr', 'Sum', 'Aut', 'Win'])}" if random.random() > 0.7 else None,
                pub_date.year,
                str(random.randint(1, 9)),
                f"{random.randint(1, 9)}" if random.random() > 0.5 else None,
                f"{random.randint(1, 9)}-{random.randint(1, 9)}" if random.random() > 0.5 else None,
                random.choice(e_location_types) if random.random() > 0.7 else None,
                f"10.{random.randint(1, 9)}/j.{random.randint(1, 9)}" if random.random() > 0.7 else None,
                ', '.join([self._short_str(4) for _ in range(random.randint(1, 3))]),
                1 if random.random() > 0.2 else 0,
                ', '.join([self._short_str(6) for _ in range(random.randint(1, 2))]),
                1 if random.random() > 0.2 else 0,
                self._short_text(8) if random.random() > 0.5 else None,  # article_abstract
                self._short_text(8) if random.random() > 0.5 else None,  # article_abstract_cn
                self._short_text(8) if random.random() > 0.5 else None,  # copyright
                self._short_text(8) if random.random() > 0.3 else None,  # article_other_abstract
                self._short_text(8) if random.random() > 0.3 else None,  # other_copyright
                ', '.join([self._short_str(4) for _ in range(random.randint(1, 3))]),
                random.randint(0, 10),
                ', '.join([str(random.randint(100, 999)) for _ in
                           range(random.randint(0, 2))]) if random.random() > 0.5 else None,
                self._short_text(8) if random.random() > 0.3 else None,  # note
                random.randint(0, 100)
            )


def insert_data(total_records: int = 40_000_000, batch_size: int = 1000):
    conn_params = {
        "host": "wuhan-dev-shardingsphere-svc",
        "port": 3307,
        "dbname": "plosp_sharding",
        "user": "root",
        "password": "root",
        "autocommit": False
    }

    generator = ArticleDataGenerator()

    # Corrected INSERT statement with exactly 42 parameters
    insert_sql = """
    INSERT INTO public.tb_dds_article (
        order_id, pmid, pmc_id, article_type, subject_type, source, pub_status, language,
        vernacular_title, title, date_pubmed, date_received_accepted, published_year,
        published_month, published_day, print_published_year, print_published_month,
        print_published_day, journal_id, medline_date, year, volume, issue, page,
        e_location_type, e_location_id, author, author_isparsed, affiliation,
        affiliation_isparsed, article_abstract, article_abstract_cn, copyright,
        article_other_abstract, other_copyright, keyword, reference_number,
        reference_id_list, note, create_date, update_date, hit_num, update_validate_date
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(),
       NOW(), %s, NOW()
    )
    """

    # Count the placeholders to verify
    placeholder_count = insert_sql.count('%s')
    logger.info(f"SQL query has {placeholder_count} placeholders")

    try:
        with psycopg.connect(**conn_params) as conn:
            with conn.cursor() as cur:
                logger.info(f"Starting insertion of {total_records:,} records...")
                # cur.execute("SELECT * FROM tb_dds_article WHERE order_id = 58641558865575936")
                # single_row = cur.fetchone()
                # logger.info(single_row)

                records_inserted = 0
                batch_count = 0
                start_time = time.time()

                # Test with a small batch first to verify parameter counts
                test_batch = list(generator.generate_article_data(1))
                logger.info(f"Generated test record with {len(test_batch[0])} parameters")

                while records_inserted < total_records:
                    current_batch_size = min(batch_size, total_records - records_inserted)
                    batch_data = list(generator.generate_article_data(current_batch_size))

                    # Verify parameter counts for first record in batch
                    if records_inserted == 0:
                        logger.info(f"First record parameter count: {len(batch_data[0])}")

                    cur.executemany(insert_sql, batch_data)
                    conn.commit()

                    records_inserted += current_batch_size
                    batch_count += 1

                    if batch_count % 10 == 0 or records_inserted == total_records:
                        elapsed = time.time() - start_time
                        rate = records_inserted / elapsed
                        remaining = (total_records - records_inserted) / rate
                        logger.info(f"Inserted {records_inserted:,} records ({records_inserted / total_records:.1%}). "
                                    f"Rate: {rate:,.0f} rec/sec. "
                                    f"ETA: {timedelta(seconds=int(remaining))}")

                elapsed = time.time() - start_time
                logger.info(f"Finished inserting {records_inserted:,} records in {timedelta(seconds=int(elapsed))}")
                logger.info(f"Average rate: {records_inserted / elapsed:,.0f} records/sec")

    except Exception as e:
        logger.error(f"Error occurred: {e}")
        raise


def format_seconds(seconds: float) -> str:
    """
    格式化秒数为人类可读的字符串形式。

    参数:
    seconds (float): 需要格式化的秒数，可以是浮点数。

    返回:
    str: 格式化后的时间字符串，形式为"小时:分钟:秒"，只返回非零时间部分。
    """
    # 检查输入值是否为None或NaN，如果是，则返回空字符串
    if seconds is None:
        return ''

    try:
        # 将秒数转换为timedelta对象，用于后续的时间格式化
        # 这里使用round函数处理浮点数精度问题
        td = timedelta(seconds=round(seconds, 6))
    except (ValueError, OverflowError) as e:
        # 捕获异常，如果转换失败，则打印错误信息并返回空字符串
        logger.error(f"无法格式化时间: {seconds}, 错误信息: {e}")
        return ''

    # 将timedelta对象转换为小时、分钟和秒
    hours, remainder = divmod(td.total_seconds(), 3600)
    minutes, seconds = divmod(remainder, 60)

    # 将小时、分钟和秒转换为整数
    hours_int, minutes_int, seconds_int = map(int, [hours, minutes, seconds])

    # 根据小时、分钟和秒的值，返回相应的时间字符串
    if hours_int > 0:
        return f"{hours_int}小时{minutes_int}分钟{seconds_int}秒"
    elif minutes_int > 0:
        return f"{minutes_int}分钟{seconds_int}秒"
    else:
        return f"{seconds_int}秒"


if __name__ == "__main__":
    try:
        start_time = time.time()  # 记录开始时间
        import psycopg
        from faker import Faker

        insert_data(total_records=40_000_000, batch_size=1000)
        # insert_data(total_records=3000, batch_size=1000)
        execution_time = format_seconds(time.time() - start_time)  # 计算执行时间
        logger.info(f"导入数据总耗时: {execution_time}")
    except Exception as e:
        logger.error(f"导入数据出错: {e}")
        raise e
