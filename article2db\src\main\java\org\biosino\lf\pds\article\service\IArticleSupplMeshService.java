package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleSupplMesh;

import java.util.List;

/**
 * 文章补充MeSH术语关联表 服务接口
 */
public interface IArticleSupplMeshService extends IService<ArticleSupplMesh> {
    /**
     * 根据文档ID查询文章补充MeSH术语关联信息
     *
     * @param docId 文档ID
     * @return 文章补充MeSH术语关联信息列表
     */
    List<ArticleSupplMesh> findByDocId(Long docId);
    
    /**
     * 根据文档ID删除文章补充MeSH术语关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);
}
