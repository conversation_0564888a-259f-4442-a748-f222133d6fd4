package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.lf.pds.article.custbean.dto.SiteDTO;
import org.biosino.lf.pds.article.custbean.dto.SiteStatusDTO;
import org.biosino.lf.pds.article.custbean.vo.SiteVO;
import org.biosino.lf.pds.article.domain.TbDdsSite;
import org.biosino.lf.pds.article.mapper.TbDdsSiteMapper;
import org.biosino.lf.pds.common.constant.CacheConstants;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.ScriptTypeEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.task.config.PdsAppConfig;
import org.biosino.lf.pds.task.service.ITbDdsSiteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 节点管理服务层实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsSiteServiceImpl extends ServiceImpl<TbDdsSiteMapper, TbDdsSite> implements ITbDdsSiteService {
    private final RedisCache redisCache;

    /**
     * 查询节点列表
     */
    @Override
    public List<SiteVO> selectSiteList(SiteDTO siteDTO) {
        return baseMapper.selectTbDdsSiteList(siteDTO);
    }

    /**
     * 根据ID获取节点详情
     */
    @Override
    public SiteVO getSiteById(Integer id) {
        if (id == null) {
            throw new ServiceException("节点ID不能为空");
        }

        // 使用Mapper的getSiteVOById方法获取节点详情
        SiteVO vo = baseMapper.getSiteVOById(id);
        if (vo == null) {
            throw new ServiceException("节点不存在");
        }

        return vo;
    }

    public static String getSiteCacheKey(Integer siteId) {
        return CacheConstants.SITE_INFO_KEY + siteId;
    }

    /**
     * 保存节点（新增或修改）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveSite(final SiteDTO siteDTO, final Long userId) {
        if (siteDTO == null) {
            return AjaxResult.error("参数不能为空");
        }

        if (StrUtil.isBlank(siteDTO.getSiteName())) {
            return AjaxResult.error("节点名称不能为空");
        }

        final String siteType = siteDTO.getSiteType();
        if (StrUtil.isBlank(siteType)) {
            return AjaxResult.error("节点类型不能为空");
        }

        final List<String> allTypes = ScriptTypeEnum.allCode();
        if (!allTypes.contains(siteType)) {
            return AjaxResult.error("节点类型错误");
        }

        final String siteGroup = siteDTO.getSiteGroup();
        if (StrUtil.isBlank(siteGroup)) {
            return AjaxResult.error("节点分组不能为空");
        }

        try {
            // 检查节点名称是否已存在
            TbDdsSite existingByName = getOne(Wrappers.lambdaQuery(TbDdsSite.class)
                    .eq(TbDdsSite::getSiteName, siteDTO.getSiteName())
                    .ne(siteDTO.getId() != null, TbDdsSite::getId, siteDTO.getId()));

            if (existingByName != null) {
                return AjaxResult.error("节点名称已存在，请更换名称");
            }

            TbDdsSite entity;
            final boolean isUpdate = siteDTO.getId() != null;
            final Date now = new Date();

            if (isUpdate) {
                // 修改操作，先查询原记录
                entity = getById(siteDTO.getId());
                if (entity == null) {
                    return AjaxResult.error("节点不存在");
                }

                // 不允许修改节点类型
                if (!entity.getSiteType().equals(siteType)) {
                    return AjaxResult.error("节点类型不允许修改");
                }

                entity.setUpdateTime(now);
            } else {
                // 新增操作
                entity = new TbDdsSite();
                entity.setCreator(userId);
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entity.setStatus(StatusEnums.ENABLE.getCode().toString()); // 默认状态为正常
                entity.setApiToken(PdsAppConfig.getDefaultApiToken());
            }

            // 设置属性
            entity.setSiteName(siteDTO.getSiteName());
            entity.setSiteAbbr(siteDTO.getSiteAbbr());
            entity.setSiteType(siteType);
            entity.setSiteGroup(siteGroup);
            entity.setObtainTaskInterval(siteDTO.getObtainTaskInterval());
            entity.setUnit(siteDTO.getUnit());
            entity.setAddress(siteDTO.getAddress());
            entity.setScriptlabelId(siteDTO.getScriptlabelId());
            final Integer taskThreadNum = siteDTO.getTaskThreadNum();
            entity.setTaskThreadNum(taskThreadNum == null ? 10 : taskThreadNum);

            // 如果更新时传入了状态，则使用传入的状态
            if (isUpdate && StrUtil.isNotBlank(siteDTO.getStatus())) {
                entity.setStatus(siteDTO.getStatus());
            }

            // 保存记录
            boolean success = saveOrUpdate(entity);
            cleanSiteCache();
            if (!success) {
                return AjaxResult.error(isUpdate ? "修改失败" : "添加失败");
            }

            return AjaxResult.success(isUpdate ? "修改成功" : "添加成功", entity.getId());
        } catch (Exception e) {
            log.error("保存节点失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }

    private void cleanSiteCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.SITE_INFO_KEY + "*");
        if (CollUtil.isNotEmpty(keys)) {
            redisCache.deleteObject(keys);
        }
    }

    /**
     * 删除节点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteSiteById(Integer id) {
        if (id == null) {
            return AjaxResult.error("节点ID不能为空");
        }

        try {
            // 查询节点信息
            TbDdsSite site = getById(id);
            if (site == null) {
                return AjaxResult.error("节点不存在");
            }

            // 删除节点记录
            boolean success = removeById(id);
            cleanSiteCache();
            if (!success) {
                throw new ServiceException("删除失败");
            }

            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除节点失败", e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

    /**
     * 修改节点状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeStatus(SiteStatusDTO statusDTO) {
        if (statusDTO == null || statusDTO.getSiteId() == null || StrUtil.isBlank(statusDTO.getStatus())) {
            return AjaxResult.error("参数不能为空");
        }

        try {
            // 查询节点信息
            TbDdsSite entity = getById(statusDTO.getSiteId());
            if (entity == null) {
                return AjaxResult.error("节点不存在");
            }

            // 修改状态
            entity.setStatus(statusDTO.getStatus());
            entity.setUpdateTime(new Date());

            // 保存记录
            boolean success = updateById(entity);
            cleanSiteCache();
            if (!success) {
                return AjaxResult.error("状态修改失败");
            }

            return AjaxResult.success("状态修改成功");
        } catch (Exception e) {
            log.error("修改节点状态失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有节点分组
     */
    @Override
    public List<String> selectAllGroups() {
        // 查询所有节点
        List<TbDdsSite> sites = list();
        // 提取所有非空分组并去重
        return sites.stream()
                .map(TbDdsSite::getSiteGroup)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public TbDdsSite findByIdWithCache(Integer siteId) {
        if (siteId == null) {
            return null;
        }
        final String cacheKey = getSiteCacheKey(siteId);
        TbDdsSite site = redisCache.getCacheObject(cacheKey);
        if (site == null) {
            site = getById(siteId);
            redisCache.setCacheObject(cacheKey, site, 10, TimeUnit.MINUTES);
        }
        return site;
    }

    @Override
    public List<Integer> findReTrySiteIds(Long paperId, List<String> siteTypes, Integer scriptlabelId, Integer currSiteId) {
        final Integer handshakeInterval = PdsAppConfig.getHandshakeInterval();
        int intervalVal = 3;
        if (handshakeInterval != null && handshakeInterval > 0) {
            intervalVal = handshakeInterval;
        }
        // 获取当前时间往前推3分钟的时间字符串
        final String aliveHandshakeTime = DateFormatUtils.format(DateUtil.offsetMinute(new Date(), -intervalVal), DatePattern.NORM_DATETIME_PATTERN);
        return this.baseMapper.findReTrySiteIds(StatusEnums.ENABLE.getCode().toString(), aliveHandshakeTime, paperId, siteTypes, scriptlabelId);
    }

}
