package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 基金信息表
 */
@Data
@TableName(value = "tb_dds_grant", autoResultMap = true)
public class Grant {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 基金ID
     */
    @TableField("grant_id")
    private String grantId;

    /**
     * 缩写
     */
    @TableField("acronym")
    private String acronym;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 机构
     */
    @TableField("agency")
    private String agency;
}
