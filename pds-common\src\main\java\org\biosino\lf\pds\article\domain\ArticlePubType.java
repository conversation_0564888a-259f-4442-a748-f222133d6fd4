package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章出版类型关联表
 */
@Data
@TableName(value = "tb_dds_article_pubtype", autoResultMap = true)
public class ArticlePubType {
    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 出版类型ID
     */
    @TableField("pubtype_id")
    private Long pubtypeId;
}
