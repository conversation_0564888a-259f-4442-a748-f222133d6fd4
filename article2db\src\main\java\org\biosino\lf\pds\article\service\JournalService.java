package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.domain.Publisher;

/**
 * 期刊服务接口
 */
public interface JournalService extends IService<Journal> {
    /**
     * 根据NLM ID保存或更新期刊信息
     *
     * @param journal 期刊信息
     * @return 期刊ID
     */
    Journal saveJournal(Journal journal, Long publisherId);
}
