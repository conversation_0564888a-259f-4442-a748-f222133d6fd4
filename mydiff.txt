diff --git a/.cursor/rules/backend-code-style.mdc b/.cursor/rules/backend-code-style.mdc
deleted file mode 100644
index a98d6b7..0000000
--- a/.cursor/rules/backend-code-style.mdc
+++ /dev/null
@@ -1,216 +0,0 @@
----
-description:
-globs:
-alwaysApply: true
----
-# 后端代码风格
-
-本项目后端代码遵循以下编码规范和最佳实践：
-
-## 整体架构
-
-项目采用传统的分层架构：
-- Controller层：负责处理HTTP请求，参数校验
-- Service层：定义业务逻辑接口
-- ServiceImpl层：实现业务逻辑
-- Mapper层：数据访问层
-- Domain层：实体类定义
-
-## 依赖注入规范
-
-本项目统一使用Lombok的`@RequiredArgsConstructor`进行依赖注入，配合final关键字声明所需依赖：
-
-```java
-@Service
-@RequiredArgsConstructor
-public class UserServiceImpl implements IUserService {
-    // 使用final标记，由@RequiredArgsConstructor自动注入
-    private final UserMapper userMapper;
-    private final RoleService roleService;
-
-    // 业务方法...
-}
-```
-
-这种方式相比`@Autowired`的优势：
-1. 代码更简洁，不需要显式编写构造函数
-2. 强制依赖不可变，更符合设计原则
-3. 便于单元测试，依赖关系更明确
-4. 避免循环依赖问题（通过@Lazy注解处理特殊情况）
-
-## Controller层规范
-
-1. 继承`BaseController`获取通用功能
-2. 使用`@RestController`注解标记控制器
-3. 使用`@RequestMapping`定义请求路径
-4. 使用`@Log`注解记录操作日志
-5. 返回统一的`AjaxResult`或`TableDataInfo`对象
-6. 方法命名规范：
-   - list：查询列表
-   - export：导出数据
-   - getInfo：获取详情
-   - add：新增数据
-   - edit：修改数据
-   - remove：删除数据
-
-示例:
-```java
-@RestController
-@RequestMapping("/monitor/job")
-@RequiredArgsConstructor
-public class SysJobController extends BaseController
-{
-    private final ISysJobService jobService;
-
-    @GetMapping("/list")
-    public TableDataInfo list(SysJob sysJob)
-    {
-        startPage();
-        List<SysJob> list = jobService.selectJobList(sysJob);
-        return getDataTable(list);
-    }
-}
-```
-
-## Service层规范
-
-本项目基于MyBatis-Plus框架，Service层采用以下模式：
-
-```java
-@Service
-@RequiredArgsConstructor
-public class ArticleAuthorServiceImpl extends ServiceImpl<ArticleAuthorMapper, ArticleAuthor> implements IArticleAuthorService {
-
-    // 如果需要处理循环依赖，使用@Lazy注解
-    @Lazy
-    private final IAuthorService authorService;
-    private final IOrganizationService organizationService;
-
-    @Override
-    public List<ArticleAuthor> selectByOrgIdIn(Collection<Long> orgIds) {
-        if (CollUtil.isEmpty(orgIds)) {
-            return CollUtil.newArrayList();
-        }
-        return this.baseMapper.selectByOrgIdIn(orgIds);
-    }
-}
-```
-
-主要特点：
-1. 接口以`I`为前缀，如`IArticleAuthorService`
-2. 实现类继承自`ServiceImpl<M, T>`获取基本CRUD操作能力
-3. 使用`@Service`注解标记服务类
-4. 使用`@RequiredArgsConstructor`注入依赖，依赖字段使用final修饰
-5. 使用`@Transactional`进行事务管理
-6. 方法命名规范：
-   - select*/find*：查询操作
-   - insert*/save*：新增操作
-   - update*：修改操作
-   - delete*/remove*：删除操作
-   - check*：校验操作
-
-## 实体类规范
-
-1. 使用Lombok注解简化代码：
-   - `@Data`：自动生成getter/setter/equals/hashCode/toString
-   - `@Builder`：构建器模式
-   - `@NoArgsConstructor`：无参构造器
-   - `@AllArgsConstructor`：全参构造器
-
-2. 使用MyBatis-Plus注解：
-   - `@TableName`：指定表名
-   - `@TableId`：指定主键
-   - `@TableField`：指定字段名
-   - `@TableField(exist = false)`：非数据库字段
-
-3. 使用数据校验注解：
-   - `@NotBlank`：非空字符串
-   - `@Size`：长度限制
-   - `@NotNull`：非空对象
-
-示例：
-```java
-@Data
-@TableName(value = "tb_dds_article_author", autoResultMap = true)
-public class ArticleAuthor {
-    /**
-     * 主键ID
-     */
-    private Long id;
-
-    /**
-     * 文档ID
-     */
-    @TableField("doc_id")
-    private Long docId;
-
-    /**
-     * 机构ID
-     */
-    @TableField(value = "organization_id", typeHandler = LongListArrayTypeHandler.class)
-    private List<Long> organizationId;
-
-    @TableField(exist = false)
-    private Author author;
-}
-```
-
-## Mapper层规范
-
-1. 继承MyBatis-Plus的`BaseMapper`
-2. 使用`@Mapper`注解标记Mapper接口
-3. 自定义查询方法使用`@Param`注解参数
-
-示例：
-```java
-@Mapper
-public interface ArticleAuthorMapper extends BaseMapper<ArticleAuthor> {
-    List<ArticleAuthor> selectByOrgIdIn(@Param("orgIds") Collection<Long> orgIds);
-    List<ArticleAuthor> findByDocId(@Param("docId") Long docId);
-}
-```
-
-## XML映射文件规范
-
-1. 使用resultMap定义结果映射
-2. 使用sql片段重用SQL语句
-3. 复杂查询参数使用foreach标签处理集合
-
-示例：
-```xml
-<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleAuthorMapper">
-    <resultMap id="ArticleAuthorResult" type="org.biosino.lf.pds.article.domain.ArticleAuthor">
-        <id property="id" column="id"/>
-        <result property="docId" column="doc_id"/>
-        <result property="organizationId" column="organization_id"
-                typeHandler="org.biosino.lf.pds.article.config.LongListArrayTypeHandler"/>
-    </resultMap>
-
-    <sql id="selectSql">
-        select a.id, a.doc_id, a.author_id, a.organization_id from tb_dds_article_author a
-    </sql>
-
-    <select id="selectByOrgIdIn" resultMap="ArticleAuthorResult">
-        <include refid="selectSql"/>
-        where a.organization_id &amp;&amp;
-        <foreach item="id" index="index" collection="orgIds" open="ARRAY[" separator="," close="]">
-            #{id}
-        </foreach>
-    </select>
-</mapper>
-```
-
-## 工具类使用
-
-1. 优先使用Hutool工具类
-2. 使用`CollUtil`处理集合操作
-3. 使用`StrUtil`处理字符串操作
-4. 注重空值检查和边界条件处理
-
-
-
-
-
-
-
-
diff --git a/.cursor/rules/db-operations.mdc b/.cursor/rules/db-operations.mdc
deleted file mode 100644
index 60b71be..0000000
--- a/.cursor/rules/db-operations.mdc
+++ /dev/null
@@ -1,321 +0,0 @@
----
-description:
-globs:
-alwaysApply: true
----
-# 数据库操作指南
-
-本项目使用MyBatis-Plus作为ORM框架，以下是数据库操作的最佳实践：
-
-## 基础CRUD操作
-
-利用MyBatis-Plus提供的基础CRUD方法简化开发：
-
-```java
-// 插入单条记录
-boolean success = userService.save(user);
-
-// 批量插入
-boolean success = userService.saveBatch(userList);
-
-// 根据ID查询
-User user = userService.getById(1L);
-
-// 条件查询
-List<User> users = userService.list(
-    Wrappers.<User>lambdaQuery()
-        .eq(User::getStatus, "0")
-        .like(User::getUsername, "admin")
-);
-
-// 分页查询
-Page<User> page = new Page<>(1, 10);
-IPage<User> userPage = userService.page(page,
-    Wrappers.<User>lambdaQuery().eq(User::getStatus, "0")
-);
-
-// 更新
-boolean success = userService.updateById(user);
-
-// 删除
-boolean success = userService.removeById(1L);
-
-// 批量删除
-boolean success = userService.removeByIds(CollUtil.newArrayList(1L, 2L, 3L));
-```
-
-## 条件构造器
-
-使用Lambda条件构造器构建查询条件：
-
-```java
-// 1. 基本用法
-Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
-    .eq(User::getStatus, "0")
-    .like(User::getUserName, "zhang")
-    .orderByDesc(User::getCreateTime);
-
-// 2. 带条件的查询（null值不加入条件）
-Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
-    .eq(StrUtil.isNotEmpty(user.getStatus()), User::getStatus, user.getStatus())
-    .like(StrUtil.isNotEmpty(user.getUserName()), User::getUserName, user.getUserName());
-
-// 3. OR条件
-Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
-    .eq(User::getStatus, "0")
-    .or()
-    .eq(User::getStatus, "1");
-
-// 4. 嵌套条件
-Wrapper<User> wrapper = Wrappers.<User>lambdaQuery()
-    .eq(User::getStatus, "0")
-    .and(w -> w.eq(User::getRole, "admin").or().eq(User::getRole, "user"));
-```
-
-## 自定义SQL
-
-对于复杂查询，使用自定义SQL：
-
-1. 在Mapper接口中声明方法：
-```java
-public interface ArticleAuthorMapper extends BaseMapper<ArticleAuthor> {
-    List<ArticleAuthor> selectByOrgIdIn(@Param("orgIds") Collection<Long> orgIds);
-}
-```
-
-2. 在XML中实现SQL：
-```xml
-<select id="selectByOrgIdIn" resultMap="ArticleAuthorResult">
-    <include refid="selectSql"/>
-    where a.organization_id &amp;&amp;
-    <foreach item="id" index="index" collection="orgIds" open="ARRAY[" separator="," close="]">
-        #{id}
-    </foreach>
-</select>
-```
-
-## 事务管理
-
-使用`@Transactional`注解管理事务：
-
-```java
-@Override
-@Transactional(rollbackFor = Exception.class)
-public void saveArticleInfo(ArticleInfoDTO dto) {
-    // 保存文章
-    Article article = dto.getArticle();
-    this.save(article);
-
-    // 保存关联作者
-    List<ArticleAuthor> authors = dto.getArticleAuthors();
-    if (CollUtil.isNotEmpty(authors)) {
-        articleAuthorService.saveBatch(authors);
-    }
-
-    // 保存关联引用
-    List<Reference> references = dto.getReferences();
-    if (CollUtil.isNotEmpty(references)) {
-        referenceService.saveBatch(references);
-    }
-}
-```
-
-注意：
-- 使用`rollbackFor = Exception.class`确保所有异常都触发事务回滚
-- 对大型事务进行拆分，避免长时间锁定表
-
-## 批量操作
-
-优先使用批量操作提高性能：
-
-```java
-// 批量保存
-if (CollUtil.isNotEmpty(authorList)) {
-    authorService.saveBatch(authorList);
-}
-
-// 批量更新
-if (CollUtil.isNotEmpty(updateList)) {
-    authorService.updateBatchById(updateList);
-}
-
-// 批量删除
-if (CollUtil.isNotEmpty(removeIds)) {
-    authorService.removeByIds(removeIds);
-}
-```
-
-## 自定义类型处理器
-
-对于复杂类型（如PostgreSQL的数组类型），使用自定义类型处理器：
-
-1. 定义类型处理器：
-```java
-public class LongListArrayTypeHandler extends BaseTypeHandler<List<Long>> {
-    @Override
-    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType)
-            throws SQLException {
-        if (parameter != null) {
-            Array array = ps.getConnection().createArrayOf("bigint", parameter.toArray(new Long[0]));
-            ps.setArray(i, array);
-        }
-    }
-
-    @Override
-    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
-        Array array = rs.getArray(columnName);
-        return arrayToList(array);
-    }
-
-    // 其他方法...
-}
-```
-
-2. 在实体类中使用：
-```java
-@TableField(value = "organization_id", typeHandler = LongListArrayTypeHandler.class)
-private List<Long> organizationId;
-```
-
-3. 在Mapper XML中使用：
-```xml
-<result property="organizationId" column="organization_id"
-        typeHandler="org.biosino.lf.pds.article.config.LongListArrayTypeHandler"/>
-```
-
-## 性能优化
-
-1. 使用分页查询处理大数据：
-```java
-Page<Article> page = new Page<>(pageNum, pageSize);
-IPage<Article> result = articleMapper.selectPage(page, wrapper);
-```
-
-2. 使用`selectCount`代替`select().size()`：
-```java
-// 好的做法
-long count = userMapper.selectCount(wrapper);
-
-// 避免的做法
-int count = userMapper.selectList(wrapper).size();
-```
-
-3. 使用动态SQL避免全表扫描：
-```xml
-<select id="selectUserList" resultType="User">
-    select * from sys_user
-    <where>
-        <if test="userName != null and userName != ''">
-            AND user_name like concat('%', #{userName}, '%')
-        </if>
-        <if test="status != null and status != ''">
-            AND status = #{status}
-        </if>
-    </where>
-</select>
-```
-
-## 空值处理
-
-使用Hutool的`CollUtil`和`StrUtil`进行空值检查：
-
-```java
-// 集合为空检查
-if (CollUtil.isEmpty(orgIds)) {
-    return CollUtil.newArrayList();
-}
-
-// 字符串为空检查
-if (StrUtil.isBlank(userName)) {
-    return null;
-}
-
-// 对象判空
-if (ObjectUtil.isNull(user)) {
-    return;
-}
-
-// 安全地获取集合中的元素
-String firstItem = CollUtil.get(list, 0);
-
-// 安全地转换类型
-Integer value = Convert.toInt(obj);
-```
-
-## 复杂关联查询
-
-对于复杂关联查询，可使用以下方式：
-
-1. 使用多次单表查询组装数据：
-```java
-// 1. 查询主表数据
-List<Article> articles = articleMapper.selectList(wrapper);
-
-// 2. 提取ID列表
-List<Long> articleIds = articles.stream()
-    .map(Article::getId)
-    .collect(Collectors.toList());
-
-// 3. 查询关联表数据
-List<ArticleAuthor> authors = articleAuthorService.list(
-    Wrappers.<ArticleAuthor>lambdaQuery()
-        .in(ArticleAuthor::getDocId, articleIds)
-);
-
-// 4. 组装数据
-Map<Long, List<ArticleAuthor>> authorMap = authors.stream()
-    .collect(Collectors.groupingBy(ArticleAuthor::getDocId));
-
-articles.forEach(article -> {
-    article.setAuthors(authorMap.getOrDefault(article.getId(), CollUtil.newArrayList()));
-});
-```
-
-2. 使用JOIN查询（适用于简单关联）：
-```xml
-<select id="selectArticleWithAuthor" resultMap="ArticleWithAuthorResult">
-    SELECT a.*, aa.*, au.*
-    FROM tb_dds_article a
-    LEFT JOIN tb_dds_article_author aa ON a.id = aa.doc_id
-    LEFT JOIN tb_dds_author au ON aa.author_id = au.id
-    WHERE a.id = #{articleId}
-</select>
-```
-
-## Hutool工具类常用操作
-
-```java
-// 集合创建
-List<Long> ids = CollUtil.newArrayList(1L, 2L, 3L);
-Set<String> names = CollUtil.newHashSet("张三", "李四");
-Map<String, Object> map = CollUtil.newHashMap();
-
-// 字符串处理
-boolean isEmpty = StrUtil.isEmpty(str);
-boolean hasText = StrUtil.isNotBlank(str);
-String trimmed = StrUtil.trim(str);
-String formatted = StrUtil.format("用户名: {}", username);
-
-// 文件操作
-File file = FileUtil.file("data.txt");
-String content = FileUtil.readUtf8String(file);
-FileUtil.writeUtf8String("新内容", file);
-
-// 日期处理
-Date now = DateUtil.date();
-String formatted = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
-Date tomorrow = DateUtil.tomorrow();
-
-// 数字处理
-boolean isNumber = NumberUtil.isNumber(str);
-double round = NumberUtil.round(value, 2).doubleValue();
-
-// 加密解密
-String md5 = SecureUtil.md5(str);
-String base64 = Base64.encode(str);
-```
-
-
-
-
-
diff --git a/.cursor/rules/frontend-code-style.mdc b/.cursor/rules/frontend-code-style.mdc
deleted file mode 100644
index 90fbafb..0000000
--- a/.cursor/rules/frontend-code-style.mdc
+++ /dev/null
@@ -1,396 +0,0 @@
----
-description:
-globs:
-alwaysApply: true
----
-# 前端代码风格
-
-本项目前端使用Vue 3和Element Plus构建，遵循以下编码规范：
-
-## 整体架构
-
-- 采用Vue 3的组合式API (Composition API)
-- 使用`<script setup>`语法糖简化代码结构
-- 使用Pinia进行状态管理
-- 使用Vue Router管理路由
-- 使用Element Plus作为UI组件库
-
-## 组件结构
-
-每个Vue组件通常按照以下结构组织：
-1. `<template>` - HTML模板
-2. `<script setup>` - JavaScript逻辑
-3. `<style>` - CSS样式（可选）
-
-```vue
-<template>
-  <!-- HTML模板 -->
-</template>
-
-<script setup name="User">
-  // 引入依赖
-  // 定义状态和方法
-  // 声明生命周期钩子
-</script>
-
-<style>
-  /* CSS样式 */
-</style>
-```
-
-## 状态管理
-
-1. 使用`ref`和`reactive`进行响应式状态管理：
-```js
-// 简单值使用ref
-const loading = ref(true);
-const showSearch = ref(true);
-
-// 对象使用reactive
-const data = reactive({
-  form: {},
-  queryParams: {
-    pageNum: 1,
-    pageSize: 20
-  }
-});
-
-// 解构reactive对象供模板使用
-const { queryParams, form } = toRefs(data);
-```
-
-2. 使用Pinia管理全局状态：
-```js
-import useAppStore from '@/store/modules/app';
-const appStore = useAppStore();
-```
-
-## 表单处理
-
-1. 表单验证规则：
-```js
-rules: {
-  userName: [
-    { required: true, message: '用户名称不能为空', trigger: 'blur' },
-    { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
-  ],
-  email: [
-    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
-  ]
-}
-```
-
-2. 表单提交：
-```js
-function submitForm() {
-  proxy.$refs['userRef'].validate(valid => {
-    if (valid) {
-      // 表单验证通过
-      updateUser(form.value).then(response => {
-        proxy.$modal.msgSuccess('修改成功');
-        open.value = false;
-        getList();
-      });
-    }
-  });
-}
-```
-
-## API调用
-
-1. 将API调用封装在单独的模块中：
-```js
-import {
-  listUser,
-  getUser,
-  delUser,
-  addUser,
-  updateUser
-} from '@/api/system/user';
-```
-
-2. 在组件中调用API：
-```js
-function getList() {
-  loading.value = true;
-  listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(
-    res => {
-      loading.value = false;
-      userList.value = res.rows;
-      total.value = res.total;
-    }
-  );
-}
-```
-
-## 事件处理
-
-1. 使用函数声明处理事件：
-```js
-function handleQuery() {
-  queryParams.value.pageNum = 1;
-  getList();
-}
-
-function handleDelete(row) {
-  const userIds = row.userId || ids.value;
-  proxy.$modal
-    .confirm('是否确认删除用户编号为"' + userIds + '"的数据项？')
-    .then(function () {
-      return delUser(userIds);
-    })
-    .then(() => {
-      getList();
-      proxy.$modal.msgSuccess('删除成功');
-    });
-}
-```
-
-2. 监听状态变化：
-```js
-watch(deptName, val => {
-  proxy.$refs['deptTreeRef'].filter(val);
-});
-```
-
-## 组件通信
-
-1. 使用`v-model`进行双向绑定：
-```html
-<pagination
-  v-show="total > 0"
-  v-model:page="queryParams.pageNum"
-  v-model:limit="queryParams.pageSize"
-  :total="total"
-  @pagination="getList"
-/>
-```
-
-2. 使用`props`和事件进行父子组件通信：
-```html
-<right-toolbar
-  v-model:show-search="showSearch"
-  :columns="columns"
-  @query-table="getList"
-></right-toolbar>
-```
-
-## UI组件使用
-
-1. 表格组件：
-```html
-<el-table
-  v-loading="loading"
-  :data="userList"
-  @selection-change="handleSelectionChange"
->
-  <el-table-column type="selection" width="50" align="center" />
-  <el-table-column label="用户名称" align="center" prop="userName" />
-  <!-- 其他列 -->
-</el-table>
-```
-
-2. 表单组件：
-```html
-<el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
-  <el-row>
-    <el-col :span="12">
-      <el-form-item label="用户昵称" prop="nickName">
-        <el-input v-model="form.nickName" placeholder="请输入用户昵称" />
-      </el-form-item>
-    </el-col>
-  </el-row>
-</el-form>
-```
-
-3. 对话框组件：
-```html
-<el-dialog v-model="open" :title="title" width="600px" append-to-body>
-  <!-- 对话框内容 -->
-  <template #footer>
-    <div class="dialog-footer">
-      <el-button type="primary" @click="submitForm">确 定</el-button>
-      <el-button @click="cancel">取 消</el-button>
-    </div>
-  </template>
-</el-dialog>
-```
-
-## 工具函数使用
-
-1. 使用全局挂载的工具函数：
-```js
-// 日期范围处理
-proxy.addDateRange(queryParams.value, dateRange.value)
-
-// 重置表单
-proxy.resetForm('queryRef')
-
-// 消息提示
-proxy.$modal.msgSuccess('删除成功')
-
-// 确认对话框
-proxy.$modal.confirm('是否确认删除?')
-```
-
-2. 使用字典数据：
-```js
-const { sys_normal_disable, sys_user_sex } = proxy.useDict(
-  'sys_normal_disable',
-  'sys_user_sex'
-);
-```
-
-## 项目工具类（ruoyi.js 和 index.js）
-
-本项目包含两个主要的工具类文件，提供了丰富的辅助函数：
-
-### ruoyi.js 核心工具函数
-
-1. 日期格式化：
-```js
-import { parseTime } from '@/utils/ruoyi';
-
-// 格式化日期
-parseTime(new Date(), '{y}-{m}-{d}'); // 2023-05-20
-parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'); // 2023-05-20 14:30:45
-```
-
-2. 表单操作：
-```js
-// 重置表单
-resetForm('formRef');
-
-// 添加日期范围到查询参数
-const params = addDateRange(queryParams, dateRange);
-// 结果：params.params = { beginTime: '2023-01-01', endTime: '2023-12-31' }
-```
-
-3. 字典数据处理：
-```js
-// 回显单个字典项
-const status = selectDictLabel(sys_normal_disable, '0'); // '正常'
-
-// 回显多个字典项
-const roles = selectDictLabels(sys_role_options, '1,2', ','); // '管理员,普通用户'
-```
-
-4. 树结构数据处理：
-```js
-// 构造树结构
-const treeData = handleTree(flatData, 'id', 'parentId');
-/*
-[
-  {
-    id: 1,
-    name: '父节点',
-    children: [
-      { id: 2, name: '子节点', parentId: 1 }
-    ]
-  }
-]
-*/
-```
-
-### index.js 扩展工具函数
-
-1. 时间格式化：
-```js
-// 格式化日期时间
-formatDate('2023-05-20T14:30:45'); // '2023-05-20 14:30:45'
-
-// 相对时间格式化
-formatTime(Date.now() - 3600 * 1000); // '1小时前'
-```
-
-2. URL参数处理：
-```js
-// URL参数转对象
-const query = param2Obj('https://example.com?id=1&name=test');
-// { id: '1', name: 'test' }
-
-// 对象转URL参数
-const str = param({ id: 1, name: 'test' }); // 'id=1&name=test'
-```
-
-3. 对象和数组操作：
-```js
-// 深拷贝对象
-const newObj = deepClone(originalObj);
-
-// 数组去重
-const uniqueArray = uniqueArr([1, 2, 2, 3, 3, 3]);
-// [1, 2, 3]
-```
-
-4. 字符串处理：
-```js
-// 首字母大写
-titleCase('hello world'); // 'Hello World'
-
-// 下划线转驼峰
-camelCase('user_name'); // 'userName'
-
-// 去除两边空格
-trimStr(' hello '); // 'hello'
-```
-
-5. 防抖函数：
-```js
-// 函数防抖
-const debouncedFn = debounce(() => {
-  console.log('执行搜索');
-}, 300);
-
-// 在输入事件中调用
-searchInput.addEventListener('input', debouncedFn);
-```
-
-### 工具类使用最佳实践
-
-1. 按需导入工具函数，避免全局导入：
-```js
-// 推荐
-import { parseTime, selectDictLabel } from '@/utils/ruoyi';
-
-// 避免
-import * as ruoyiUtil from '@/utils/ruoyi';
-```
-
-2. 优先使用工具类中的方法，避免重复实现相同功能：
-```js
-// 推荐
-import { deepClone } from '@/utils/index';
-const newObj = deepClone(originalObj);
-
-// 避免
-const newObj = JSON.parse(JSON.stringify(originalObj)); // 不处理特殊情况
-```
-
-3. 开发新功能时，考虑是否可以扩展现有工具类，保持一致性：
-```js
-// 在utils/index.js中扩展
-export function capitalizeFirstLetter(str) {
-  return str.charAt(0).toUpperCase() + str.slice(1);
-}
-```
-
-## 命名规范
-
-1. 组件名：使用PascalCase
-2. 实例名：使用camelCase
-3. 事件处理函数：使用handle前缀
-4. 属性名：使用camelCase
-5. 常量：使用全大写下划线分隔
-
-## 代码格式
-
-1. 使用2个空格作为缩进
-2. 使用单引号作为字符串引号
-3. 大括号前有空格
-4. 组件顶级标签之间有空行
-
-
-
-
-
diff --git a/.cursor/rules/project-tech-stack.mdc b/.cursor/rules/project-tech-stack.mdc
deleted file mode 100644
index 25f8205..0000000
--- a/.cursor/rules/project-tech-stack.mdc
+++ /dev/null
@@ -1,46 +0,0 @@
----
-description:
-globs:
-alwaysApply: true
----
-# 项目技术栈
-
-本项目是一个文献传递系统(PLOSP & PDS)，主要技术栈如下：
-
-## 后端技术
-
-- **核心框架**：Spring Boot 3.3.12
-- **ORM框架**：MyBatis-Plus 3.5.12
-- **数据库**：MySQL 8.2.0
-- **工具库**：Hutool 5.8.37
-- **搜索引擎**：Elasticsearch 8.13.4，Easy-ES 3.0.0
-- **对象映射**：MapStruct 1.5.5
-- **定时任务**：Quartz
-- **安全框架**：Spring Security
-- **接口文档**：SpringDoc OpenAPI 2.6.0
-- **Excel处理**：Apache POI
-- **序列化工具**：FastJSON 2
-- **验证框架**：Jakarta Validation API
-
-## 前端技术
-
-- **核心框架**：Vue 3
-- **UI组件库**：Element Plus
-- **状态管理**：Pinia
-- **路由管理**：Vue Router
-- **构建工具**：Vite
-- **组件库**：SplitPanes等
-
-## 项目结构
-
-本项目采用多模块架构，主要模块包括：
-
-- pds-admin：管理后台模块
-- pds-framework：框架核心模块
-- pds-system：系统功能模块
-- pds-quartz：定时任务模块
-- pds-generator：代码生成模块
-- pds-common：公共模块
-- plosp-admin：PLOSP管理模块
-- article2db：文章数据模块
-
diff --git a/pds-common/src/main/java/org/biosino/lf/pds/article/domain/PlospUser.java b/pds-common/src/main/java/org/biosino/lf/pds/article/domain/PlospUser.java
index 52eddf7..66ac066 100644
--- a/pds-common/src/main/java/org/biosino/lf/pds/article/domain/PlospUser.java
+++ b/pds-common/src/main/java/org/biosino/lf/pds/article/domain/PlospUser.java
@@ -25,7 +25,7 @@ public class PlospUser implements Serializable {
     /**
      * 主键ID
      */
-    @TableId(value = "id", type = IdType.AUTO)
+    @TableId(value = "user_id", type = IdType.AUTO)
     private Long userId;
 
     /**
diff --git a/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Publisher.java b/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Publisher.java
index e588942..0242207 100644
--- a/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Publisher.java
+++ b/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Publisher.java
@@ -1,9 +1,15 @@
 package org.biosino.lf.pds.article.domain;
 
-import com.baomidou.mybatisplus.annotation.*;
+import com.baomidou.mybatisplus.annotation.IdType;
+import com.baomidou.mybatisplus.annotation.TableField;
+import com.baomidou.mybatisplus.annotation.TableId;
+import com.baomidou.mybatisplus.annotation.TableName;
+import com.fasterxml.jackson.annotation.JsonFormat;
 import lombok.Data;
+import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;
 
 import java.util.Date;
+import java.util.List;
 
 /**
  * 出版社表
@@ -11,12 +17,13 @@ import java.util.Date;
  * <AUTHOR>
  */
 @Data
-@TableName(value = "tb_dds_publisher", autoResultMap = true)
+@TableName(value = "tb_dds_publisher_250715", autoResultMap = true)
 public class Publisher {
     /**
      * 文档ID
      */
     @TableId(value = "id", type = IdType.ASSIGN_ID)
+    @JsonFormat(shape = JsonFormat.Shape.STRING)
     private Long id;
 
     /**
@@ -25,9 +32,21 @@ public class Publisher {
     @TableField("name")
     private String name;
 
+    /**
+     * 地址
+     */
     @TableField("ioc")
     private String ioc;
 
+    /**
+     * 别名
+     */
+    @TableField(value = "alias", typeHandler = StringListArrayTypeHandler.class)
+    private List<String> alias;
+
+    /**
+     * 来源
+     */
     @TableField("source_type")
     private String sourceType;
 
@@ -55,4 +74,10 @@ public class Publisher {
     @TableField(exist = false)
     private boolean insert;
 
+    /**
+     * 非数据库字段，期刊数量
+     */
+    @TableField(exist = false)
+    private Integer journalCount;
+
 }
diff --git a/pds-common/src/main/java/org/biosino/lf/pds/article/dto/PublisherMergeDTO.java b/pds-common/src/main/java/org/biosino/lf/pds/article/dto/PublisherMergeDTO.java
index 9ec8b6f..c15fae8 100644
--- a/pds-common/src/main/java/org/biosino/lf/pds/article/dto/PublisherMergeDTO.java
+++ b/pds-common/src/main/java/org/biosino/lf/pds/article/dto/PublisherMergeDTO.java
@@ -1,5 +1,8 @@
 package org.biosino.lf.pds.article.dto;
 
+import jakarta.validation.constraints.NotBlank;
+import jakarta.validation.constraints.NotEmpty;
+import jakarta.validation.constraints.NotNull;
 import lombok.Data;
 
 import java.util.List;
@@ -13,16 +16,20 @@ public class PublisherMergeDTO {
     /**
      * 目标出版社ID（保留的出版社）
      */
+    @NotNull
     private Long targetId;
 
     /**
      * 源出版社ID列表（要合并的出版社）
      */
+    @NotNull
+    @NotEmpty
     private List<Long> sourceIds;
 
     /**
      * 合并后的出版社名称
      */
+    @NotBlank
     private String name;
 
     /**
@@ -31,7 +38,7 @@ public class PublisherMergeDTO {
     private String ioc;
 
     /**
-     * 合并后的别名（每行一个）
+     * 合并后的别名
      */
-    private String alias;
+    private List<String> alias;
 }
diff --git a/pds-common/src/main/java/org/biosino/lf/pds/article/mapper/PublisherMapper.java b/pds-common/src/main/java/org/biosino/lf/pds/article/mapper/PublisherMapper.java
index 68ed1ff..5bc562e 100644
--- a/pds-common/src/main/java/org/biosino/lf/pds/article/mapper/PublisherMapper.java
+++ b/pds-common/src/main/java/org/biosino/lf/pds/article/mapper/PublisherMapper.java
@@ -3,10 +3,23 @@ package org.biosino.lf.pds.article.mapper;
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
 import org.apache.ibatis.annotations.Mapper;
 import org.biosino.lf.pds.article.domain.Publisher;
+import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
+
+import java.util.List;
 
 /**
- * 出版社数据访问接口
+ * 出版社 Mapper 接口
  */
 @Mapper
 public interface PublisherMapper extends BaseMapper<Publisher> {
+
+    /**
+     * 查询出版社列表
+     *
+     * @param queryDTO 查询条件
+     * @return 出版社列表
+     */
+    List<Publisher> selectPublisherList(PublisherQueryDTO queryDTO);
+
+
 }
diff --git a/pds-common/src/main/resources/mapper/article/ArticleMapper.xml b/pds-common/src/main/resources/mapper/article/ArticleMapper.xml
index 546920b..3fd0467 100644
--- a/pds-common/src/main/resources/mapper/article/ArticleMapper.xml
+++ b/pds-common/src/main/resources/mapper/article/ArticleMapper.xml
@@ -82,9 +82,8 @@
             resultMap="ArticleResult">
         <include refid="selectArticleVo"/>
         <where>
-            a.id = '1940205807782965248'
             <if test="pmid != null">
-                and a.pmid = #{pmid}
+                a.pmid = #{pmid}
             </if>
             <if test="pmcId != null">
                 and a.pmc_id = #{pmcId}
diff --git a/pds-common/src/main/resources/mapper/article/PublisherMapper.xml b/pds-common/src/main/resources/mapper/article/PublisherMapper.xml
index ea44c87..160053f 100644
--- a/pds-common/src/main/resources/mapper/article/PublisherMapper.xml
+++ b/pds-common/src/main/resources/mapper/article/PublisherMapper.xml
@@ -8,23 +8,26 @@
         <id property="id" column="id"/>
         <result property="name" column="name"/>
         <result property="ioc" column="ioc"/>
-        <result property="alias" column="alias"/>
+        <result property="alias" column="alias"
+                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
         <result property="sourceType" column="source_type"/>
         <result property="status" column="status"/>
         <result property="createTime" column="create_time"/>
         <result property="updateTime" column="update_time"/>
+        <result property="journalCount" column="journal_count"/>
     </resultMap>
 
     <sql id="selectSql">
-        select id,
-               name,
-               ioc,
-               alias,
-               source_type,
-               status,
-               create_time,
-               update_time
-        from tb_dds_publisher_250715
+        select p.id,
+               p.name,
+               p.ioc,
+               p.alias,
+               p.source_type,
+               p.status,
+               p.create_time,
+               p.update_time,
+               (select count(*) from tb_dds_journal j where j.publisher_id = p.id) as journal_count
+        from tb_dds_publisher_250715 p
     </sql>
 
     <!-- 查询出版社列表 -->
@@ -44,25 +47,15 @@
             <if test="sourceType != null and sourceType != ''">
                 and source_type = #{sourceType}
             </if>
+            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
+                AND to_char(update_time,'yyyy-MM-dd')::date &gt;= to_date(#{beginTime},'yyyy-MM-dd')
+            </if>
+            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
+                AND to_char(update_time,'yyyy-MM-dd')::date &lt;= to_date(#{endTime},'yyyy-MM-dd')
+            </if>
         </where>
         order by update_time desc
     </select>
 
-    <!-- 根据ID列表查询出版社 -->
-    <select id="selectPublisherByIds" parameterType="java.util.List" resultMap="PublisherResult">
-        <include refid="selectSql"/>
-        where id in
-        <foreach item="id" collection="list" open="(" separator="," close=")">
-            #{id}
-        </foreach>
-    </select>
-
-    <!-- 批量删除出版社 -->
-    <delete id="deletePublisherByIds" parameterType="java.util.List">
-        delete from tb_dds_publisher_250715 where id in
-        <foreach item="id" collection="list" open="(" separator="," close=")">
-            #{id}
-        </foreach>
-    </delete>
 
 </mapper>
diff --git a/pds-system/src/main/java/org/biosino/lf/pds/article/service/IPublisherService.java b/pds-system/src/main/java/org/biosino/lf/pds/article/service/IPublisherService.java
index 810c8bd..e3a7083 100644
--- a/pds-system/src/main/java/org/biosino/lf/pds/article/service/IPublisherService.java
+++ b/pds-system/src/main/java/org/biosino/lf/pds/article/service/IPublisherService.java
@@ -4,6 +4,7 @@ import com.baomidou.mybatisplus.extension.service.IService;
 import org.biosino.lf.pds.article.domain.Publisher;
 import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
 import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
+import org.biosino.lf.pds.article.vo.ErrorMsgVO;
 
 import java.util.List;
 
@@ -29,30 +30,14 @@ public interface IPublisherService extends IService<Publisher> {
     Publisher selectPublisherById(Long id);
 
 
-
     /**
      * 修改出版社
      *
      * @param publisher 出版社
      * @return 结果
      */
-    int updatePublisher(Publisher publisher);
-
-    /**
-     * 批量删除出版社
-     *
-     * @param ids 需要删除的出版社ID
-     * @return 结果
-     */
-    int deletePublisherByIds(List<Long> ids);
+    boolean updatePublisher(Publisher publisher);
 
-    /**
-     * 删除出版社信息
-     *
-     * @param id 出版社ID
-     * @return 结果
-     */
-    int deletePublisherById(Long id);
 
     /**
      * 合并出版社
@@ -60,14 +45,23 @@ public interface IPublisherService extends IService<Publisher> {
      * @param mergeDTO 合并参数
      * @return 结果
      */
-    int mergePublishers(PublisherMergeDTO mergeDTO);
+    boolean mergePublishers(PublisherMergeDTO mergeDTO);
 
     /**
      * 修改出版社状态
      *
-     * @param id 出版社ID
+     * @param id     出版社ID
      * @param status 状态
      * @return 结果
      */
-    int updatePublisherStatus(Long id, Integer status);
+    boolean updatePublisherStatus(Long id, Integer status);
+
+    /**
+     * 校验别名是否已存在
+     *
+     * @param alias      别名列表
+     * @param excludeIds 排除的出版社ID列表
+     * @return 错误信息列表
+     */
+    List<ErrorMsgVO> validateAlias(List<String> alias, List<Long> excludeIds);
 }
diff --git a/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/PublisherServiceImpl.java b/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/PublisherServiceImpl.java
index 4f24120..5dcfbfc 100644
--- a/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/PublisherServiceImpl.java
+++ b/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/PublisherServiceImpl.java
@@ -1,6 +1,10 @@
 package org.biosino.lf.pds.article.service.impl;
 
+import cn.hutool.core.bean.BeanUtil;
+import cn.hutool.core.collection.CollUtil;
 import cn.hutool.core.util.StrUtil;
+import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
+import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import lombok.RequiredArgsConstructor;
 import org.biosino.lf.pds.article.domain.Publisher;
@@ -8,8 +12,8 @@ import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
 import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
 import org.biosino.lf.pds.article.mapper.PublisherMapper;
 import org.biosino.lf.pds.article.service.IPublisherService;
+import org.biosino.lf.pds.article.vo.ErrorMsgVO;
 import org.biosino.lf.pds.common.exception.ServiceException;
-
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 
@@ -35,111 +39,80 @@ public class PublisherServiceImpl extends ServiceImpl<PublisherMapper, Publisher
     }
 
 
-
     @Override
-    public int updatePublisher(Publisher publisher) {
-        if (publisher.getId() == null) {
-            throw new ServiceException("出版社ID不能为空");
-        }
-        if (StrUtil.isBlank(publisher.getName())) {
-            throw new ServiceException("出版社名称不能为空");
-        }
-        
-        Publisher existPublisher = this.getById(publisher.getId());
-        if (existPublisher == null) {
-            throw new ServiceException("出版社不存在");
-        }
-        
-        publisher.setUpdateTime(new Date());
-        return this.updateById(publisher) ? 1 : 0;
-    }
+    public boolean updatePublisher(Publisher publisher) {
+        Publisher existPublisher = this.getOptById(publisher.getId()).orElseThrow(() -> new ServiceException("出版社不存在"));
 
-    @Override
-    public int deletePublisherByIds(List<Long> ids) {
-        if (ids == null || ids.isEmpty()) {
-            throw new ServiceException("删除的出版社ID不能为空");
-        }
-        
-        return this.baseMapper.deletePublisherByIds(ids);
-    }
+        BeanUtil.copyProperties(publisher, existPublisher);
+        existPublisher.setUpdateTime(new Date());
 
-    @Override
-    public int deletePublisherById(Long id) {
-        if (id == null) {
-            throw new ServiceException("出版社ID不能为空");
-        }
-        
-        return this.removeById(id) ? 1 : 0;
+        return this.updateById(existPublisher);
     }
 
+
     @Override
     @Transactional(rollbackFor = Exception.class)
-    public int mergePublishers(PublisherMergeDTO mergeDTO) {
-        if (mergeDTO.getTargetId() == null) {
-            throw new ServiceException("目标出版社ID不能为空");
-        }
-        if (mergeDTO.getSourceIds() == null || mergeDTO.getSourceIds().isEmpty()) {
-            throw new ServiceException("源出版社ID不能为空");
-        }
-        if (StrUtil.isBlank(mergeDTO.getName())) {
-            throw new ServiceException("合并后的出版社名称不能为空");
-        }
-        
+    public boolean mergePublishers(PublisherMergeDTO mergeDTO) {
         // 检查目标出版社是否存在
-        Publisher targetPublisher = this.getById(mergeDTO.getTargetId());
-        if (targetPublisher == null) {
-            throw new ServiceException("目标出版社不存在");
-        }
-        
+        Publisher targetPublisher = this.getOptById(mergeDTO.getTargetId()).orElseThrow(() -> new ServiceException("目标出版社不存在"));
+
         // 检查源出版社是否存在
-        List<Publisher> sourcePublishers = this.baseMapper.selectPublisherByIds(mergeDTO.getSourceIds());
-        if (sourcePublishers.size() != mergeDTO.getSourceIds().size()) {
+        List<Publisher> sourcePublishers = this.listByIds(mergeDTO.getSourceIds());
+        if (CollUtil.isEmpty(sourcePublishers)) {
             throw new ServiceException("部分源出版社不存在");
         }
-        
+
         // 更新目标出版社信息
-        targetPublisher.setName(mergeDTO.getName());
-        if (StrUtil.isNotBlank(mergeDTO.getIoc())) {
-            targetPublisher.setIoc(mergeDTO.getIoc());
-        }
-        if (StrUtil.isNotBlank(mergeDTO.getAlias())) {
-            // 将换行分隔的字符串转换为List
-            String[] aliasArray = mergeDTO.getAlias().split("\n");
-            List<String> aliasList = new ArrayList<>();
-            for (String alias : aliasArray) {
-                if (StrUtil.isNotBlank(alias.trim())) {
-                    aliasList.add(alias.trim());
-                }
-            }
-            targetPublisher.setAlias(aliasList);
-        }
+        BeanUtil.copyProperties(mergeDTO, targetPublisher);
         targetPublisher.setUpdateTime(new Date());
-        
-        this.updateById(targetPublisher);
-        
+
         // 删除源出版社
-        this.baseMapper.deletePublisherByIds(mergeDTO.getSourceIds());
-        
-        return 1;
+        this.removeByIds(mergeDTO.getSourceIds());
+
+        return this.updateById(targetPublisher);
     }
 
     @Override
-    public int updatePublisherStatus(Long id, Integer status) {
-        if (id == null) {
-            throw new ServiceException("出版社ID不能为空");
+    public boolean updatePublisherStatus(Long id, Integer status) {
+        return this.update(Wrappers.<Publisher>lambdaUpdate().eq(Publisher::getId, id).set(Publisher::getStatus, status));
+    }
+
+    @Override
+    public List<ErrorMsgVO> validateAlias(List<String> alias, List<Long> excludeIds) {
+        List<ErrorMsgVO> result = new ArrayList<>();
+
+        if (CollUtil.isEmpty(alias)) {
+            return result;
         }
-        if (status == null) {
-            throw new ServiceException("状态不能为空");
+
+        // 查询所有出版社的别名数据
+        LambdaQueryWrapper<Publisher> queryWrapper = new LambdaQueryWrapper<>();
+        queryWrapper.select(Publisher::getId, Publisher::getName, Publisher::getAlias);
+
+        // 排除指定的ID
+        if (CollUtil.isNotEmpty(excludeIds)) {
+            queryWrapper.notIn(Publisher::getId, excludeIds);
         }
-        
-        Publisher publisher = this.getById(id);
-        if (publisher == null) {
-            throw new ServiceException("出版社不存在");
+
+        List<Publisher> existingPublishers = this.list(queryWrapper);
+
+        // 校验每个别名
+        for (int i = 0; i < alias.size(); i++) {
+            String aliasItem = alias.get(i);
+            if (StrUtil.isBlank(aliasItem)) {
+                continue;
+            }
+
+            // 检查是否与现有出版社的别名重复
+            for (Publisher publisher : existingPublishers) {
+                if (CollUtil.isNotEmpty(publisher.getAlias()) && publisher.getAlias().contains(aliasItem)) {
+                    result.add(ErrorMsgVO.errMsg(i, "别名", aliasItem,
+                        StrUtil.format("别名已存在于出版社：{}", publisher.getName())));
+                    break;
+                }
+            }
         }
-        
-        publisher.setStatus(status);
-        publisher.setUpdateTime(new Date());
-        
-        return this.updateById(publisher) ? 1 : 0;
+
+        return result;
     }
 }
diff --git a/plosp-admin-ui/src/views/literature/import.vue b/plosp-admin-ui/src/views/literature/import.vue
index 36ecbb2..9ae45e4 100644
--- a/plosp-admin-ui/src/views/literature/import.vue
+++ b/plosp-admin-ui/src/views/literature/import.vue
@@ -125,7 +125,7 @@ import * as XLSX from 'xlsx';
 import {useRouter} from 'vue-router';
 import {getMaxCustomId, importArticle} from '@/api/article/article';
 import {isArrEmpty, sleep, trimStr} from "@/utils/index.js";
-import ResultLog from "@/views/literature/ResultLog.vue";
+import ResultLog from "@/components/ResultLog/index.vue";
 
 // 导入路由
 const router = useRouter();
diff --git a/plosp-admin-ui/src/views/literature/index.vue b/plosp-admin-ui/src/views/literature/index.vue
index f204c8d..72131c6 100644
--- a/plosp-admin-ui/src/views/literature/index.vue
+++ b/plosp-admin-ui/src/views/literature/index.vue
@@ -493,7 +493,7 @@ const dateRange = ref([]);
 // 查询参数
 const queryParams = ref({
   pageNum: 1,
-  pageSize: 20,
+  pageSize: 10,
   pmid: null,
   pmcId: null,
   customId: null,
@@ -860,7 +860,7 @@ function removeAuthor(index) {
 /** 编辑按钮操作 */
 function handleEdit(row) {
   // 先显示loading
-  const loadingInstance = proxy.$modal.loading("正在加载文献详情...");
+  proxy.$modal.loading("正在加载文献详情...");
 
   // 获取文献详情
   getArticle(row.id).then(response => {
@@ -932,11 +932,11 @@ function handleEdit(row) {
     setupFileList(data);
 
     // 关闭loading，打开弹窗
-    loadingInstance.close();
+    proxy.$modal.closeLoading();
     editDialogVisible.value = true;
   }).catch(error => {
     // 关闭loading
-    loadingInstance.close();
+    proxy.$modal.closeLoading();
     console.error("获取文献详情失败:", error);
     proxy.$modal.msgError("获取文献详情失败");
   });
diff --git a/plosp-admin-ui/src/views/publishers/index.vue b/plosp-admin-ui/src/views/publishers/index.vue
index 53bbe2c..f7e7eed 100644
--- a/plosp-admin-ui/src/views/publishers/index.vue
+++ b/plosp-admin-ui/src/views/publishers/index.vue
@@ -12,7 +12,7 @@
       <el-form-item label="状态" prop="status">
         <el-select v-model="queryParams.status" class="search-input" placeholder="出版社状态" clearable>
           <el-option
-            v-for="dict in statusOptions"
+              v-for="dict in sys_normal_disable"
             :key="dict.value"
             :label="dict.label"
             :value="dict.value"
@@ -51,15 +51,31 @@
     <el-table v-loading="loading" :data="publisherList" @selection-change="handleSelectionChange">
       <el-table-column type="selection" width="55" align="center" />
       <el-table-column label="出版社名称" align="center" prop="name" show-overflow-tooltip/>
-      <el-table-column label="出版社地址" align="center" prop="address" min-width="150" show-overflow-tooltip/>
+      <el-table-column label="出版社地址" align="center" prop="ioc" min-width="150" show-overflow-tooltip/>
+      <el-table-column label="出版社别名" align="center" min-width="120">
+        <template #default="scope">
+          <span>{{
+              Array.isArray(scope.row.alias) && scope.row.alias.length > 0 ? scope.row.alias[0] : (scope.row.alias || '-')
+            }}</span>
+          <el-tooltip v-if="Array.isArray(scope.row.alias) && scope.row.alias.length > 1">
+            <template #content>
+              <div v-for="(aliasItem, index) in scope.row.alias.slice(1)" :key="index">{{ aliasItem }}</div>
+            </template>
+            <el-icon class="more-icon">
+              <Fold/>
+            </el-icon>
+          </el-tooltip>
+        </template>
+      </el-table-column>
       <el-table-column label="期刊数量" align="center" prop="journalCount" width="150">
+        <template #default="scope">
+          {{ scope.row.journalCount || 0 }}
+        </template>
       </el-table-column>
       <el-table-column label="更新时间" align="center" prop="updateTime" width="250" />
       <el-table-column label="状态" align="center" prop="status" width="150">
         <template #default="scope">
-          <el-tag :type="scope.row.status === '0' ? 'danger' : 'success'">
-            {{ scope.row.status === '0' ? '停用' : '正常' }}
-          </el-tag>
+          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
         </template>
       </el-table-column>
       <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
@@ -71,19 +87,13 @@
             @click="handleUpdate(scope.row)"
           >编辑</el-button>
           <el-button
-            :type="scope.row.status === '0' ? 'success' : 'danger'"
-            link
-            icon="CircleCheck"
-            v-if="scope.row.status === '0'"
-            @click="handleStatusChange(scope.row)"
-          >启用</el-button>
-          <el-button
-            :type="scope.row.status === '0' ? 'success' : 'danger'"
-            link
-            icon="CircleClose"
-            v-else
-            @click="handleStatusChange(scope.row)"
-          >停用</el-button>
+              :type="scope.row.status === 1 ? 'success' : 'danger'"
+              link
+              :icon="scope.row.status === 1 ? 'CircleCheck' : 'CircleClose'"
+              @click="handleStatusChange(scope.row)"
+          >
+            {{ scope.row.status === 1 ? '启用' : '停用' }}
+          </el-button>
         </template>
       </el-table-column>
     </el-table>
@@ -97,19 +107,29 @@
     />
 
     <!-- 添加或修改出版社对话框 -->
-    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
+    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
       <el-form ref="publisherFormRef" :model="form" :rules="rules" label-width="100px">
         <el-form-item label="出版社名称" prop="name">
           <el-input v-model="form.name" placeholder="请输入出版社名称" />
         </el-form-item>
-        <el-form-item label="出版社地址" prop="address">
-          <el-input v-model="form.address" placeholder="请输入出版社地址" />
+        <el-form-item label="出版社地址" prop="ioc">
+          <el-input v-model="form.ioc" placeholder="请输入出版社地址"/>
+        </el-form-item>
+        <el-form-item label="出版社别名" prop="aliasStr">
+          <el-input
+              v-model="form.aliasStr"
+              type="textarea"
+              placeholder="请输入出版社别名，每行一个"
+              :rows="10"
+              wrap="off"
+          />
         </el-form-item>
         <el-form-item label="状态">
           <el-radio-group v-model="form.status">
             <el-radio
-              v-for="dict in statusOptions"
+                v-for="dict in sys_normal_disable"
               :key="dict.value"
+                :value="dict.value"
               :label="dict.value"
             >{{ dict.label }}</el-radio>
           </el-radio-group>
@@ -124,7 +144,7 @@
     </el-dialog>
 
     <!-- 出版社合并对话框 -->
-    <el-dialog title="出版社合并" v-model="mergeOpen" width="500px" append-to-body>
+    <el-dialog title="出版社合并" v-model="mergeOpen" width="600px" append-to-body>
       <div v-if="selectedPublishers.length < 2" class="merge-warning">
         请至少选择两个出版社进行合并操作
       </div>
@@ -142,9 +162,9 @@
             </template>
           </el-autocomplete>
         </el-form-item>
-        <el-form-item label="出版社地址" prop="address">
+        <el-form-item label="出版社地址" prop="ioc">
           <el-autocomplete
-            v-model="mergeData.address"
+              v-model="mergeData.ioc"
             :fetch-suggestions="queryPublisherAddresses"
             placeholder="请输入或选择合并后的出版社地址"
             clearable
@@ -155,6 +175,15 @@
             </template>
           </el-autocomplete>
         </el-form-item>
+        <el-form-item label="出版社别名" prop="aliasStr">
+          <el-input
+              v-model="mergeData.aliasStr"
+              type="textarea"
+              placeholder="请输入出版社别名，每行一个"
+              :rows="10"
+              wrap="off"
+          />
+        </el-form-item>
       </el-form>
       <template #footer>
         <div class="dialog-footer">
@@ -163,12 +192,36 @@
         </div>
       </template>
     </el-dialog>
+
+    <!-- 错误信息弹窗 -->
+    <result-log
+        v-if="resultDialogOpen"
+        :log-data="resultDialogData"
+        curr-exp-type="Publisher"
+    >
+    </result-log>
   </div>
 </template>
 
 <script setup>
-import { ref, reactive, onMounted } from 'vue'
-import { ElMessage, ElMessageBox } from 'element-plus'
+import {getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from 'vue'
+import {Fold} from '@element-plus/icons-vue'
+import Pagination from '@/components/Pagination'
+import RightToolbar from '@/components/RightToolbar'
+import ResultLog from "@/components/ResultLog/index.vue";
+
+import {
+  changePublisherStatus,
+  getPublisher,
+  listPublisher,
+  mergePublisher,
+  updatePublisher
+} from '@/api/article/publisher'
+
+const {proxy} = getCurrentInstance()
+
+// 字典数据
+const {sys_normal_disable} = proxy.useDict('sys_normal_disable')
 
 // 显示搜索条件
 const showSearch = ref(true)
@@ -194,32 +247,52 @@ const open = ref(false)
 const mergeOpen = ref(false)
 // 日期范围
 const dateRange = ref([])
-// 状态数据字典
-const statusOptions = [
-  { value: "1", label: "正常" },
-  { value: "0", label: "停用" }
-]
-
-// 查询参数
-const queryParams = reactive({
-  pageNum: 1,
-  pageSize: 10,
-  name: undefined,
-  status: undefined
+// 错误信息弹窗
+const resultDialogOpen = ref(false)
+const resultDialogData = ref([])
+
+// 数据定义
+const data = reactive({
+  queryParams: {
+    pageNum: 1,
+    pageSize: 10,
+    name: undefined,
+    status: undefined
+  },
+  form: {
+    id: undefined,
+    name: undefined,
+    ioc: undefined,
+    status: "1",
+    alias: [],
+    aliasStr: undefined
+  },
+  mergeData: {
+    name: undefined,
+    ioc: undefined,
+    alias: [],
+    aliasStr: undefined
+  }
 })
 
-// 表单参数
-const form = reactive({
-  id: undefined,
-  name: undefined,
-  address: undefined,
-  status: "1"
+const {queryParams, form, mergeData} = toRefs(data)
+
+// 监听合并表单别名字符串变化，切割为数组
+watch(() => mergeData.value.aliasStr, (newVal) => {
+  if (newVal) {
+    mergeData.value.alias = newVal.split('\n').filter(item => item.trim() !== '')
+  } else {
+    mergeData.value.alias = []
+  }
 })
 
-// 合并表单参数
-const mergeData = reactive({
-  name: undefined,
-  address: undefined
+// 监听编辑表单别名字符串变化，切割为数组
+watch(() => form.value.aliasStr, (newVal) => {
+  if (newVal) {
+    form.value.alias = newVal.split('\n').filter(item => item.trim() !== '')
+  } else {
+    form.value.alias = []
+  }
 })
 
 // 表单校验
@@ -229,15 +302,16 @@ const rules = reactive({
   ]
 })
 
-// 表单引用
-const publisherFormRef = ref(null)
-const mergeFormRef = ref(null)
-const queryForm = ref(null)
+
 
 // 出版社名称建议
 function queryPublisherNames(queryString, cb) {
-  const suggestions = selectedPublishers.value.map(publisher => ({
-    value: publisher.name
+  const uniqueNames = [...new Set(selectedPublishers.value
+      .filter(publisher => publisher.name) // 过滤掉空名称
+      .map(publisher => publisher.name))]; // 去重
+
+  const suggestions = uniqueNames.map(name => ({
+    value: name
   }));
 
   const results = queryString
@@ -249,8 +323,12 @@ function queryPublisherNames(queryString, cb) {
 
 // 出版社地址建议
 function queryPublisherAddresses(queryString, cb) {
-  const suggestions = selectedPublishers.value.map(publisher => ({
-    value: publisher.address
+  const uniqueAddresses = [...new Set(selectedPublishers.value
+      .filter(publisher => publisher.ioc) // 过滤掉空地址
+      .map(publisher => publisher.ioc))]; // 去重
+
+  const suggestions = uniqueAddresses.map(address => ({
+    value: address
   }));
 
   const results = queryString
@@ -260,51 +338,18 @@ function queryPublisherAddresses(queryString, cb) {
   cb(results);
 }
 
-// 模拟数据
-const mockPublishers = [
-  { id: 1, name: "科学出版社", address: "北京市东城区东黄城根北街16号", journalCount: 35, updateTime: "2023-06-15 14:30:22", status: "1" },
-  { id: 2, name: "高等教育出版社", address: "北京市西城区德外大街4号", journalCount: 28, updateTime: "2023-05-20 09:15:47", status: "1" },
-  { id: 3, name: "人民教育出版社", address: "北京市海淀区中关村南大街17号", journalCount: 42, updateTime: "2023-07-05 16:45:33", status: "1" },
-  { id: 4, name: "商务印书馆", address: "北京市东城区王府井大街36号", journalCount: 19, updateTime: "2023-04-30 11:20:18", status: "0" },
-  { id: 5, name: "中华书局", address: "北京市东城区朝阳门内大街137号", journalCount: 24, updateTime: "2023-08-10 10:05:56", status: "1" },
-  { id: 6, name: "电子工业出版社", address: "北京市海淀区万寿路南口金家村288号", journalCount: 31, updateTime: "2023-09-01 13:40:12", status: "1" },
-  { id: 7, name: "机械工业出版社", address: "北京市西城区百万庄大街22号", journalCount: 26, updateTime: "2023-07-18 08:55:39", status: "0" }
-]
+
 
 /** 查询出版社列表 */
 function getList() {
   loading.value = true
-  // 模拟后端查询
-  setTimeout(() => {
-    // 过滤数据
-    let filteredList = [...mockPublishers]
-
-    if (queryParams.name) {
-      filteredList = filteredList.filter(item => item.name.includes(queryParams.name))
-    }
-
-    if (queryParams.status) {
-      filteredList = filteredList.filter(item => item.status === queryParams.status)
-    }
-
-    if (dateRange.value && dateRange.value.length === 2) {
-      const startDate = new Date(dateRange.value[0])
-      const endDate = new Date(dateRange.value[1])
-      filteredList = filteredList.filter(item => {
-        const updateTime = new Date(item.updateTime)
-        return updateTime >= startDate && updateTime <= endDate
-      })
-    }
-
-    total.value = filteredList.length
-
-    // 分页
-    const start = (queryParams.pageNum - 1) * queryParams.pageSize
-    const end = start + queryParams.pageSize
-    publisherList.value = filteredList.slice(start, end)
-
+  listPublisher(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
+    publisherList.value = response.rows
+    total.value = response.total
     loading.value = false
-  }, 300)
+  }).catch(() => {
+    loading.value = false
+  })
 }
 
 /** 取消按钮 */
@@ -316,35 +361,28 @@ function cancel() {
 /** 取消合并按钮 */
 function cancelMerge() {
   mergeOpen.value = false
-  mergeData.name = undefined
-  mergeData.address = undefined
+  mergeData.value.name = undefined
+  mergeData.value.ioc = undefined
+  mergeData.value.alias = []
+  mergeData.value.aliasStr = undefined
 }
 
 /** 表单重置 */
 function resetForm() {
-  if (publisherFormRef.value) {
-    publisherFormRef.value.resetFields()
-  }
-  form.id = undefined
-  form.name = undefined
-  form.address = undefined
-  form.status = "1"
-  form.remark = undefined
+  proxy.$refs.publisherFormRef?.resetFields()
 }
 
 /** 搜索按钮操作 */
 function handleQuery() {
-  queryParams.pageNum = 1
+  queryParams.value.pageNum = 1
   getList()
 }
 
 /** 重置按钮操作 */
 function resetQuery() {
   dateRange.value = []
-  if (queryForm.value) {
-    queryForm.value.resetFields()
-  }
-  queryParams.pageNum = 1
+  proxy.$refs.queryForm?.resetFields()
+  queryParams.value.pageNum = 1
   handleQuery()
 }
 
@@ -356,50 +394,46 @@ function handleSelectionChange(selection) {
   multiple.value = !selection.length
 }
 
-/** 新增按钮操作 */
-function handleAdd() {
-  resetForm()
-  open.value = true
-  title.value = "添加出版社"
-}
+
 
 /** 修改按钮操作 */
 function handleUpdate(row) {
   resetForm()
   const publisherId = row.id || ids.value[0]
-  const publisher = mockPublishers.find(item => item.id === publisherId)
-
-  Object.assign(form, publisher)
-  open.value = true
-  title.value = "修改出版社"
+  getPublisher(publisherId).then(response => {
+    Object.assign(form.value, response.data)
+    // 确保status是字符串类型
+    if (form.value.status !== undefined) {
+      form.value.status = String(form.value.status)
+    }
+    // 将alias数组转换为字符串
+    if (form.value.alias && Array.isArray(form.value.alias)) {
+      form.value.aliasStr = form.value.alias.join('\n')
+    }
+    open.value = true
+    title.value = "修改出版社"
+  })
 }
 
 /** 提交按钮 */
 function submitForm() {
-  if (!publisherFormRef.value) return
-
-  publisherFormRef.value.validate(valid => {
+  proxy.$refs.publisherFormRef?.validate(valid => {
     if (valid) {
-      if (form.id) {
-        // 模拟修改
-        const index = mockPublishers.findIndex(item => item.id === form.id)
-        if (index !== -1) {
-          mockPublishers[index] = { ...mockPublishers[index], ...form }
-        }
-        ElMessage.success("修改成功")
-      } else {
-        // 模拟新增
-        const newPublisher = {
-          id: mockPublishers.length + 1,
-          ...form,
-          journalCount: 0,
-          updateTime: new Date().toLocaleString()
+      updatePublisher(form.value).then(response => {
+        let data = response.data;
+        if (data && Array.isArray(data) && data.length > 0) {
+          // 有错误信息，展示错误弹窗
+          resultDialogData.value = data;
+          resultDialogOpen.value = true;
+        } else {
+          // 修改成功
+          proxy.$modal.msgSuccess("修改成功")
+          open.value = false
+          getList()
         }
-        mockPublishers.push(newPublisher)
-        ElMessage.success("新增成功")
-      }
-      open.value = false
-      getList()
+      }).catch(error => {
+        console.error('修改出版社失败:', error);
+      })
     }
   })
 }
@@ -407,24 +441,37 @@ function submitForm() {
 /** 合并按钮操作 */
 function handleMerge() {
   if (ids.value.length < 2) {
-    ElMessage.warning("请至少选择两个出版社进行合并操作")
+    proxy.$modal.msgWarning("请至少选择两个出版社进行合并操作")
     return
   }
   mergeOpen.value = true
-  mergeData.name = undefined
-  mergeData.address = undefined
+  mergeData.value.name = undefined
+  mergeData.value.ioc = undefined
+  mergeData.value.alias = []
+  mergeData.value.aliasStr = undefined
 
   // 默认使用第一个选中的出版社作为名称和地址的初始值
   if (selectedPublishers.value.length > 0) {
-    mergeData.name = selectedPublishers.value[0].name
-    mergeData.address = selectedPublishers.value[0].address
+    mergeData.value.name = selectedPublishers.value[0].name
+    mergeData.value.ioc = selectedPublishers.value[0].ioc
+
+    // 收集所有选中出版社的别名
+    const allAliases = []
+    selectedPublishers.value.forEach(publisher => {
+      if (publisher.alias && Array.isArray(publisher.alias)) {
+        allAliases.push(...publisher.alias)
+      }
+    })
+    // 去重并设置到textarea
+    const uniqueAliases = [...new Set(allAliases)]
+    mergeData.value.aliasStr = uniqueAliases.join('\n')
   }
 }
 
 /** 提交合并 */
 function submitMerge() {
-  if (!mergeData.name) {
-    ElMessage.warning("请输入合并后的出版社名称")
+  if (!mergeData.value.name) {
+    proxy.$modal.msgWarning("请输入合并后的出版社名称")
     return
   }
 
@@ -433,62 +480,48 @@ function submitMerge() {
   // 其余出版社作为源出版社
   const sourcePublisherIds = ids.value.filter(id => id !== targetPublisherId)
 
-  ElMessageBox.confirm(
-    '确认要将选中的出版社合并吗？合并后无法恢复',
-    '警告',
-    {
-      confirmButtonText: '确定',
-      cancelButtonText: '取消',
-      type: 'warning',
-    }
-  ).then(() => {
-    // 模拟合并操作
-    const targetPublisher = mockPublishers.find(item => item.id === targetPublisherId)
-
-    if (targetPublisher) {
-      // 更新目标出版社信息
-      targetPublisher.name = mergeData.name
-      targetPublisher.address = mergeData.address || targetPublisher.address
-
-      // 更新目标出版社期刊数量
-      const journalCount = mockPublishers.filter(item => !sourcePublisherIds.includes(item.id))
-        .reduce((sum, item) => sum + item.journalCount, 0)
-      targetPublisher.journalCount = journalCount
-      targetPublisher.updateTime = new Date().toLocaleString()
-
-      // 删除源出版社
-      const newPublisherList = mockPublishers.filter(item => !sourcePublisherIds.includes(item.id))
-      mockPublishers.length = 0
-      mockPublishers.push(...newPublisherList)
-
-      ElMessage.success("合并成功")
-      mergeOpen.value = false
-      getList()
-    }
+  const mergeParams = {
+    targetId: targetPublisherId,
+    sourceIds: sourcePublisherIds,
+    name: mergeData.value.name,
+    ioc: mergeData.value.ioc,
+    alias: mergeData.value.alias
+  }
+
+  proxy.$modal.confirm('确认要将选中的出版社合并吗？合并后无法恢复', '警告').then(() => {
+    mergePublisher(mergeParams).then(response => {
+      let data = response.data;
+      if (data && Array.isArray(data) && data.length > 0) {
+        // 有错误信息，展示错误弹窗
+        resultDialogData.value = data;
+        resultDialogOpen.value = true;
+      } else {
+        // 合并成功
+        proxy.$modal.msgSuccess("合并成功")
+        mergeOpen.value = false
+        // 清空选择状态
+        ids.value = []
+        selectedPublishers.value = []
+        single.value = true
+        multiple.value = true
+        getList()
+      }
+    }).catch(error => {
+      console.error('合并出版社失败:', error);
+    })
   }).catch(() => {})
 }
 
 /** 状态修改 */
 function handleStatusChange(row) {
   const text = row.status === "0" ? "启用" : "停用"
+  const newStatus = row.status === 0 ? 1 : 0
 
-  ElMessageBox.confirm(
-    `确认要${text}"${row.name}"出版社吗？`,
-    '警告',
-    {
-      confirmButtonText: '确定',
-      cancelButtonText: '取消',
-      type: 'warning',
-    }
-  ).then(() => {
-    // 模拟状态修改
-    const index = mockPublishers.findIndex(item => item.id === row.id)
-    if (index !== -1) {
-      mockPublishers[index].status = row.status === "0" ? "1" : "0"
-      mockPublishers[index].updateTime = new Date().toLocaleString()
-      ElMessage.success(`${text}成功`)
+  proxy.$modal.confirm(`确认要${text}"${row.name}"出版社吗？`, '警告').then(() => {
+    changePublisherStatus(row.id, newStatus).then(() => {
+      proxy.$modal.msgSuccess(`${text}成功`)
       getList()
-    }
+    })
   }).catch(() => {})
 }
 
@@ -527,4 +560,13 @@ onMounted(() => {
       width: 200px;
     }
 }
+
+.more-icon {
+  top: 2px;
+  margin-left: 4px;
+  font-size: 14px;
+  color: #7C9EFF;
+  cursor: pointer;
+}
+
 </style>
diff --git a/plosp-admin/src/main/java/org/biosino/lf/plosp/web/controller/article/PublisherController.java b/plosp-admin/src/main/java/org/biosino/lf/plosp/web/controller/article/PublisherController.java
index b7f475b..bde9dd1 100644
--- a/plosp-admin/src/main/java/org/biosino/lf/plosp/web/controller/article/PublisherController.java
+++ b/plosp-admin/src/main/java/org/biosino/lf/plosp/web/controller/article/PublisherController.java
@@ -1,15 +1,20 @@
 package org.biosino.lf.plosp.web.controller.article;
 
+import cn.hutool.core.collection.CollUtil;
 import lombok.RequiredArgsConstructor;
 import org.biosino.lf.pds.article.domain.Publisher;
 import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
 import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
 import org.biosino.lf.pds.article.service.IPublisherService;
+import org.biosino.lf.pds.article.vo.ErrorMsgVO;
 import org.biosino.lf.pds.common.core.controller.BaseController;
 import org.biosino.lf.pds.common.core.domain.AjaxResult;
 import org.biosino.lf.pds.common.core.page.TableDataInfo;
+import org.springframework.validation.annotation.Validated;
 import org.springframework.web.bind.annotation.*;
 
+import java.util.ArrayList;
+import java.util.Arrays;
 import java.util.List;
 
 /**
@@ -37,34 +42,49 @@ public class PublisherController extends BaseController {
      */
     @GetMapping("/{id}")
     public AjaxResult getInfo(@PathVariable Long id) {
-        return success(publisherService.selectPublisherById(id));
+        Publisher publisher = publisherService.selectPublisherById(id);
+        return success(publisher);
     }
 
 
-
     /**
      * 修改出版社
      */
     @PutMapping
     public AjaxResult edit(@RequestBody Publisher publisher) {
-        return toAjax(publisherService.updatePublisher(publisher));
-    }
+        // 校验别名是否已存在
+        if (CollUtil.isNotEmpty(publisher.getAlias())) {
+            List<ErrorMsgVO> errors = publisherService.validateAlias(publisher.getAlias(), Arrays.asList(publisher.getId()));
+            if (CollUtil.isNotEmpty(errors)) {
+                return AjaxResult.success(errors);
+            }
+        }
 
-    /**
-     * 删除出版社
-     */
-    @DeleteMapping("/{ids}")
-    public AjaxResult remove(@PathVariable List<Long> ids) {
-        return toAjax(publisherService.deletePublisherByIds(ids));
+        publisherService.updatePublisher(publisher);
+        return AjaxResult.success();
     }
 
+
     /**
      * 合并出版社
      */
     @PostMapping("/merge")
-    public AjaxResult merge(@RequestBody PublisherMergeDTO mergeDTO) {
+    public AjaxResult merge(@RequestBody @Validated PublisherMergeDTO mergeDTO) {
+        // 校验别名是否已存在
+        if (CollUtil.isNotEmpty(mergeDTO.getAlias())) {
+            // 排除目标出版社和源出版社的ID
+            List<Long> excludeIds = new ArrayList<>();
+            excludeIds.add(mergeDTO.getTargetId());
+            excludeIds.addAll(mergeDTO.getSourceIds());
+
+            List<ErrorMsgVO> errors = publisherService.validateAlias(mergeDTO.getAlias(), excludeIds);
+            if (CollUtil.isNotEmpty(errors)) {
+                return AjaxResult.success(errors);
+            }
+        }
+
         publisherService.mergePublishers(mergeDTO);
-        return success();
+        return AjaxResult.success();
     }
 
     /**
@@ -73,6 +93,6 @@ public class PublisherController extends BaseController {
     @PostMapping("/changeStatus")
     public AjaxResult changeStatus(@RequestParam Long id, @RequestParam Integer status) {
         publisherService.updatePublisherStatus(id, status);
-        return success();
+        return AjaxResult.success();
     }
 }
