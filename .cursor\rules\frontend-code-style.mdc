---
description:
globs:
alwaysApply: true
---
# 前端代码风格

本项目前端使用Vue 3和Element Plus构建，遵循以下编码规范：

## 整体架构

- 采用Vue 3的组合式API (Composition API)
- 使用`<script setup>`语法糖简化代码结构
- 使用Pinia进行状态管理
- 使用Vue Router管理路由
- 使用Element Plus作为UI组件库

## 组件结构

每个Vue组件通常按照以下结构组织：
1. `<template>` - HTML模板
2. `<script setup>` - JavaScript逻辑
3. `<style>` - CSS样式（可选）

```vue
<template>
  <!-- HTML模板 -->
</template>

<script setup name="User">
  // 引入依赖
  // 定义状态和方法
  // 声明生命周期钩子
</script>

<style>
  /* CSS样式 */
</style>
```

## 状态管理

1. 使用`ref`和`reactive`进行响应式状态管理：
```js
// 简单值使用ref
const loading = ref(true);
const showSearch = ref(true);

// 对象使用reactive
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20
  }
});

// 解构reactive对象供模板使用
const { queryParams, form } = toRefs(data);
```

2. 使用Pinia管理全局状态：
```js
import useAppStore from '@/store/modules/app';
const appStore = useAppStore();
```

## 表单处理

1. 表单验证规则：
```js
rules: {
  userName: [
    { required: true, message: '用户名称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ]
}
```

2. 表单提交：
```js
function submitForm() {
  proxy.$refs['userRef'].validate(valid => {
    if (valid) {
      // 表单验证通过
      updateUser(form.value).then(response => {
        proxy.$modal.msgSuccess('修改成功');
        open.value = false;
        getList();
      });
    }
  });
}
```

## API调用

1. 将API调用封装在单独的模块中：
```js
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser
} from '@/api/system/user';
```

2. 在组件中调用API：
```js
function getList() {
  loading.value = true;
  listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(
    res => {
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
    }
  );
}
```

## 事件处理

1. 使用函数声明处理事件：
```js
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleDelete(row) {
  const userIds = row.userId || ids.value;
  proxy.$modal
    .confirm('是否确认删除用户编号为"' + userIds + '"的数据项？')
    .then(function () {
      return delUser(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    });
}
```

2. 监听状态变化：
```js
watch(deptName, val => {
  proxy.$refs['deptTreeRef'].filter(val);
});
```

## 组件通信

1. 使用`v-model`进行双向绑定：
```html
<pagination
  v-show="total > 0"
  v-model:page="queryParams.pageNum"
  v-model:limit="queryParams.pageSize"
  :total="total"
  @pagination="getList"
/>
```

2. 使用`props`和事件进行父子组件通信：
```html
<right-toolbar
  v-model:show-search="showSearch"
  :columns="columns"
  @query-table="getList"
></right-toolbar>
```

## UI组件使用

1. 表格组件：
```html
<el-table
  v-loading="loading"
  :data="userList"
  @selection-change="handleSelectionChange"
>
  <el-table-column type="selection" width="50" align="center" />
  <el-table-column label="用户名称" align="center" prop="userName" />
  <!-- 其他列 -->
</el-table>
```

2. 表单组件：
```html
<el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
  <el-row>
    <el-col :span="12">
      <el-form-item label="用户昵称" prop="nickName">
        <el-input v-model="form.nickName" placeholder="请输入用户昵称" />
      </el-form-item>
    </el-col>
  </el-row>
</el-form>
```

3. 对话框组件：
```html
<el-dialog v-model="open" :title="title" width="600px" append-to-body>
  <!-- 对话框内容 -->
  <template #footer>
    <div class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </template>
</el-dialog>
```

## 工具函数使用

1. 使用全局挂载的工具函数：
```js
// 日期范围处理
proxy.addDateRange(queryParams.value, dateRange.value)

// 重置表单
proxy.resetForm('queryRef')

// 消息提示
proxy.$modal.msgSuccess('删除成功')

// 确认对话框
proxy.$modal.confirm('是否确认删除?')
```

2. 使用字典数据：
```js
const { sys_normal_disable, sys_user_sex } = proxy.useDict(
  'sys_normal_disable',
  'sys_user_sex'
);
```

## 项目工具类（ruoyi.js 和 index.js）

本项目包含两个主要的工具类文件，提供了丰富的辅助函数：

### ruoyi.js 核心工具函数

1. 日期格式化：
```js
import { parseTime } from '@/utils/ruoyi';

// 格式化日期
parseTime(new Date(), '{y}-{m}-{d}'); // 2023-05-20
parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'); // 2023-05-20 14:30:45
```

2. 表单操作：
```js
// 重置表单
resetForm('formRef');

// 添加日期范围到查询参数
const params = addDateRange(queryParams, dateRange);
// 结果：params.params = { beginTime: '2023-01-01', endTime: '2023-12-31' }
```

3. 字典数据处理：
```js
// 回显单个字典项
const status = selectDictLabel(sys_normal_disable, '0'); // '正常'

// 回显多个字典项
const roles = selectDictLabels(sys_role_options, '1,2', ','); // '管理员,普通用户'
```

4. 树结构数据处理：
```js
// 构造树结构
const treeData = handleTree(flatData, 'id', 'parentId');
/*
[
  {
    id: 1,
    name: '父节点',
    children: [
      { id: 2, name: '子节点', parentId: 1 }
    ]
  }
]
*/
```

### index.js 扩展工具函数

1. 时间格式化：
```js
// 格式化日期时间
formatDate('2023-05-20T14:30:45'); // '2023-05-20 14:30:45'

// 相对时间格式化
formatTime(Date.now() - 3600 * 1000); // '1小时前'
```

2. URL参数处理：
```js
// URL参数转对象
const query = param2Obj('https://example.com?id=1&name=test');
// { id: '1', name: 'test' }

// 对象转URL参数
const str = param({ id: 1, name: 'test' }); // 'id=1&name=test'
```

3. 对象和数组操作：
```js
// 深拷贝对象
const newObj = deepClone(originalObj);

// 数组去重
const uniqueArray = uniqueArr([1, 2, 2, 3, 3, 3]);
// [1, 2, 3]
```

4. 字符串处理：
```js
// 首字母大写
titleCase('hello world'); // 'Hello World'

// 下划线转驼峰
camelCase('user_name'); // 'userName'

// 去除两边空格
trimStr(' hello '); // 'hello'
```

5. 防抖函数：
```js
// 函数防抖
const debouncedFn = debounce(() => {
  console.log('执行搜索');
}, 300);

// 在输入事件中调用
searchInput.addEventListener('input', debouncedFn);
```

### 工具类使用最佳实践

1. 按需导入工具函数，避免全局导入：
```js
// 推荐
import { parseTime, selectDictLabel } from '@/utils/ruoyi';

// 避免
import * as ruoyiUtil from '@/utils/ruoyi';
```

2. 优先使用工具类中的方法，避免重复实现相同功能：
```js
// 推荐
import { deepClone } from '@/utils/index';
const newObj = deepClone(originalObj);

// 避免
const newObj = JSON.parse(JSON.stringify(originalObj)); // 不处理特殊情况
```

3. 开发新功能时，考虑是否可以扩展现有工具类，保持一致性：
```js
// 在utils/index.js中扩展
export function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
```

## 命名规范

1. 组件名：使用PascalCase
2. 实例名：使用camelCase
3. 事件处理函数：使用handle前缀
4. 属性名：使用camelCase
5. 常量：使用全大写下划线分隔

## 代码格式

1. 使用2个空格作为缩进
2. 使用单引号作为字符串引号
3. 大括号前有空格
4. 组件顶级标签之间有空行





