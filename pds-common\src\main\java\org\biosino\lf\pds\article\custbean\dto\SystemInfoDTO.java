package org.biosino.lf.pds.article.custbean.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 系统信息DTO
 * 用于解析system_info()函数获取的系统信息JSON数据
 * 对应Python函数中的返回值 {"cpu": cpu_info(), "memory": memory_info(), "disk": disk_info()}
 *
 * <AUTHOR>
 */
@Data
public class SystemInfoDTO {
    /**
     * CPU信息
     * 对应Python函数中的cpu_info()返回值
     */
    private CpuInfoDTO cpu;

    /**
     * 内存信息
     * 对应Python函数中的memory_info()返回值
     */
    private MemoryInfoDTO memory;

    /**
     * 磁盘信息
     * 对应Python函数中的disk_info()返回值，获取当前脚本运行目录所在磁盘的信息
     */
    private DiskInfoDTO disk;

    /**
     * CPU信息DTO
     * 对应Python的cpu_info()函数返回值
     */
    @Data
    public static class CpuInfoDTO {
        /**
         * CPU逻辑核心数量
         * 对应Python中的logical_count字段
         */
        @JSONField(name = "logical_count")
        private Integer logicalCount;

        /**
         * CPU物理核心数量
         * 对应Python中的physical_count字段
         */
        @JSONField(name = "physical_count")
        private Integer physicalCount;

        /**
         * 当前CPU频率（单位：MHz）
         * 对应Python中的freq_current字段
         */
        @JSONField(name = "freq_current")
        private Double freqCurrent;

        /**
         * CPU最小频率（单位：MHz）
         * 对应Python中的freq_min字段
         */
        @JSONField(name = "freq_min")
        private Double freqMin;

        /**
         * CPU最大频率（单位：MHz）
         * 对应Python中的freq_max字段
         */
        @JSONField(name = "freq_max")
        private Double freqMax;
    }

    /**
     * 内存信息DTO
     * 对应Python的memory_info()函数返回值
     */
    @Data
    public static class MemoryInfoDTO {
        /**
         * 总内存大小（字节）
         * 对应Python中的total字段
         */
        private Long total;

        /**
         * 可用内存大小（字节）
         * 对应Python中的available字段
         */
        private Long available;

        /**
         * 已使用内存大小（字节）
         * 对应Python中的used字段
         */
        private Long used;

        /**
         * 空闲内存大小（字节）
         * 对应Python中的free字段
         */
        private Long free;

        /**
         * 内存使用率（百分比）
         * 对应Python中的percent字段
         */
        private Double percent;
    }

    /**
     * 磁盘信息DTO
     * 对应Python的disk_info()函数返回值
     */
    @Data
    public static class DiskInfoDTO {
        /**
         * 磁盘总容量（字节）
         * 对应Python中的total字段
         */
        private Long total;

        /**
         * 已使用磁盘空间（字节）
         * 对应Python中的used字段
         */
        private Long used;

        /**
         * 可用磁盘空间（字节）
         * 对应Python中的free字段
         */
        private Long free;

        /**
         * 磁盘使用率（百分比）
         * 对应Python中的percent字段
         */
        private Double percent;
    }
} 