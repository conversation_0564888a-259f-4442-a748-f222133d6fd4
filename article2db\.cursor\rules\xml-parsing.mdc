---
description: 
globs: 
alwaysApply: true
---
# XML解析指南

本项目中的XML解析是核心功能，主要处理三种来源的XML文件：PubMed、PMC和BioRxiv。

## 解析接口

所有XML解析器都实现了[XmlParseService](mdc:src/main/java/org/biosino/lf/pds/article/service/XmlParseService.java)接口，该接口定义了基本的解析方法：

```java
public interface XmlParseService {
    ArticleInfoDTO parse(String content);
}
```

## 解析实现

### PubMed XML解析

[PubmedXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/PubmedXmlParse.java)负责解析PubMed格式的XML文件，主要处理以下内容：
- 文章基本信息（标题、作者、摘要等）
- 发布日期信息
- MeSH术语
- 化学物质
- 引用文献

### PMC XML解析

[PmcXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/PmcXmlParse.java)负责解析PMC格式的XML文件，主要特点：
- 更详细的文章内容
- 更完整的作者和机构信息
- 完整的引用文献列表

### BioRxiv XML解析

[BioRxivXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/BioRxivXmlParse.java)负责解析BioRxiv预印本服务器的XML文件。

## 日期处理

日期处理是XML解析中的重要部分：
- 电子出版日期(epub)和印刷出版日期(ppub)的处理
- 优先从印刷出版日期(ppub)获取Published日期
- 如果ppub中没有值，则从电子出版日期(epub)获取
- 日期格式的标准化和转换

## 解析结果

所有解析器都返回[ArticleInfoDTO](mdc:src/main/java/org/biosino/lf/pds/article/dto/ArticleInfoDTO.java)对象，包含完整的文章信息，包括：
- 文章基本信息
- 作者和机构
- 引用文献
- 关键词和MeSH术语
- 发布日期

