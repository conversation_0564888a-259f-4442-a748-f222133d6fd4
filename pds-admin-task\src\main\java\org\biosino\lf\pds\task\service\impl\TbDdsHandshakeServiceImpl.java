package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.api.HandshakeDTO;
import org.biosino.lf.pds.article.custbean.dto.SystemInfoDTO;
import org.biosino.lf.pds.article.custbean.vo.api.ApiResultVO;
import org.biosino.lf.pds.article.domain.TbDdsHandshake;
import org.biosino.lf.pds.article.mapper.TbDdsHandshakeMapper;
import org.biosino.lf.pds.common.utils.ip.IpUtils;
import org.biosino.lf.pds.task.service.ITbDdsHandshakeService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 握手信息服务层实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsHandshakeServiceImpl extends ServiceImpl<TbDdsHandshakeMapper, TbDdsHandshake> implements ITbDdsHandshakeService {

    @Override
    public ApiResultVO sendHandshake(final HandshakeDTO dto, final HttpServletRequest request) {
        log.info("收到握手请求：{}", dto);
        try {
            final String siteIdReq = dto.getSiteId();
            if (StrUtil.isBlank(siteIdReq)) {
                return new ApiResultVO("error", "站点ID参数不能为空");
            }

            // JSONObject sysInfoJSON = null;
            SystemInfoDTO systemInfoDTO = null;
            final String sysInfo = dto.getSysInfo();
            if (StrUtil.isNotBlank(sysInfo)) {
                try {
                    // sysInfoJSON = JSON.parseObject(sysInfo);
                    // 解析JSON字符串到SystemInfoDTO对象
                    systemInfoDTO = JSON.parseObject(sysInfo, SystemInfoDTO.class);
                } catch (Exception e) {
                    return new ApiResultVO("error", "sysInfo 参数格式错误，sysInfo: " + sysInfo);
                }
            }

            final int siteId = Integer.parseInt(siteIdReq);

            final TbDdsHandshake handshake = new TbDdsHandshake();
            handshake.setSiteId(siteId);
            handshake.setSiteIp(IpUtils.getIpAddr(request));
            // 设置系统信息
            handshake.setSysInfo(toJson(systemInfoDTO));
            handshake.setSignalDate(new Date());

            save(handshake);
            return new ApiResultVO("success", "操作成功");
        } catch (Exception e) {
            log.error("接收握手信息出错：", e);
            return new ApiResultVO("error", getErrMessage(e));
        }
    }

    private String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        return JSON.toJSONString(obj);
    }
}
