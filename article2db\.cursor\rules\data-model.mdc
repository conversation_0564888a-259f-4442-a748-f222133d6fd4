---
description: 
globs: 
alwaysApply: true
---
# 数据模型指南

本项目使用多个领域模型类来表示学术文章的各个方面。这些模型类之间有明确的关系和依赖。

## 核心模型

### 文章

[Article.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Article.java)是核心领域模型，包含文章的基本信息：
- PMID (PubMed ID)
- PMC ID
- DOI
- 标题
- 作者列表
- 摘要
- 发布日期
- 卷号和期号
- 页码
- 关键词

### 作者与机构

- [Author.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Author.java) - 包含作者姓名、邮箱等信息
- [Organization.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Organization.java) - 包含机构名称、地址等信息
- [ArticleAuthor.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleAuthor.java) - 文章和作者的关联，包含作者顺序、通讯作者标记等

### 引用文献

[Reference.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Reference.java)表示文章引用的其他文献：
- 引用文本
- PMID
- DOI
- PMC ID

### 日期信息

[PubMedPubDate.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/PubMedPubDate.java)表示文章的各种日期：
- 发布状态（电子出版、印刷出版、接收、修改、接受等）
- 年、月、日

## 关联模型

项目包含多个关联模型，用于表示文章与其他实体的多对多关系：

- [ArticleOtherId.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleOtherId.java) - 文章的其他标识符
- [ArticleMesh.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleMesh.java) - 文章的MeSH术语
- [ArticleSupplMesh.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleSupplMesh.java) - 文章的补充MeSH术语
- [ArticleChemical.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleChemical.java) - 文章中提到的化学物质
- [ArticleGene.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleGene.java) - 文章中提到的基因
- [ArticleGrant.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleGrant.java) - 文章的资助信息
- [ArticlePubType.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticlePubType.java) - 文章的出版类型
- [ArticleDatabank.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/ArticleDatabank.java) - 文章相关的数据库信息

## 数据传输对象

项目使用两个主要的DTO来传输和组织数据：

- [ArticleInfoDTO.java](mdc:src/main/java/org/biosino/lf/pds/article/dto/ArticleInfoDTO.java) - 包含解析XML后的完整文章信息
- [ArticleDTO.java](mdc:src/main/java/org/biosino/lf/pds/article/dto/ArticleDTO.java) - 用于服务层之间传递文章信息

