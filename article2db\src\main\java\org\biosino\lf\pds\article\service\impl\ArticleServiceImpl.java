package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.dto.ArticleDTO;
import org.biosino.lf.pds.article.dto.ArticleInfoDTO;
import org.biosino.lf.pds.article.mapper.ArticleMapper;
import org.biosino.lf.pds.article.service.*;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文章信息表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements IArticleService {

    private final PublisherService publisherService;
    private final JournalService journalService;
    private final IArticleDatabankService databankService;
    private final IArticlePubTypeService articlePubTypeService;
    private final IPubTypeService pubTypeService;
    private final IChemicalService chemicalService;
    private final IArticleChemicalService articleChemicalService;
    private final IArticleMeshService articleMeshService;
    private final IArticleSupplMeshService articleSupplMeshService;
    private final IGeneService geneService;
    private final IArticleGeneService articleGeneService;
    private final IGrantService grantService;
    private final IArticleGrantService articleGrantService;
    private final IArticleOtherIdService articleOtherIdService;
    private final IReferenceService referenceService;
    private final IAuthorService authorService;
    private final IOrganizationService organizationService;
    private final IArticleAuthorService articleAuthorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void merge(Long pmid, Long pmcid) {
        if (pmid == null && pmcid == null) {
            return;
        }
        Article exsitPubmedArticle = null;
        Article exsitPmcArticle = null;

        if (pmid != null && pmcid != null) {
            exsitPubmedArticle = this.getOne(Wrappers.<Article>lambdaQuery().eq(Article::getPmid, pmid)
                    .apply("source @> ARRAY[{0}]::text[]", SourceTypeEnums.PubMed.name()));
            exsitPmcArticle = this.getOne(Wrappers.<Article>lambdaQuery().eq(Article::getPmcId, pmcid)
                    .apply("source @> ARRAY[{0}]::text[]", SourceTypeEnums.PMC.name()));
        }

        if (exsitPubmedArticle == null && exsitPmcArticle == null) {

            List<Article> articleList = this.list(Wrappers.<Article>lambdaQuery()
                    .eq(pmid == null && pmcid != null, Article::getPmcId, pmcid)
                    .eq(pmid != null && pmcid == null, Article::getPmid, pmid));

            if (CollUtil.isEmpty(articleList)) {
                return;
            }
            if (articleList.size() < 2) {
                return;
            }
            if (articleList.size() > 2) {
                throw new ServiceException("数据合并时，发现2条以上可合并记录，需人工核实");
            }
            for (Article article : articleList) {
                if (CollUtil.containsAll(article.getSource(), CollUtil.newArrayList(SourceTypeEnums.PubMed.name()))) {
                    exsitPubmedArticle = article;
                    continue;
                }
                if (CollUtil.containsAll(article.getSource(), CollUtil.newArrayList(SourceTypeEnums.PMC.name()))) {
                    exsitPmcArticle = article;
                }
            }
        }

        if (exsitPubmedArticle == null || exsitPmcArticle == null) {
            return;
        }

        if (exsitPubmedArticle.getId().equals(exsitPmcArticle.getId())) {
            return;
        }

        ArticleDTO pubmedArticleDTO = obtainArticleDTO(exsitPubmedArticle.getId());
        ArticleDTO pmcArticleDTO = obtainArticleDTO(exsitPmcArticle.getId());

        Article pubmedArticle = pubmedArticleDTO.getArticle();
        Article pmcArticle = pmcArticleDTO.getArticle();

        // 合并Pubmed Article作为主
        if (pubmedArticle.getPmid() == null && pmcArticle.getPmid() != null) {
            pubmedArticle.setPmid(pmcArticle.getPmid());
        }
        if (pubmedArticle.getPmcId() == null && pmcArticle.getPmcId() != null) {
            pubmedArticle.setPmcId(pmcArticle.getPmcId());
        }
        if (StrUtil.isBlank(pubmedArticle.getDoi()) && StrUtil.isNotBlank(pmcArticle.getDoi())) {
            pubmedArticle.setDoi(pmcArticle.getDoi());
        }
        if (StrUtil.isNotBlank(pmcArticle.getTitle())) {
            pubmedArticle.setTitle(pmcArticle.getTitle());
        }
        if (CollUtil.isNotEmpty(pmcArticle.getAuthor())) {
            pubmedArticle.setAuthor(pmcArticle.getAuthor());
        }
        if (CollUtil.isNotEmpty(pmcArticle.getAffiliation())) {
            pubmedArticle.setAffiliation(pmcArticle.getAffiliation());
        }
        if (StrUtil.isNotBlank(pmcArticle.getArticleAbstract())) {
            pubmedArticle.setArticleAbstract(pmcArticle.getArticleAbstract());
        }
        if (StrUtil.isNotBlank(pmcArticle.getOtherAbstract())) {
            pubmedArticle.setOtherAbstract(pmcArticle.getOtherAbstract());
        }
        pubmedArticle.setSource(CollUtil.newArrayList(SourceTypeEnums.PubMed.name(), SourceTypeEnums.PMC.name()));
        // 更新pubmed
        this.updateById(pubmedArticle);
        // 删除pmc
        this.removeById(pmcArticle.getId());

        // 作者优先采用Pubmed的
        // 删除PMC的作者信息 和相关联的组织
        articleAuthorService.removeByDocId(pmcArticle.getId());

        List<Long> removeAuthorIds = new ArrayList<>();
        List<Long> removeOrganizationIds = new ArrayList<>();

        for (ArticleAuthor articleAuthor : pmcArticleDTO.getArticleAuthors()) {
            removeAuthorIds.add(articleAuthor.getAuthorId());
            if (CollUtil.isNotEmpty(articleAuthor.getOrganizations())) {
                for (Organization organization : articleAuthor.getOrganizations()) {
                    removeOrganizationIds.add(organization.getId());
                }
            }
        }

        if (CollUtil.isNotEmpty(removeAuthorIds)) {
            authorService.removeBatchByIds(removeAuthorIds.stream().sorted().collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(removeOrganizationIds)) {
            organizationService.removeBatchByIds(removeOrganizationIds.stream().sorted().collect(Collectors.toList()));
        }

        // 删除pmc的reference，采用pubmed reference
        referenceService.removeByDocId(pmcArticle.getId());

        articleOtherIdService.removeByDocId(pmcArticle.getId());
    }

    @Override
    @Retryable(value = {DeadlockLoserDataAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void saveOne(ArticleInfoDTO dto) {

        Article article = dto.getArticle();

        Long docId = null;
        String source = article.getSource().get(0);

        if (SourceTypeEnums.PubMed.name().equals(source)) {
            Long pmid = article.getPmid();
            Article pubmedArticle = this.getOne(Wrappers.<Article>lambdaQuery().eq(Article::getPmid, pmid)
                    .apply("source @> ARRAY[{0}]::text[]", SourceTypeEnums.PubMed.name()));

            if (pubmedArticle != null) {
                docId = pubmedArticle.getId();
            }
        } else if (SourceTypeEnums.PMC.name().equals(source)) {
            Long pmcid = article.getPmcId();
            Article pmcArticle = this.getOne(Wrappers.<Article>lambdaQuery().eq(Article::getPmcId, pmcid)
                    .apply("source @> ARRAY[{0}]::text[]", SourceTypeEnums.PMC.name()));

            if (pmcArticle != null) {
                docId = pmcArticle.getId();
            }
        } else if (CollUtil.contains(CollUtil.newArrayList(SourceTypeEnums.BioRxiv.name(), SourceTypeEnums.MedRxiv.name()), source)) {
            String doi = article.getDoi();
            if (StrUtil.isBlank(doi)) {
                throw new ServiceException("该预印本没有DOI");
            }
            Article pmcArticle = this.getOne(Wrappers.<Article>lambdaQuery().eq(Article::getDoi, doi)
                    .apply("source @> ARRAY[{0}]::text[]", source));

            if (pmcArticle != null) {
                docId = pmcArticle.getId();
            }
        }
        Publisher publisher = publisherService.savePublisher(dto.getPublisher());
        Long publisherId = null;
        if (publisher != null) {
            publisherId = publisher.getId();
        }
        Journal journal = journalService.saveJournal(dto.getJournal(), publisherId);

        if (journal == null) {
            throw new ServiceException("期刊为空");
        }

        Long journalId = journal.getId();

        // 如果期刊没有使用到该出版社，则删除刚才构建的出版社，防止脏数据
        if (publisherId != null && publisher.isInsert() && !journal.getPublisherId().equals(publisherId)) {
            publisherService.deletedById(publisherId);
        }
        article.setJournalId(journalId);

        if (docId != null) {
            deletedAllByDocId(source, docId);
        }

        // 不存在历史记录，完全新增
        Date now = new Date();
        if (docId == null) {
            // 新增文章
            docId = IdUtil.getSnowflakeNextId();
            article.setId(docId);
            article.setCreateTime(now);
            article.setUpdateTime(now);
            this.save(article);
        } else {
            Article exsitArticle = this.getById(docId);
            // 更新文章
            article.setId(docId);
            article.setHitNum(exsitArticle.getHitNum());
            article.setDownload(exsitArticle.getDownload());
            article.setCreateTime(exsitArticle.getCreateTime());
            article.setUpdateTime(now);
            this.updateById(article);
        }

        // 保存databank
        Set<ArticleDatabank> databanks = dto.getArticleDatabanks();
        if (CollUtil.isNotEmpty(databanks)) {
            for (ArticleDatabank databank : databanks) {
                databank.setId(IdUtil.getSnowflakeNextId());
                databank.setDocId(docId);
            }
            databankService.saveBatch(databanks);
        }

        // 保存PubType
        Set<PubType> pubTypes = dto.getPubTypes();
        if (CollUtil.isNotEmpty(pubTypes)) {
            Set<String> collect = pubTypes.stream().map(PubType::getPubType).collect(Collectors.toSet());
            List<PubType> list = pubTypeService.findByPubTypeIn(collect);
            Map<String, Long> pubTypeToIdMap = list.stream().collect(Collectors.toMap(PubType::getPubType, PubType::getId, (o, n) -> n));

            List<ArticlePubType> articlePubTypeList = new ArrayList<>();
            List<PubType> pubTypeList = new ArrayList<>();
            for (PubType pubType : pubTypes) {
                if (pubTypeToIdMap.containsKey(pubType.getPubType())) {
                    ArticlePubType articlePubType = new ArticlePubType();
                    articlePubType.setDocId(docId);
                    articlePubType.setPubtypeId(pubTypeToIdMap.get(pubType.getPubType()));
                    articlePubTypeList.add(articlePubType);
                } else {
                    long pubTypeId = IdUtil.getSnowflakeNextId();
                    pubType.setId(pubTypeId);
                    pubTypeToIdMap.put(pubType.getPubType(), pubTypeId);
                    pubTypeList.add(pubType);

                    ArticlePubType articlePubType = new ArticlePubType();
                    articlePubType.setDocId(docId);
                    articlePubType.setPubtypeId(pubTypeId);
                    articlePubTypeList.add(articlePubType);
                }
            }
            if (CollUtil.isNotEmpty(pubTypeList)) {
                pubTypeService.saveBatch(pubTypeList);
            }
            if (CollUtil.isNotEmpty(articlePubTypeList)) {
                articlePubTypeService.saveBatch(articlePubTypeList);
            }
        }

        // 保存Chemical
        Set<Chemical> chemicals = dto.getChemicals();
        if (CollUtil.isNotEmpty(chemicals)) {
            Set<String> meshIdSet = chemicals.stream().map(Chemical::getMeshId).collect(Collectors.toSet());
            List<Chemical> existChemicals = chemicalService.findByMeshIdIn(meshIdSet);
            Map<String, Long> regNumToIdMap = existChemicals.stream()
                    .collect(Collectors.toMap(x -> x.getRegistryNo() + "_" + x.getName(), Chemical::getId, (o, n) -> n));

            List<Chemical> chemicalList = new ArrayList<>();
            List<ArticleChemical> articleChemicalList = new ArrayList<>();

            for (Chemical chemical : chemicals) {
                String key = chemical.getRegistryNo() + "_" + chemical.getName();
                if (regNumToIdMap.containsKey(key)) {
                    ArticleChemical articleChemical = new ArticleChemical();
                    articleChemical.setDocId(docId);
                    articleChemical.setChemicalId(regNumToIdMap.get(key));
                    articleChemicalList.add(articleChemical);
                } else {
                    long chemicalId = IdUtil.getSnowflakeNextId();
                    chemical.setId(chemicalId);
                    regNumToIdMap.put(key, chemicalId);
                    chemicalList.add(chemical);

                    ArticleChemical articleChemical = new ArticleChemical();
                    articleChemical.setDocId(docId);
                    articleChemical.setChemicalId(chemicalId);
                    articleChemicalList.add(articleChemical);
                }
            }
            if (CollUtil.isNotEmpty(chemicalList)) {
                chemicalService.saveBatch(chemicalList);
            }
            if (CollUtil.isNotEmpty(articleChemicalList)) {
                articleChemicalService.saveBatch(articleChemicalList);
            }
        }

        // 保存ArticleMesh
        Set<ArticleMesh> articleMeshes = dto.getArticleMeshes();
        if (CollUtil.isNotEmpty(articleMeshes)) {
            for (ArticleMesh mesh : articleMeshes) {
                mesh.setId(IdUtil.getSnowflakeNextId());
                mesh.setDocId(docId);
            }
            articleMeshService.saveBatch(articleMeshes);
        }

        // 保存ArticleSupplMesh
        Set<ArticleSupplMesh> articleSupplMeshes = dto.getArticleSupplMeshes();
        if (CollUtil.isNotEmpty(articleSupplMeshes)) {
            for (ArticleSupplMesh supplMesh : articleSupplMeshes) {
                // ArticleSupplMesh没有id字段
                supplMesh.setDocId(docId);
            }
            articleSupplMeshService.saveBatch(articleSupplMeshes);
        }

        // 保存Gene
        Set<Gene> genes = dto.getGenes();
        if (CollUtil.isNotEmpty(genes)) {
            Set<String> geneSymbols = genes.stream().map(Gene::getGeneSymbol).collect(Collectors.toSet());
            List<Gene> existGenes = geneService.findByGeneSymbolIn(geneSymbols);
            Map<String, Long> symbolToIdMap = existGenes.stream()
                    .collect(Collectors.toMap(Gene::getGeneSymbol, Gene::getId, (o, n) -> n));

            List<Gene> geneList = new ArrayList<>();
            List<ArticleGene> articleGeneList = new ArrayList<>();

            for (Gene gene : genes) {
                if (symbolToIdMap.containsKey(gene.getGeneSymbol())) {
                    ArticleGene articleGene = new ArticleGene();
                    articleGene.setDocId(docId);
                    articleGene.setGeneId(symbolToIdMap.get(gene.getGeneSymbol()));
                    articleGeneList.add(articleGene);
                } else {
                    long geneId = IdUtil.getSnowflakeNextId();
                    gene.setId(geneId);
                    symbolToIdMap.put(gene.getGeneSymbol(), geneId);
                    geneList.add(gene);

                    ArticleGene articleGene = new ArticleGene();

                    articleGene.setDocId(docId);
                    articleGene.setGeneId(geneId);
                    articleGeneList.add(articleGene);
                }
            }
            if (CollUtil.isNotEmpty(geneList)) {
                geneService.saveBatch(geneList);
            }
            if (CollUtil.isNotEmpty(articleGeneList)) {
                articleGeneService.saveBatch(articleGeneList);
            }
        }

        // 保存Grant
        Set<Grant> grants = dto.getGrants();
        if (CollUtil.isNotEmpty(grants)) {
            Set<String> grantIds = grants.stream().map(Grant::getGrantId).collect(Collectors.toSet());
            List<Grant> existGrants = grantService.findByGrantIdIn(grantIds);
            Map<String, Long> grantIdToIdMap = existGrants.stream()
                    .collect(Collectors.toMap(Grant::getGrantId, Grant::getId, (o, n) -> n));

            List<Grant> grantList = new ArrayList<>();
            List<ArticleGrant> articleGrantList = new ArrayList<>();

            for (Grant grant : grants) {
                if (grantIdToIdMap.containsKey(grant.getGrantId())) {
                    ArticleGrant articleGrant = new ArticleGrant();
                    // ArticleGrant使用pmid而不是docId
                    articleGrant.setDocId(docId);
                    articleGrant.setGrantId(grantIdToIdMap.get(grant.getGrantId()));
                    articleGrantList.add(articleGrant);
                } else {
                    long grantId = IdUtil.getSnowflakeNextId();
                    grant.setId(grantId);
                    grantIdToIdMap.put(grant.getGrantId(), grantId);
                    grantList.add(grant);

                    ArticleGrant articleGrant = new ArticleGrant();
                    // ArticleGrant使用pmid而不是docId
                    articleGrant.setDocId(docId);
                    articleGrant.setGrantId(grantId);
                    articleGrantList.add(articleGrant);
                }
            }
            if (CollUtil.isNotEmpty(grantList)) {
                grantService.saveBatch(grantList);
            }
            if (CollUtil.isNotEmpty(articleGrantList)) {
                articleGrantService.saveBatch(articleGrantList);
            }
        }

        // 保存ArticleOtherId
        Set<ArticleOtherId> articleOtherIds = dto.getArticleOtherIds();
        if (CollUtil.isNotEmpty(articleOtherIds)) {
            for (ArticleOtherId otherId : articleOtherIds) {
                otherId.setId(IdUtil.getSnowflakeNextId());
                otherId.setDocId(docId);
            }
            articleOtherIdService.saveBatch(articleOtherIds);
        }

        // 保存Reference
        Set<Reference> references = dto.getReferences();
        if (CollUtil.isNotEmpty(references)) {
            for (Reference reference : references) {
                reference.setId(IdUtil.getSnowflakeNextId());
                reference.setDocId(docId);
            }
            // 优化前801ms
            referenceService.saveBatch(references);
        }

        // 保存Author和ArticleAuthor
        Set<ArticleAuthor> articleAuthors = dto.getArticleAuthors();
        if (CollUtil.isNotEmpty(articleAuthors)) {
            // 使用更可靠的标识符作为键，比如作者姓名或其他唯一标识
            Map<String, Long> authorNameToIdMap = new HashMap<>();
            Map<String, Long> orgNameToIdMap = new HashMap<>();
            List<Author> authorList = new ArrayList<>();
            List<Organization> orgList = new ArrayList<>();

            // 收集所有作者和机构
            for (ArticleAuthor articleAuthor : articleAuthors) {
                if (articleAuthor.getAuthor() != null) {
                    Author author = articleAuthor.getAuthor();
                    // 使用姓名作为键
                    String authorKey = author.getLastname() + "_" + author.getForename();
                    if (!authorNameToIdMap.containsKey(authorKey)) {
                        long authorId = IdUtil.getSnowflakeNextId();
                        author.setId(authorId);
                        authorList.add(author);
                        authorNameToIdMap.put(authorKey, authorId);
                    }
                }

                if (articleAuthor.getOrganizations() != null) {
                    for (Organization org : articleAuthor.getOrganizations()) {
                        // 使用机构名称作为键
                        String orgKey = org.getName();
                        if (!orgNameToIdMap.containsKey(orgKey)) {
                            long orgId = IdUtil.getSnowflakeNextId();
                            org.setId(orgId);
                            orgList.add(org);
                            orgNameToIdMap.put(orgKey, orgId);
                        }
                    }
                }
            }

            // 保存作者和机构
            if (CollUtil.isNotEmpty(authorList)) {
                authorService.saveBatch(authorList);
            }
            if (CollUtil.isNotEmpty(orgList)) {
                organizationService.saveBatch(orgList);
            }

            // 设置ArticleAuthor关联
            for (ArticleAuthor articleAuthor : articleAuthors) {
                articleAuthor.setId(IdUtil.getSnowflakeNextId());
                articleAuthor.setDocId(docId);

                if (articleAuthor.getAuthor() != null) {
                    String authorKey = articleAuthor.getAuthor().getLastname() + "_" + articleAuthor.getAuthor().getForename();
                    articleAuthor.setAuthorId(authorNameToIdMap.get(authorKey));
                }

                if (articleAuthor.getOrganizations() != null && !articleAuthor.getOrganizations().isEmpty()) {
                    List<Long> orgIds = new ArrayList<>();
                    for (Organization org : articleAuthor.getOrganizations()) {
                        String orgKey = org.getName();
                        if (orgNameToIdMap.containsKey(orgKey)) {
                            orgIds.add(orgNameToIdMap.get(orgKey));
                        }
                    }
                    articleAuthor.setOrganizationId(orgIds);
                }
            }

            articleAuthorService.saveBatch(articleAuthors);
        }

        // 合并PubMed和PMC中的同一篇文献
        merge(article.getPmid(), article.getPmcId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletedAllByDocId(String source, Long docId) {
        // 删除PubMed特有的数据，注意：后续如果其他源也有下列数据，记得排除
        if (SourceTypeEnums.PubMed.name().equals(source)) {
            // 删除数据库关联信息
            databankService.removeByDocId(docId);

            // 删除化学物质关联信息和未使用的化学物质
            List<ArticleChemical> articleChemicals = articleChemicalService.findByDocId(docId);
            List<Long> chemicalIds = articleChemicals.stream().map(ArticleChemical::getChemicalId).collect(Collectors.toList());
            articleChemicalService.removeByDocId(docId);
            if (CollUtil.isNotEmpty(chemicalIds)) {
                chemicalService.removeUnusedByIds(chemicalIds);
            }

            // 删除MeSH术语关联信息
            articleMeshService.removeByDocId(docId);

            // 删除补充MeSH术语关联信息
            articleSupplMeshService.removeByDocId(docId);

            // 删除基因关联信息和未使用的基因
            List<ArticleGene> articleGenes = articleGeneService.findByPmid(docId);
            List<Long> geneIds = articleGenes.stream().map(ArticleGene::getGeneId).collect(Collectors.toList());
            articleGeneService.removeByDocId(docId);
            if (CollUtil.isNotEmpty(geneIds)) {
                geneService.removeUnusedByIds(geneIds);
            }

            // 删除资助信息关联和未使用的资助信息
            List<ArticleGrant> articleGrants = articleGrantService.findByPmid(docId);
            List<Long> grantIds = articleGrants.stream().map(ArticleGrant::getGrantId).collect(Collectors.toList());
            articleGrantService.removeByDocId(docId);
            if (CollUtil.isNotEmpty(grantIds)) {
                grantService.removeUnusedByIds(grantIds);
            }
        }

        // 删除出版类型关联信息和未使用的出版类型
        articlePubTypeService.removeByDocId(docId);
        // 删除没有被任何文献引用的PubType （速度太慢，取消）
            /*List<ArticlePubType> articlePubTypes = articlePubTypeService.findByDocId(docId);
            List<Long> pubTypeIds = articlePubTypes.stream().map(ArticlePubType::getPubtypeId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pubTypeIds)) {
                pubTypeService.removeUnusedByIds(pubTypeIds);
            }*/

        // 删除其他ID关联信息
        articleOtherIdService.removeByDocId(docId);

        // 删除引用文献
        referenceService.removeByDocId(docId);

        // 删除作者关联信息和未使用的作者、机构
        List<ArticleAuthor> articleAuthors = articleAuthorService.findByDocId(docId);
        List<Long> authorIds = articleAuthors.stream().map(ArticleAuthor::getAuthorId).collect(Collectors.toList());

        // 修改此处逻辑，适应 organizationId 作为 List<Long> 的情况
        Set<Long> organizationIdsSet = new HashSet<>();
        for (ArticleAuthor author : articleAuthors) {
            if (author.getOrganizationId() != null) {
                organizationIdsSet.addAll(author.getOrganizationId());
            }
        }
        List<Long> organizationIds = new ArrayList<>(organizationIdsSet);

        if (CollUtil.isNotEmpty(authorIds)) {
            // 排序后删除，避免高并发删除时死锁
            authorService.removeBatchByIds(authorIds.stream().sorted().collect(Collectors.toList()));
        }

        if (CollUtil.isNotEmpty(organizationIds)) {
            organizationService.removeBatchByIds(organizationIds.stream().sorted().collect(Collectors.toList()));
        }
        // 最后才能删除articleAuthor中间表
        articleAuthorService.removeByDocId(docId);
    }

    public ArticleDTO obtainArticleDTO(Long id) {
        ArticleDTO articleDTO = new ArticleDTO();
        Article article = this.getById(id);
        articleDTO.setArticle(article);

        List<ArticleDatabank> databanks = databankService.findByDocId(id);
        articleDTO.setArticleDatabanks(databanks);

        List<ArticlePubType> articlePubTypes = articlePubTypeService.findByDocId(id);
        articleDTO.setArticlePubTypes(articlePubTypes);

        List<Long> pubTypeIds = articlePubTypes.stream().map(ArticlePubType::getPubtypeId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(pubTypeIds)) {
            List<PubType> pubTypeList = pubTypeService.listByIds(pubTypeIds);
            articleDTO.setPubTypes(pubTypeList);
        }

        List<ArticleChemical> articleChemicalList = articleChemicalService.findByDocId(id);
        articleDTO.setArticleChemicals(articleChemicalList);

        List<Long> chemicalIds = articleChemicalList.stream().map(ArticleChemical::getChemicalId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(chemicalIds)) {
            List<Chemical> chemicalList = chemicalService.listByIds(chemicalIds);
            articleDTO.setChemicals(chemicalList);
        }

        List<ArticleMesh> articleMeshList = articleMeshService.findByDocId(id);
        articleDTO.setArticleMeshes(articleMeshList);

        List<ArticleSupplMesh> articleSupplMeshList = articleSupplMeshService.findByDocId(id);
        articleDTO.setArticleSupplMeshes(articleSupplMeshList);

        List<ArticleGene> articleGeneList = articleGeneService.findByPmid(id);
        articleDTO.setArticleGenes(articleGeneList);

        List<Long> geneIds = articleGeneList.stream()
                .map(ArticleGene::getGeneId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(geneIds)) {
            List<Gene> geneList = geneService.listByIds(geneIds);
            articleDTO.setGenes(geneList);
        }

        List<ArticleGrant> articleGrantList = articleGrantService.findByPmid(id);
        articleDTO.setArticleGrants(articleGrantList);

        List<Long> grantIds = articleGrantList.stream().map(ArticleGrant::getGrantId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(grantIds)) {
            List<Grant> grantList = grantService.listByIds(grantIds);
            articleDTO.setGrants(grantList);
        }

        List<ArticleOtherId> articleOtherIdList = articleOtherIdService.findByPmid(id);
        articleDTO.setArticleOtherIds(articleOtherIdList);

        List<Reference> referenceList = referenceService.findByDocId(id);
        articleDTO.setReferences(referenceList);

        List<ArticleAuthor> articleAuthorList = articleAuthorService.findByDocId(id);
        articleDTO.setArticleAuthors(articleAuthorList);

        return articleDTO;
    }
}
