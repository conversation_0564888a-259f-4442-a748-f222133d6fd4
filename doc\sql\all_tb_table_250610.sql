*
 Navicat Premium Data Transfer

 Source Server         : Citus
 Source Server Type    : PostgreSQL
 Source Server Version : 170002 (170002)
 Source Host           : ************:31910
 Source Catalog        : postgres
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170002 (170002)
 File Encoding         : 65001

 Date: 10/06/2025 17:07:51
*/


-- ----------------------------
-- Table structure for tb_dds_reference
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_reference";
CREATE TABLE "public"."tb_dds_reference" (
  "id" int8 NOT NULL,
  "doc_id" int8,
  "citation" text COLLATE "pg_catalog"."default",
  "pmid" text COLLATE "pg_catalog"."default",
  "pmcid" text COLLATE "pg_catalog"."default",
  "doi" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_pubtype
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_pubtype";
CREATE TABLE "public"."tb_dds_pubtype" (
  "id" int8 NOT NULL,
  "pub_type" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_publisher
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_publisher";
CREATE TABLE "public"."tb_dds_publisher" (
  "id" int8 NOT NULL,
  "name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "ioc" varchar(300) COLLATE "pg_catalog"."default",
  "home" varchar(300) COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL,
  "update_time" timestamptz(6) NOT NULL,
  "create_time" timestamptz(6) NOT NULL
)
;

-- ----------------------------
-- Table structure for tb_dds_organization
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_organization";
CREATE TABLE "public"."tb_dds_organization" (
  "id" int8 NOT NULL,
  "name" text COLLATE "pg_catalog"."default",
  "description" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_journal
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_journal";
CREATE TABLE "public"."tb_dds_journal" (
  "id" int8 NOT NULL,
  "publisher_id" int8,
  "zky_section_id" int8,
  "wos_quartile" varchar(2) COLLATE "pg_catalog"."default",
  "jcrabbreviation" varchar(300) COLLATE "pg_catalog"."default",
  "issn_print" varchar(60) COLLATE "pg_catalog"."default",
  "issn_electronic" varchar(60) COLLATE "pg_catalog"."default",
  "title" varchar(300) COLLATE "pg_catalog"."default",
  "isoabbreviation" varchar(300) COLLATE "pg_catalog"."default",
  "medline_ta" varchar(300) COLLATE "pg_catalog"."default",
  "unique_nlm_id" varchar(300) COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL,
  "create_time" timestamptz(6) NOT NULL,
  "update_time" timestamptz(6) NOT NULL,
  "source" text[] COLLATE "pg_catalog"."default",
  "source_type" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."tb_dds_journal"."source" IS '数组，来源，PubMed、JCR、PMC等';
COMMENT ON COLUMN "public"."tb_dds_journal"."source_type" IS '字符串，来源类型，熊刚治理的：custom。XML插入时新增的：system';

-- ----------------------------
-- Table structure for tb_dds_grant
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_grant";
CREATE TABLE "public"."tb_dds_grant" (
  "id" int8 NOT NULL,
  "grant_id" text COLLATE "pg_catalog"."default",
  "acronym" text COLLATE "pg_catalog"."default",
  "country" text COLLATE "pg_catalog"."default",
  "agency" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_gene
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_gene";
CREATE TABLE "public"."tb_dds_gene" (
  "id" int8 NOT NULL,
  "gene_symbol" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_file_content
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_file_content";
CREATE TABLE "public"."tb_dds_file_content" (
  "id" int8 NOT NULL,
  "file_data" bytea
)
;
COMMENT ON COLUMN "public"."tb_dds_file_content"."id" IS '雪花算法';

-- ----------------------------
-- Table structure for tb_dds_file
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_file";
CREATE TABLE "public"."tb_dds_file" (
  "id" int8 NOT NULL,
  "file_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "content_type" varchar(255) COLLATE "pg_catalog"."default",
  "file_size" int8 NOT NULL,
  "md5" char(32) COLLATE "pg_catalog"."default" NOT NULL,
  "file_path" text COLLATE "pg_catalog"."default" NOT NULL,
  "file_data" bytea,
  "create_time" timestamptz(6) NOT NULL DEFAULT now(),
  "source" text COLLATE "pg_catalog"."default",
  "pmid" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."tb_dds_file"."id" IS '雪花算法id';
COMMENT ON COLUMN "public"."tb_dds_file"."file_name" IS '原始文件名称';
COMMENT ON COLUMN "public"."tb_dds_file"."content_type" IS '后缀';
COMMENT ON COLUMN "public"."tb_dds_file"."file_size" IS '大小';
COMMENT ON COLUMN "public"."tb_dds_file"."file_path" IS '相对路径';
COMMENT ON COLUMN "public"."tb_dds_file"."create_time" IS '创建日期';

-- ----------------------------
-- Table structure for tb_dds_chemical
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_chemical";
CREATE TABLE "public"."tb_dds_chemical" (
  "id" int8 NOT NULL,
  "name" text COLLATE "pg_catalog"."default",
  "registry_no" text COLLATE "pg_catalog"."default",
  "mesh_id" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_author
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_author";
CREATE TABLE "public"."tb_dds_author" (
  "id" int8 NOT NULL,
  "forename" text COLLATE "pg_catalog"."default",
  "lastname" text COLLATE "pg_catalog"."default",
  "email" text COLLATE "pg_catalog"."default",
  "type" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article_xml
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_xml";
CREATE TABLE "public"."tb_dds_article_xml" (
  "id" int8 NOT NULL,
  "file_data" bytea NOT NULL
)
;

-- ----------------------------
-- Table structure for tb_dds_article_supplmesh
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_supplmesh";
CREATE TABLE "public"."tb_dds_article_supplmesh" (
  "doc_id" int8,
  "supplmesh_id" varchar(20) COLLATE "pg_catalog"."default",
  "type" text COLLATE "pg_catalog"."default",
  "name" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article_pubtype
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_pubtype";
CREATE TABLE "public"."tb_dds_article_pubtype" (
  "doc_id" int8,
  "pubtype_id" int8
)
;

-- ----------------------------
-- Table structure for tb_dds_article_parse
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_parse";
CREATE TABLE "public"."tb_dds_article_parse" (
  "id" int8 NOT NULL,
  "doc_id" int8,
  "file_name" varchar(600) COLLATE "pg_catalog"."default" NOT NULL,
  "local_path" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "content_type" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "source_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "source" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int4 NOT NULL,
  "file_md5" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" timestamptz(6) NOT NULL,
  "update_time" timestamptz(6),
  "error_msg" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article_otherid
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_otherid";
CREATE TABLE "public"."tb_dds_article_otherid" (
  "id" int8 NOT NULL,
  "doc_id" int8,
  "other_id" text COLLATE "pg_catalog"."default",
  "source" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article_mesh
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_mesh";
CREATE TABLE "public"."tb_dds_article_mesh" (
  "id" int8 NOT NULL,
  "doc_id" int8,
  "descriptor_id" text COLLATE "pg_catalog"."default",
  "qualifier_id1" text COLLATE "pg_catalog"."default",
  "qualifier_id2" text COLLATE "pg_catalog"."default",
  "qualifier_id3" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article_grant
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_grant";
CREATE TABLE "public"."tb_dds_article_grant" (
  "doc_id" int8,
  "grant_id" int8
)
;

-- ----------------------------
-- Table structure for tb_dds_article_gene
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_gene";
CREATE TABLE "public"."tb_dds_article_gene" (
  "doc_id" int8,
  "gene_id" int8
)
;

-- ----------------------------
-- Table structure for tb_dds_article_databank
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_databank";
CREATE TABLE "public"."tb_dds_article_databank" (
  "id" int8 NOT NULL,
  "doc_id" int8,
  "name" text COLLATE "pg_catalog"."default",
  "value" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article_chemical
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_chemical";
CREATE TABLE "public"."tb_dds_article_chemical" (
  "doc_id" int8,
  "chemical_id" int8
)
;

-- ----------------------------
-- Table structure for tb_dds_article_author
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article_author";
CREATE TABLE "public"."tb_dds_article_author" (
  "id" int8 NOT NULL,
  "doc_id" int8,
  "author_id" int8,
  "organization_id" int8[],
  "author_order" int4,
  "author_first" text COLLATE "pg_catalog"."default",
  "author_correspond" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for tb_dds_article
-- ----------------------------
DROP TABLE IF EXISTS "public"."tb_dds_article";
CREATE TABLE "public"."tb_dds_article" (
  "id" int8 NOT NULL,
  "custom_id" int8,
  "pmid" int4,
  "pmc_id" int4,
  "doi" text COLLATE "pg_catalog"."default",
  "source" text[] COLLATE "pg_catalog"."default",
  "pub_status" text COLLATE "pg_catalog"."default",
  "language" text COLLATE "pg_catalog"."default",
  "vernacular_title" text COLLATE "pg_catalog"."default",
  "title" text COLLATE "pg_catalog"."default",
  "published_year" int4,
  "published_month" int4,
  "published_day" int4,
  "other_date" jsonb,
  "journal_id" int8,
  "year" int4,
  "volume" text COLLATE "pg_catalog"."default",
  "issue" text COLLATE "pg_catalog"."default",
  "page" text COLLATE "pg_catalog"."default",
  "author" text[] COLLATE "pg_catalog"."default",
  "affiliation" text[] COLLATE "pg_catalog"."default",
  "keywords" text[] COLLATE "pg_catalog"."default",
  "abstract" text COLLATE "pg_catalog"."default",
  "other_abstract" text COLLATE "pg_catalog"."default",
  "copyright" text COLLATE "pg_catalog"."default",
  "hit_num" int4,
  "download" int4,
  "create_time" timestamptz(6),
  "update_time" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."tb_dds_article"."id" IS '主键，雪花算法生成';
COMMENT ON COLUMN "public"."tb_dds_article"."custom_id" IS '用户自定义id';
COMMENT ON COLUMN "public"."tb_dds_article"."pmid" IS 'pmid';
COMMENT ON COLUMN "public"."tb_dds_article"."pmc_id" IS 'pmc_id';
COMMENT ON COLUMN "public"."tb_dds_article"."doi" IS '文献doi';
COMMENT ON COLUMN "public"."tb_dds_article"."source" IS '文献来源';
COMMENT ON COLUMN "public"."tb_dds_article"."pub_status" IS '发表状态';
COMMENT ON COLUMN "public"."tb_dds_article"."language" IS '语言';
COMMENT ON COLUMN "public"."tb_dds_article"."vernacular_title" IS '原文标题';
COMMENT ON COLUMN "public"."tb_dds_article"."title" IS '英文标题';
COMMENT ON COLUMN "public"."tb_dds_article"."published_year" IS '发表年—epub';
COMMENT ON COLUMN "public"."tb_dds_article"."published_month" IS '发表月—epub';
COMMENT ON COLUMN "public"."tb_dds_article"."published_day" IS '发表日—epub';
COMMENT ON COLUMN "public"."tb_dds_article"."other_date" IS '其他日期';
COMMENT ON COLUMN "public"."tb_dds_article"."journal_id" IS '期刊表ID';
COMMENT ON COLUMN "public"."tb_dds_article"."year" IS '年';
COMMENT ON COLUMN "public"."tb_dds_article"."volume" IS '卷';
COMMENT ON COLUMN "public"."tb_dds_article"."issue" IS '期';
COMMENT ON COLUMN "public"."tb_dds_article"."page" IS '页码';
COMMENT ON COLUMN "public"."tb_dds_article"."author" IS '作者';
COMMENT ON COLUMN "public"."tb_dds_article"."affiliation" IS '单位';
COMMENT ON COLUMN "public"."tb_dds_article"."keywords" IS '关键词';
COMMENT ON COLUMN "public"."tb_dds_article"."abstract" IS '摘要';
COMMENT ON COLUMN "public"."tb_dds_article"."other_abstract" IS '其他摘要';
COMMENT ON COLUMN "public"."tb_dds_article"."copyright" IS '版权';
COMMENT ON COLUMN "public"."tb_dds_article"."hit_num" IS '点击量';
COMMENT ON COLUMN "public"."tb_dds_article"."download" IS '下载量';
COMMENT ON COLUMN "public"."tb_dds_article"."create_time" IS '入库时间';
COMMENT ON COLUMN "public"."tb_dds_article"."update_time" IS '更新时间';

-- ----------------------------
-- Indexes structure for table tb_dds_reference
-- ----------------------------
CREATE INDEX "tb_dds_reference_doc_id_idx" ON "public"."tb_dds_reference" USING btree (
  "doc_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_reference_doi_idx" ON "public"."tb_dds_reference" USING btree (
  "doi" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_reference_pmcid_idx" ON "public"."tb_dds_reference" USING btree (
  "pmcid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_reference_pmid_idx" ON "public"."tb_dds_reference" USING btree (
  "pmid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_reference
-- ----------------------------
ALTER TABLE "public"."tb_dds_reference" ADD CONSTRAINT "tb_dds_reference_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_pubtype
-- ----------------------------
CREATE INDEX "tb_dds_pubtype_pub_type_idx" ON "public"."tb_dds_pubtype" USING btree (
  "pub_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_pubtype
-- ----------------------------
ALTER TABLE "public"."tb_dds_pubtype" ADD CONSTRAINT "tb_dds_pubtype_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_publisher
-- ----------------------------
CREATE INDEX "tb_dds_publisher_name_idx" ON "public"."tb_dds_publisher" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table tb_dds_publisher
-- ----------------------------
ALTER TABLE "public"."tb_dds_publisher" ADD CONSTRAINT "tb_dds_publisher_20250530_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table tb_dds_publisher
-- ----------------------------
ALTER TABLE "public"."tb_dds_publisher" ADD CONSTRAINT "tb_dds_publisher_20250530_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_organization
-- ----------------------------
CREATE INDEX "tb_dds_organization_name_idx" ON "public"."tb_dds_organization" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_organization
-- ----------------------------
ALTER TABLE "public"."tb_dds_organization" ADD CONSTRAINT "tb_dds_organization_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_journal
-- ----------------------------
CREATE INDEX "tb_dds_journal_isoabbreviation_idx" ON "public"."tb_dds_journal" USING btree (
  "isoabbreviation" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_journal_issn_electronic_idx" ON "public"."tb_dds_journal" USING btree (
  "issn_electronic" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_journal_issn_print_idx" ON "public"."tb_dds_journal" USING btree (
  "issn_print" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_journal_publisher_id_idx" ON "public"."tb_dds_journal" USING btree (
  "publisher_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_journal_title_idx" ON "public"."tb_dds_journal" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_journal_unique_nlm_id_idx" ON "public"."tb_dds_journal" USING btree (
  "unique_nlm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_journal
-- ----------------------------
ALTER TABLE "public"."tb_dds_journal" ADD CONSTRAINT "tb_dds_journal_20250530_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_grant
-- ----------------------------
ALTER TABLE "public"."tb_dds_grant" ADD CONSTRAINT "tb_dds_grant_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_gene
-- ----------------------------
ALTER TABLE "public"."tb_dds_gene" ADD CONSTRAINT "tb_dds_gene_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_file_content
-- ----------------------------
ALTER TABLE "public"."tb_dds_file_content" ADD CONSTRAINT "tb_dds_file_content_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_file
-- ----------------------------
ALTER TABLE "public"."tb_dds_file" ADD CONSTRAINT "tb_dds_file_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_chemical
-- ----------------------------
ALTER TABLE "public"."tb_dds_chemical" ADD CONSTRAINT "tb_dds_chemical_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_author
-- ----------------------------
ALTER TABLE "public"."tb_dds_author" ADD CONSTRAINT "tb_dds_author_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tb_dds_article_xml
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_xml" ADD CONSTRAINT "tb_dds_file_content_20250521_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_article_parse
-- ----------------------------
CREATE INDEX "tb_dds_article_parse_create_time_idx" ON "public"."tb_dds_article_parse" USING btree (
  "create_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_parse_source_idx" ON "public"."tb_dds_article_parse" USING btree (
  "source" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_parse_status_idx" ON "public"."tb_dds_article_parse" USING btree (
  "status" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table tb_dds_article_parse
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_parse" ADD CONSTRAINT "tb_dds_file_20250521_file_md5_key" UNIQUE ("file_md5");

-- ----------------------------
-- Primary Key structure for table tb_dds_article_parse
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_parse" ADD CONSTRAINT "tb_dds_file_20250521_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_article_otherid
-- ----------------------------
CREATE INDEX "tb_dds_article_otherid_doc_id_idx" ON "public"."tb_dds_article_otherid" USING btree (
  "doc_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_article_otherid
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_otherid" ADD CONSTRAINT "tb_dds_article_otherid_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_article_mesh
-- ----------------------------
CREATE INDEX "tb_dds_article_mesh_descriptor_id_idx" ON "public"."tb_dds_article_mesh" USING btree (
  "descriptor_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_mesh_doc_id_idx" ON "public"."tb_dds_article_mesh" USING btree (
  "doc_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_article_mesh
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_mesh" ADD CONSTRAINT "tb_dds_article_mesh_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_article_databank
-- ----------------------------
CREATE INDEX "tb_dds_article_databank_doc_id_idx" ON "public"."tb_dds_article_databank" USING btree (
  "doc_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_article_databank
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_databank" ADD CONSTRAINT "tb_dds_article_databank_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_article_author
-- ----------------------------
CREATE INDEX "tb_dds_article_author_doc_id_idx" ON "public"."tb_dds_article_author" USING btree (
  "doc_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_article_author
-- ----------------------------
ALTER TABLE "public"."tb_dds_article_author" ADD CONSTRAINT "tb_dds_article_author_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tb_dds_article
-- ----------------------------
CREATE INDEX "tb_dds_article_custom_id_idx" ON "public"."tb_dds_article" USING btree (
  "custom_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_doi_idx" ON "public"."tb_dds_article" USING hash (
  "doi" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops"
);
CREATE INDEX "tb_dds_article_journal_id_idx" ON "public"."tb_dds_article" USING btree (
  "journal_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_pmc_id_idx" ON "public"."tb_dds_article" USING btree (
  "pmc_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_pmid_idx" ON "public"."tb_dds_article" USING btree (
  "pmid" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_published_day_idx" ON "public"."tb_dds_article" USING btree (
  "published_day" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_published_month_idx" ON "public"."tb_dds_article" USING btree (
  "published_month" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_published_year_idx" ON "public"."tb_dds_article" USING btree (
  "published_year" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_source_idx" ON "public"."tb_dds_article" USING btree (
  "source" COLLATE "pg_catalog"."default" "pg_catalog"."array_ops" ASC NULLS LAST
);
CREATE INDEX "tb_dds_article_title_idx" ON "public"."tb_dds_article" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table tb_dds_article
-- ----------------------------
ALTER TABLE "public"."tb_dds_article" ADD CONSTRAINT "tb_dds_article_pkey" PRIMARY KEY ("id");
