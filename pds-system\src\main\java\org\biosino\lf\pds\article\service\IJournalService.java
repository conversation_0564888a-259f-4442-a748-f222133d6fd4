package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;

import java.util.List;

/**
 * 期刊服务接口
 */
public interface IJournalService extends IService<Journal> {

    List<Journal> selectJournalList(JournalQueryDTO queryDTO);

    List<Journal> findByTitleIn(List<String> journalNames);
}
