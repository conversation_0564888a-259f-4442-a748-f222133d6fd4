package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleOtherId;
import org.biosino.lf.pds.article.mapper.ArticleOtherIdMapper;
import org.biosino.lf.pds.article.service.IArticleOtherIdService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章其他ID关联表 服务实现类
 */
@Service
public class ArticleOtherIdServiceImpl extends ServiceImpl<ArticleOtherIdMapper, ArticleOtherId> implements IArticleOtherIdService {
    @Override
    public List<ArticleOtherId> findByPmid(Long pmid) {
        return this.list(
                Wrappers.<ArticleOtherId>lambdaQuery()
                        .eq(ArticleOtherId::getDocId, pmid)
        );
    }

    @Override
    public boolean removeByDocId(Long id) {
        return this.remove(Wrappers.<ArticleOtherId>lambdaQuery()
                .eq(ArticleOtherId::getDocId, id));
    }
}
