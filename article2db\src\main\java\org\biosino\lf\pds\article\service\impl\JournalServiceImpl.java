package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.service.JournalService;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 期刊服务实现类
 *
 * <AUTHOR>
 */
@Service
public class JournalServiceImpl extends ServiceImpl<JournalMapper, Journal> implements JournalService {

    @Override
    public Journal saveJournal(Journal journal, Long publisherId) {
        if (journal == null || StringUtils.isBlank(journal.getTitle())) {
            return null;
        }

        // 按照优先级查询期刊：uniqueNlmId > issnPrint > issnElectronic > isoabbreviation > title
        Journal existingJournal = null;

        // 1. 优先使用uniqueNlmId查询
        if (CollUtil.isNotEmpty(journal.getUniqueNlmId())) {
            existingJournal = getOne(Wrappers.<Journal>lambdaQuery()
                    .apply("unique_nlm_id @> ARRAY[{0}]::text[]", journal.getUniqueNlmId().get(0))
                    .last("LIMIT 1"));
        }

        // 2. 如果uniqueNlmId查询不到，使用issnPrint查询
        if (existingJournal == null && CollUtil.isNotEmpty(journal.getIssnPrint())) {
            existingJournal = getOne(Wrappers.<Journal>lambdaQuery()
                    .apply("issn_print @> ARRAY[{0}]::text[]", journal.getIssnPrint().get(0))
                    .last("LIMIT 1"));
        }

        // 3. 如果issnPrint查询不到，使用issnElectronic查询
        if (existingJournal == null && CollUtil.isNotEmpty(journal.getIssnElectronic())) {
            existingJournal = getOne(Wrappers.<Journal>lambdaQuery()
                    .apply("issn_electronic @> ARRAY[{0}]::text[]", journal.getIssnElectronic().get(0))
                    .last("LIMIT 1"));
        }

        // 4. 如果issnElectronic查询不到，使用isoabbreviation查询
        if (existingJournal == null && StringUtils.isNotBlank(journal.getIsoabbreviation())) {
            existingJournal = getOne(new LambdaQueryWrapper<Journal>()
                    .eq(Journal::getIsoabbreviation, journal.getIsoabbreviation())
                    .last("LIMIT 1"));
        }

        // 5. 最后使用title查询
        if (existingJournal == null && StringUtils.isNotBlank(journal.getTitle())) {
            existingJournal = getOne(new LambdaQueryWrapper<Journal>()
                    .eq(Journal::getTitle, journal.getTitle())
                    .last("LIMIT 1"));
        }

        // 添加新期刊
        if (existingJournal == null) {
            journal.setId(IdUtils.getSnowflakeNextId());
            journal.setStatus(StatusEnums.ENABLE.getCode());
            if (publisherId != null) {
                journal.setPublisherId(publisherId);
            }
            journal.setCreateTime(new Date());
            journal.setUpdateTime(new Date());
            save(journal);
            return journal;
        }

        // 如果以前期刊和出版社没有建立关联关系 则重新建立链接
        if (existingJournal.getPublisherId() == null && publisherId != null) {
            existingJournal.setPublisherId(publisherId);
            updateById(existingJournal);
        }
        return existingJournal;
    }
}
