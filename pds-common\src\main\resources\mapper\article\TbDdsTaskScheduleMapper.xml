<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsTaskScheduleMapper">

    <update id="updateNotCompleteTaskScheduleStatusByDocId">
        UPDATE tb_dds_task_schedule t
        SET status = #{changeToStatus} FROM tb_dds_task_paper p
        WHERE t.paper_id = p.id
        AND p.doc_id = #{docId}
        AND t.status IN
        <foreach collection="notCompleteStatus" item="statusVal" open="(" separator="," close=")">
            #{statusVal}
        </foreach>
    </update>

</mapper>