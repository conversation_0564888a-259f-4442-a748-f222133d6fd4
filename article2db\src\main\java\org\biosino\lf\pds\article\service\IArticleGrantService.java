package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleGrant;

import java.util.List;

/**
 * 文章资助信息关联表 服务接口
 */
public interface IArticleGrantService extends IService<ArticleGrant> {
    /**
     * 根据文档ID查询文章资助信息关联信息
     *
     * @param pmid 文档ID
     * @return 文章资助信息关联信息列表
     */
    List<ArticleGrant> findByPmid(Long pmid);
    
    /**
     * 根据文档ID删除文章资助信息关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);
}
