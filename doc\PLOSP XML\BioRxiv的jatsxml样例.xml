<article article-type="article" specific-use="production" xml:lang="en" xmlns:hw="org.highwire.hpp"
         xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns:ali="http://www.niso.org/schemas/ali/1.0/" xmlns:ref="http://schema.highwire.org/Reference"
         xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:nlm="http://schema.highwire.org/NLM/Journal"
         xmlns:a="http://www.w3.org/2005/Atom" xmlns:c="http://schema.highwire.org/Compound"
         xmlns:hpp="http://schema.highwire.org/Publishing" xmlns:hwp="http://schema.highwire.org/Journal"
         xmlns:l="http://schema.highwire.org/Linking" xmlns:r="http://schema.highwire.org/Revision"
         xmlns:x="http://www.w3.org/1999/xhtml" xmlns:app="http://www.w3.org/2007/app">
    <front>
        <journal-meta>
            <journal-id journal-id-type="hwp">biorxiv</journal-id>
            <journal-id journal-id-type="publisher-id">BIORXIV</journal-id>
            <journal-title>bioRxiv</journal-title>
            <abbrev-journal-title abbrev-type="publisher">bioRxiv</abbrev-journal-title>
            <publisher>
                <publisher-name>Cold Spring Harbor Laboratory</publisher-name>
            </publisher>
        </journal-meta>
        <article-meta>
            <article-id pub-id-type="doi">10.1101/2025.03.21.644627</article-id>
            <article-id pub-id-type="other" hwp:sub-type="pisa">biorxiv;2025.03.21.644627v1</article-id>
            <article-id pub-id-type="other" hwp:sub-type="pisa-master">biorxiv;2025.03.21.644627</article-id>
            <article-id pub-id-type="other" hwp:sub-type="slug">2025.03.21.644627</article-id>
            <article-id pub-id-type="other" hwp:sub-type="atom-slug">2025.03.21.644627</article-id>
            <article-id pub-id-type="other" hwp:sub-type="tag">2025.03.21.644627</article-id>
            <article-version>1.1</article-version>
            <article-categories>
                <subj-group subj-group-type="author-type">
                    <subject>Regular Article</subject>
                </subj-group>
                <subj-group subj-group-type="heading">
                    <subject>New Results</subject>
                </subj-group>
                <subj-group subj-group-type="hwp-journal-coll" hwp:journal-coll-id="Bioinformatics"
                            hwp:journal="biorxiv">
                    <subject>Bioinformatics</subject>
                </subj-group>
            </article-categories>
            <title-group>
                <article-title hwp:id="article-title-1">Deep learning-based 3D spatial transcriptomics with X-Pression
                </article-title>
            </title-group>
            <author-notes hwp:id="author-notes-1">
                <fn id="n1" fn-type="equal" hwp:id="fn-1" hwp:rev-id="xref-fn-1-1 xref-fn-1-2">
                    <label>†</label>
                    <p hwp:id="p-1">These authors contributed equally</p>
                </fn>
                <fn id="n2" fn-type="others" hwp:id="fn-2" hwp:rev-id="xref-fn-2-1 xref-fn-2-2 xref-fn-2-3 xref-fn-2-4">
                    <label>‡</label>
                    <p hwp:id="p-2">These authors jointly supervised this work</p>
                </fn>
                <corresp id="cor1" hwp:id="corresp-1" hwp:rev-id="xref-corresp-1-1 xref-corresp-1-2 xref-corresp-1-3">
                    <label>✉</label>
                    <bold>Corresponding author.</bold>
                    Correspondance to Sven Rottenberg: <email hwp:id="email-1"><EMAIL></email>, Volker
                    Thiel: <email hwp:id="email-2"><EMAIL></email>, Etori Aguiar Moreira:
                    <email hwp:id="email-3"><EMAIL></email>
                </corresp>
            </author-notes>
            <contrib-group hwp:id="contrib-group-1">
                <contrib contrib-type="author" equal-contrib="yes" hwp:id="contrib-1">
                    <name name-style="western" hwp:sortable="Túrós Demeter">
                        <surname>Túrós</surname>
                        <given-names>Demeter</given-names>
                    </name>
                    <xref ref-type="aff" rid="a1" hwp:id="xref-aff-1-1" hwp:rel-id="aff-1">1</xref>
                    <xref ref-type="fn" rid="n1" hwp:id="xref-fn-1-1" hwp:rel-id="fn-1">†</xref>
                </contrib>
                <contrib contrib-type="author" equal-contrib="yes" hwp:id="contrib-2">
                    <name name-style="western" hwp:sortable="Gladiseva Lollija">
                        <surname>Gladiseva</surname>
                        <given-names>Lollija</given-names>
                    </name>
                    <xref ref-type="aff" rid="a1" hwp:id="xref-aff-1-2" hwp:rel-id="aff-1">1</xref>
                    <xref ref-type="fn" rid="n1" hwp:id="xref-fn-1-2" hwp:rel-id="fn-1">†</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-3">
                    <name name-style="western" hwp:sortable="Botos Marius">
                        <surname>Botos</surname>
                        <given-names>Marius</given-names>
                    </name>
                    <xref ref-type="aff" rid="a2" hwp:id="xref-aff-2-1" hwp:rel-id="aff-2">2</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-4">
                    <name name-style="western" hwp:sortable="He Chang">
                        <surname>He</surname>
                        <given-names>Chang</given-names>
                    </name>
                    <xref ref-type="aff" rid="a1" hwp:id="xref-aff-1-3" hwp:rel-id="aff-1">1</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-5">
                    <name name-style="western" hwp:sortable="Barut G. Tuba">
                        <surname>Barut</surname>
                        <given-names>G. Tuba</given-names>
                    </name>
                    <xref ref-type="aff" rid="a2" hwp:id="xref-aff-2-2" hwp:rel-id="aff-2">2</xref>
                    <xref ref-type="aff" rid="a3" hwp:id="xref-aff-3-1" hwp:rel-id="aff-3">3</xref>
                    <xref ref-type="aff" rid="a4" hwp:id="xref-aff-4-1" hwp:rel-id="aff-4">4</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-6">
                    <name name-style="western" hwp:sortable="Veiga Inês Berenguer">
                        <surname>Veiga</surname>
                        <given-names>Inês Berenguer</given-names>
                    </name>
                    <xref ref-type="aff" rid="a2" hwp:id="xref-aff-2-3" hwp:rel-id="aff-2">2</xref>
                    <xref ref-type="aff" rid="a3" hwp:id="xref-aff-3-2" hwp:rel-id="aff-3">3</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-7">
                    <name name-style="western" hwp:sortable="Ebert Nadine">
                        <surname>Ebert</surname>
                        <given-names>Nadine</given-names>
                    </name>
                    <xref ref-type="aff" rid="a2" hwp:id="xref-aff-2-4" hwp:rel-id="aff-2">2</xref>
                    <xref ref-type="aff" rid="a3" hwp:id="xref-aff-3-3" hwp:rel-id="aff-3">3</xref>
                    <xref ref-type="aff" rid="a4" hwp:id="xref-aff-4-2" hwp:rel-id="aff-4">4</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-8">
                    <name name-style="western" hwp:sortable="Bonnin Anne">
                        <surname>Bonnin</surname>
                        <given-names>Anne</given-names>
                    </name>
                    <xref ref-type="aff" rid="a5" hwp:id="xref-aff-5-1" hwp:rel-id="aff-5">5</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-9">
                    <name name-style="western" hwp:sortable="Chanfon Astrid">
                        <surname>Chanfon</surname>
                        <given-names>Astrid</given-names>
                    </name>
                    <xref ref-type="aff" rid="a1" hwp:id="xref-aff-1-4" hwp:rel-id="aff-1">1</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-10">
                    <name name-style="western" hwp:sortable="Grau-Roma Llorenç">
                        <surname>Grau-Roma</surname>
                        <given-names>Llorenç</given-names>
                    </name>
                    <xref ref-type="aff" rid="a1" hwp:id="xref-aff-1-5" hwp:rel-id="aff-1">1</xref>
                    <xref ref-type="aff" rid="a3" hwp:id="xref-aff-3-4" hwp:rel-id="aff-3">3</xref>
                    <xref ref-type="aff" rid="a4" hwp:id="xref-aff-4-3" hwp:rel-id="aff-4">4</xref>
                </contrib>
                <contrib contrib-type="author" hwp:id="contrib-11">
                    <name name-style="western" hwp:sortable="Valdeolivas Alberto">
                        <surname>Valdeolivas</surname>
                        <given-names>Alberto</given-names>
                    </name>
                    <xref ref-type="aff" rid="a5" hwp:id="xref-aff-5-2" hwp:rel-id="aff-5">5</xref>
                    <xref ref-type="fn" rid="n2" hwp:id="xref-fn-2-1" hwp:rel-id="fn-2">‡</xref>
                </contrib>
                <contrib contrib-type="author" corresp="yes" hwp:id="contrib-12">
                    <name name-style="western" hwp:sortable="Rottenberg Sven">
                        <surname>Rottenberg</surname>
                        <given-names>Sven</given-names>
                    </name>
                    <xref ref-type="aff" rid="a1" hwp:id="xref-aff-1-6" hwp:rel-id="aff-1">1</xref>
                    <xref ref-type="aff" rid="a6" hwp:id="xref-aff-6-1" hwp:rel-id="aff-6">6</xref>
                    <xref ref-type="fn" rid="n2" hwp:id="xref-fn-2-2" hwp:rel-id="fn-2">‡</xref>
                    <xref ref-type="corresp" rid="cor1" hwp:id="xref-corresp-1-1" hwp:rel-id="corresp-1">✉</xref>
                </contrib>
                <contrib contrib-type="author" corresp="yes" hwp:id="contrib-13">
                    <name name-style="western" hwp:sortable="Thiel Volker">
                        <surname>Thiel</surname>
                        <given-names>Volker</given-names>
                    </name>
                    <xref ref-type="aff" rid="a2" hwp:id="xref-aff-2-5" hwp:rel-id="aff-2">2</xref>
                    <xref ref-type="aff" rid="a3" hwp:id="xref-aff-3-5" hwp:rel-id="aff-3">3</xref>
                    <xref ref-type="aff" rid="a4" hwp:id="xref-aff-4-4" hwp:rel-id="aff-4">4</xref>
                    <xref ref-type="fn" rid="n2" hwp:id="xref-fn-2-3" hwp:rel-id="fn-2">‡</xref>
                    <xref ref-type="corresp" rid="cor1" hwp:id="xref-corresp-1-2" hwp:rel-id="corresp-1">✉</xref>
                </contrib>
                <contrib contrib-type="author" corresp="yes" hwp:id="contrib-14">
                    <name name-style="western" hwp:sortable="Moreira Etori Aguiar">
                        <surname>Moreira</surname>
                        <given-names>Etori Aguiar</given-names>
                    </name>
                    <xref ref-type="aff" rid="a2" hwp:id="xref-aff-2-6" hwp:rel-id="aff-2">2</xref>
                    <xref ref-type="aff" rid="a3" hwp:id="xref-aff-3-6" hwp:rel-id="aff-3">3</xref>
                    <xref ref-type="aff" rid="a4" hwp:id="xref-aff-4-5" hwp:rel-id="aff-4">4</xref>
                    <xref ref-type="fn" rid="n2" hwp:id="xref-fn-2-4" hwp:rel-id="fn-2">‡</xref>
                    <xref ref-type="corresp" rid="cor1" hwp:id="xref-corresp-1-3" hwp:rel-id="corresp-1">✉</xref>
                </contrib>
                <aff id="a1" hwp:id="aff-1"
                     hwp:rev-id="xref-aff-1-1 xref-aff-1-2 xref-aff-1-3 xref-aff-1-4 xref-aff-1-5 xref-aff-1-6">
                    <label>1</label>
                    <institution hwp:id="institution-1">Institute of Animal Pathology, Vetsuisse Faculty, University of
                        Bern</institution>, Bern,
                    <country>Switzerland</country>
                </aff>
                <aff id="a2" hwp:id="aff-2"
                     hwp:rev-id="xref-aff-2-1 xref-aff-2-2 xref-aff-2-3 xref-aff-2-4 xref-aff-2-5 xref-aff-2-6">
                    <label>2</label>
                    <institution hwp:id="institution-2">Institute of Virology and Immunology</institution>, Bern and
                    Mittelhäusern, Bern,
                    <country>Switzerland</country>
                </aff>
                <aff id="a3" hwp:id="aff-3"
                     hwp:rev-id="xref-aff-3-1 xref-aff-3-2 xref-aff-3-3 xref-aff-3-4 xref-aff-3-5 xref-aff-3-6">
                    <label>3</label>
                    <institution hwp:id="institution-3">Department of Infectious Diseases and Pathobiology, Vetsuisse
                        Faculty, University of Bern</institution>, Bern,
                    <country>Switzerland</country>
                </aff>
                <aff id="a4" hwp:id="aff-4"
                     hwp:rev-id="xref-aff-4-1 xref-aff-4-2 xref-aff-4-3 xref-aff-4-4 xref-aff-4-5">
                    <label>4</label>
                    <institution hwp:id="institution-4">Multidisciplinary Center for Infectious Diseases, University of
                        Bern</institution>, Bern,
                    <country>Switzerland</country>
                </aff>
                <aff id="a5" hwp:id="aff-5" hwp:rev-id="xref-aff-5-1 xref-aff-5-2">
                    <label>5</label>
                    <institution hwp:id="institution-5">Paul Scherrer Institute</institution>, Villigen,
                    <country>Switzerland</country>
                </aff>
                <aff id="a6" hwp:id="aff-6" hwp:rev-id="xref-aff-6-1">
                    <label>6</label>
                    <institution hwp:id="institution-6">Roche Pharma Research and Early Development, Roche Innovation
                        Center Basel, F. Hoffmann-La Roche Ltd</institution>, Basel,
                    <country>Switzerland</country>
                </aff>
                <aff id="a7" hwp:id="aff-7">
                    <label>7</label>
                    <institution hwp:id="institution-7">Bern Center for Precision Medicine (BCPM), University of
                        Bern</institution>, Bern,
                    <country>Switzerland</country>
                </aff>
            </contrib-group>
            <pub-date pub-type="epub-original" date-type="pub" publication-format="electronic" hwp:start="2025">
                <year>2025</year>
            </pub-date>
            <pub-date pub-type="hwp-created" hwp:start="2025-03-24T18:39:14-07:00">
                <day>24</day>
                <month>3</month>
                <year>2025</year>
            </pub-date>
            <pub-date pub-type="hwp-received" hwp:start="2025-03-24T18:39:14-07:00">
                <day>24</day>
                <month>3</month>
                <year>2025</year>
            </pub-date>
            <pub-date pub-type="epub" hwp:start="2025-03-24T18:44:25-07:00">
                <day>24</day>
                <month>3</month>
                <year>2025</year>
            </pub-date>
            <pub-date pub-type="epub-version" hwp:start="2025-03-24T18:44:25-07:00">
                <day>24</day>
                <month>3</month>
                <year>2025</year>
            </pub-date>
            <elocation-id>2025.03.21.644627</elocation-id>
            <history hwp:id="history-1">
                <date date-type="received" hwp:start="2025-03-21">
                    <day>21</day>
                    <month>3</month>
                    <year>2025</year>
                </date>
                <date date-type="rev-recd" hwp:start="2025-03-21">
                    <day>21</day>
                    <month>3</month>
                    <year>2025</year>
                </date>
                <date date-type="accepted" hwp:start="2025-03-24">
                    <day>24</day>
                    <month>3</month>
                    <year>2025</year>
                </date>
            </history>
            <permissions>
                <copyright-statement hwp:id="copyright-statement-1">© 2025, Posted by Cold Spring Harbor Laboratory
                </copyright-statement>
                <copyright-year>2025</copyright-year>
                <license hwp:id="license-1">
                    <p hwp:id="p-3">The copyright holder for this pre-print is the author. All rights reserved. The
                        material may not be redistributed, re-used or adapted without the author's permission.
                    </p>
                </license>
            </permissions>
            <self-uri xlink:href="644627.pdf" content-type="pdf" xlink:role="full-text"/>
            <self-uri l:ref="forthcoming:yes" c:role="http://schema.highwire.org/variant/abstract" xlink:role="abstract"
                      content-type="xhtml+xml" hwp:variant="yes"/>
            <self-uri l:ref="forthcoming:yes" c:role="http://schema.highwire.org/variant/external-links"
                      xlink:role="external-links" content-type="xhtml+xml" hwp:variant="yes"/>
            <self-uri l:ref="forthcoming:yes" c:role="http://schema.highwire.org/variant/full-text"
                      xlink:href="file:/content/biorxiv/vol0/issue2025/pdf/2025.03.21.644627v1.pdf" hwp:variant="yes"
                      content-type="pdf" xlink:role="full-text"/>
            <self-uri l:ref="forthcoming:yes" c:role="http://schema.highwire.org/variant/full-text"
                      xlink:role="full-text" content-type="xhtml+xml" hwp:variant="yes"/>
            <self-uri l:ref="forthcoming:yes" c:role="http://schema.highwire.org/variant/source" xlink:role="source"
                      content-type="xml" xlink:show="none" hwp:variant="yes"/>
            <self-uri l:ref="forthcoming:yes" c:role="http://schema.highwire.org/variant/original" xlink:role="original"
                      content-type="xml" xlink:show="none" hwp:variant="yes" xlink:href="644627.xml"/>
            <self-uri content-type="abstract"
                      xlink:href="file:/content/biorxiv/vol0/issue2025/abstracts/2025.03.21.644627v1/2025.03.21.644627v1.htslp"/>
            <self-uri content-type="fulltext"
                      xlink:href="file:/content/biorxiv/vol0/issue2025/fulltext/2025.03.21.644627v1/2025.03.21.644627v1.htslp"/>
            <abstract hwp:id="abstract-1">
                <p hwp:id="p-4">Spatial transcriptomics technologies currently lack scalable and cost-effective options
                    to profile tissues in three dimensions. Technological advances in microcomputed tomography enabled
                    non-destructive volumetric imaging of tissue blocks with sub-micron resolution at a centimetre
                    scale. Here, we present X-Pression, a deep convolutional neural network-based frame-work designed to
                    reconstruct 3D expression signatures of cellular niches from volumetric microcomputed tomography
                    data. By training on a singular 2D section of a paired spatial transcriptomics experiment,
                    X-Pression achieves high accuracy and is capable of generalising to out-of-sample examples. We
                    utilised X-Pression to demonstrate the benefit of 3D examination of tissues on a paired SARS-CoV-2
                    vaccine efficacy spatial transcriptomics and microcomputed tomography cohort of a recently developed
                    live attenuated SARS-CoV-2 vaccine. By applying X-Pression to the entire mouse lung, we visualised
                    the sites of viral replication at the organ level and the simultaneous collapse of small alveoli in
                    their vicinity. In addition, we assessed the immunological response following vaccination and virus
                    challenge infection. X-Pression offers a valuable and cost-effective addition to infer expression
                    signatures without the need for consecutive 2D sectioning and reconstruction, providing new insights
                    into transcriptomic profiles in three dimensions.
                </p>
            </abstract>
            <kwd-group kwd-group-type="author" hwp:id="kwd-group-1">
                <kwd hwp:id="kwd-1">Spatial Transcriptomics</kwd>
                <kwd hwp:id="kwd-2">Microcomputed Tomography</kwd>
                <kwd hwp:id="kwd-3">Deep Learning</kwd>
                <kwd hwp:id="kwd-4">3D gene expression</kwd>
            </kwd-group>
            <counts>
                <page-count count="18"/>
            </counts>
            <custom-meta-wrap>
                <custom-meta hwp:id="custom-meta-1">
                    <meta-name>special-property</meta-name>
                    <meta-value>contains-inline-supplementary-material</meta-value>
                </custom-meta>
            </custom-meta-wrap>
        </article-meta>
        <notes hwp:id="notes-1">
            <notes notes-type="competing-interest-statement" hwp:id="notes-2">
                <title hwp:id="title-1">Competing Interest Statement</title>
                <p hwp:id="p-5">A.V. and I.B.V. are currently employed by F. Hoffmann-La Roche Ltd. The University of
                    Bern has patented the utilisation of OTS206 as a vaccine, with G.T.B., N.E., and V.T. listed as
                    inventors. The development of OTS206 involved collaboration between the University of Bern and
                    Rocketvax AG, with financial support being provided to the University by Rocketvax AG. V.T. is
                    currently providing consultancy services to Rocketvax AG. The remaining authors have declared no
                    competing interests.
                </p>
            </notes>
        </notes>
    </front>
    <body>
        <sec id="s1" hwp:id="sec-1">
            <title hwp:id="title-2">Introduction</title>
            <p hwp:id="p-6">Spatial transcriptomics (ST) technologies have had a massive impact on life sciences in
                recent years, ranging from new findings in developmental biology to cancer research with discoveries
                directly linked to patient outcomes [<xref ref-type="bibr" rid="c1" hwp:id="xref-ref-1-1"
                                                           hwp:rel-id="ref-1">1</xref>–<xref ref-type="bibr" rid="c3"
                                                                                             hwp:id="xref-ref-3-1"
                                                                                             hwp:rel-id="ref-3">3</xref>]. <italic
                        toggle="yes">In situ
                </italic> profiling technologies enable us to examine cells and their organisation in the tissue space,
                revealing new patterns and spatial interactions [<xref ref-type="bibr" rid="c4" hwp:id="xref-ref-4-1"
                                                                       hwp:rel-id="ref-4">4</xref>–<xref ref-type="bibr"
                                                                                                         rid="c6"
                                                                                                         hwp:id="xref-ref-6-1"
                                                                                                         hwp:rel-id="ref-6">
                    6</xref>].
            </p>
            <p hwp:id="p-7">Despite the rapid development of numerous ST technologies, including <italic toggle="yes">in
                situ
            </italic> sequencing and spatial barcoding approaches, most of these platforms struggle with scalability due
                to proprietary components and high costs [<xref ref-type="bibr" rid="c7" hwp:id="xref-ref-7-1"
                                                                hwp:rel-id="ref-7">7</xref>]. Consequently, together
                with the histopathology standards that involve working with thin two-dimensional tissue sections, these
                platforms tend to overlook that tissues are threedimensional structures.
            </p>
            <p hwp:id="p-8">Novel methodologies and computational approaches in the field of ST are exploring more
                cost-effective open-source platforms allowing serial sectioning and 3D reconstruction [<xref
                        ref-type="bibr" rid="c8" hwp:id="xref-ref-8-1" hwp:rel-id="ref-8">8</xref>–<xref ref-type="bibr"
                                                                                                         rid="c12"
                                                                                                         hwp:id="xref-ref-12-1"
                                                                                                         hwp:rel-id="ref-12">
                    12</xref>]. Although these approaches led to significant discoveries by examining the 3D structure,
                they still require multiple thin sections to be processed and reconstructed, which in turn restricts
                their scalability and results in the destruction of the valuable original tissue samples. New
                generations of imaging mass cytometry [<xref ref-type="bibr" rid="c13" hwp:id="xref-ref-13-1"
                                                             hwp:rel-id="ref-13">13</xref>] and MERFISH [<xref
                        ref-type="bibr" rid="c14" hwp:id="xref-ref-14-1" hwp:rel-id="ref-14">14</xref>] are being
                developed to effectively analyse thicker (200-300 µm) tissue samples. While these methods offer crucial
                insights into 3D spatial biology, they present several technological draw-backs, such as limited tissue
                penetration of antibodies/oligos [<xref ref-type="bibr" rid="c15" hwp:id="xref-ref-15-1"
                                                        hwp:rel-id="ref-15">15</xref>] and diminished signal intensity [<xref
                        ref-type="bibr" rid="c14" hwp:id="xref-ref-14-2" hwp:rel-id="ref-14">14</xref>, <xref
                        ref-type="bibr" rid="c16" hwp:id="xref-ref-16-1" hwp:rel-id="ref-16">16</xref>].
            </p>
            <p hwp:id="p-9">The field of computational pathology has been adapting to emerging 3D imaging technologies
                that enable the capture of volumetric tissue data in a non-destructive manner [<xref ref-type="bibr"
                                                                                                     rid="c17"
                                                                                                     hwp:id="xref-ref-17-1"
                                                                                                     hwp:rel-id="ref-17">
                    17</xref>, <xref ref-type="bibr" rid="c18" hwp:id="xref-ref-18-1" hwp:rel-id="ref-18">18</xref>].
                Modalities such as light-sheet microscopy [<xref ref-type="bibr" rid="c19" hwp:id="xref-ref-19-1"
                                                                 hwp:rel-id="ref-19">19</xref>], multiphoton microscopy,
                optical coherence tomography [<xref ref-type="bibr" rid="c20" hwp:id="xref-ref-20-1"
                                                    hwp:rel-id="ref-20">20</xref>], photoacoustic microscopy [<xref
                        ref-type="bibr" rid="c21" hwp:id="xref-ref-21-1" hwp:rel-id="ref-21">21</xref>], and
                microcomputed tomography (micro-CT) have been shown to enhance standard pathological work-flows. Among
                these, micro-CT is capable of imaging large tissue samples (centimetre scale) at sub-micrometre
                resolution directly on the paraffin blocks [<xref ref-type="bibr" rid="c22" hwp:id="xref-ref-22-1"
                                                                  hwp:rel-id="ref-22">22</xref>, <xref ref-type="bibr"
                                                                                                       rid="c23"
                                                                                                       hwp:id="xref-ref-23-1"
                                                                                                       hwp:rel-id="ref-23">
                    23</xref>]. This technology has also demonstrated success in resolving the 3D microstructure of
                solid tissues [<xref ref-type="bibr" rid="c24" hwp:id="xref-ref-24-1" hwp:rel-id="ref-24">24</xref>] and
                consistently produced quantitative features without disrupting histological workflows [<xref
                        ref-type="bibr" rid="c25" hwp:id="xref-ref-25-1" hwp:rel-id="ref-25">25</xref>].
            </p>
            <p hwp:id="p-10">Deep learning-based computational tools have shown great promise in linking histology to
                gene expression. Various methods, including as ST-Net [<xref ref-type="bibr" rid="c26"
                                                                             hwp:id="xref-ref-26-1" hwp:rel-id="ref-26">
                    26</xref>], XFuse [<xref ref-type="bibr" rid="c27" hwp:id="xref-ref-27-1" hwp:rel-id="ref-27">
                    27</xref>], and iStar [<xref ref-type="bibr" rid="c28" hwp:id="xref-ref-28-1" hwp:rel-id="ref-28">
                    28</xref>] have been developed to predict gene expression from haematoxylin and eosin (H&amp;E)
                tissue sections or to enhance the resolution of traditional ST platforms by imputing gene expression
                from histology.
            </p>
            <p hwp:id="p-11">Building on these approaches, we introduce X-Pression, a novel deep learning-based
                framework designed to reconstruct gene expression programs in 3D from micro-CT data. We demonstrate its
                effectiveness using a cohort of SARS-CoV-2 infected mouse lungs profiled with both 10x Visium and
                micro-CT. X-Pression reliably infers expression programs related to viral infection and replication
                using a single ST sample for training. Additionally, we show how investigating 3D spatial domains can
                provide valuable insights into SARS-CoV-2 infection, disease progression, and the efficacy of various
                vaccines. Our approach also allows for accurate prediction of expression profiles in volumes that lack
                paired ST data from the same cohort. X-Pression offers a scalable, costeffective alternative for
                exploring the 3D transcriptional land-scape of tissues, paving the way to new discoveries in both basic
                and applied research.
            </p>
        </sec>
        <sec id="s2" hwp:id="sec-2">
            <title hwp:id="title-3">Results</title>
            <sec id="s2a" hwp:id="sec-3">
                <title hwp:id="title-4">Model overview</title>
                <p hwp:id="p-12">To enable scalable and cost-effective inference of 3D gene expression programs from
                    volumetric imaging data, we developed X-Pression. This framework builds on a deep convolutional
                    neural network (CNN) backbone that takes 3D patches from the micro-CT volume to predict gene
                    expression programs. First, we obtained tissue sections from paraffin blocks containing fixed
                    tissues for ST with 10x Visium, while the remaining blocks were used for micro-CT scanning (
                    <bold>
                        <xref ref-type="fig" rid="fig1" hwp:id="xref-fig-1-1" hwp:rel-id="F1">Fig.1a</xref>
                    </bold>
                    ). Next, we preprocessed the micro-CT volumes by segmenting them with a U-net architecture trained
                    on axial tomogram slices annotated by veterinary pathologists. After co-registering the two
                    modalities with landmark-based affine transformations, X-Pression extracted 3D volumetric patches
                    (128×128×21 pixels; 204.8×204.8×33.6 µm) centred around ST capture spots from the micro-CT volume.
                    By extending the patch size over the boundaries of the capture spots (diameter = 55 µm), we
                    incorporated additional spatial context that matches the average distance of paracrine signalling
                    molecules (200-300 µm) capturing the potential influence of the surrounding tissue [<xref
                            ref-type="bibr" rid="c29" hwp:id="xref-ref-29-1" hwp:rel-id="ref-29">29</xref>, <xref
                            ref-type="bibr" rid="c30" hwp:id="xref-ref-30-1" hwp:rel-id="ref-30">30</xref>]. These
                    patches are subsequently used to train a supervised CNN to infer gene expression programs associated
                    with specific cellular niches (<italic toggle="yes">i</italic>.<italic toggle="yes">e</italic>.,
                    tissue compartments) calculated by Chrysalis [<xref ref-type="bibr" rid="c31" hwp:id="xref-ref-31-1"
                                                                        hwp:rel-id="ref-31">31</xref>] (
                    <bold>
                        <xref ref-type="fig" rid="fig1" hwp:id="xref-fig-1-2" hwp:rel-id="F1">Fig.1b</xref>
                    </bold>
                    ). In the CNN, 3D convolutional layers perform feature extraction, followed by a regressor head with
                    a final output layer that employs a softmax activation function. Since the expression programs
                    calculated with Chrysalis are represented as a convex combination summing to one, the softmax
                    outputs are directly used as their predicted values for each capture spot. Hence, by leveraging deep
                    learning and spatial context beyond individual capture spots, X-Pression enables a robust,
                    high-resolution reconstruction of gene expression profiles in 3D.
                </p>
                <fig id="fig1" position="float" fig-type="figure" orientation="portrait" hwp:id="F1"
                     hwp:rev-id="xref-fig-1-1 xref-fig-1-2">
                    <object-id pub-id-type="other" hwp:sub-type="pisa">biorxiv;2025.03.21.644627v1/FIG1</object-id>
                    <object-id pub-id-type="other" hwp:sub-type="slug">F1</object-id>
                    <object-id pub-id-type="publisher-id">fig1</object-id>
                    <label>Fig. 1.</label>
                    <caption hwp:id="caption-1">
                        <title hwp:id="title-5">X-Pression overview.</title>
                        <p hwp:id="p-13"><bold>a</bold>, X-Pression framework leverages a formalin-fixed
                            paraffin-embedded (FFPE) tissue block. A single tissue section is taken from the surface of
                            a paraffin block and subjected to ST (10x Visium), while the rest of the block is imaged
                            using micro-CT. This approach produces paired samples of spot-based ST data and micro-CT
                            volumes of the whole tissue blocks with a resolution of 1.625 µm. <bold>b</bold>, After
                            co-registration, X-Pression takes 3D image tiles from the micro-CT volume centred around
                            each capture spot for training. The CNN model of X-Pression is composed of a feature
                            extractor containing four blocks of 3D convolutional and max-pooling layers, followed by a
                            regressor head with a softmax activation function to predict gene expression programs.
                        </p>
                    </caption>
                    <graphic xlink:href="644627v1_fig1" position="float" orientation="portrait" hwp:id="graphic-1"/>
                </fig>
            </sec>
            <sec id="s2b" hwp:id="sec-4">
                <title hwp:id="title-6">X-Pression accurately infers gene expression programs from micro-CT data</title>
                <p hwp:id="p-14">To assess the accuracy and applicability of X-Pression, we applied it to an ST and
                    micro-CT dataset from a severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) vaccine
                    efficacy study. We analysed samples from animals immunised with either a genome-modified
                    live-attenuated virus, including “one-tostop” codons (OTS206) [<xref ref-type="bibr" rid="c32"
                                                                                         hwp:id="xref-ref-32-1"
                                                                                         hwp:rel-id="ref-32">32</xref>],
                    or an mRNA vaccine. Following SARS-CoV-2 Delta (B.1.617.2) variant challenge, we compared their
                    elicited immune responses in 3D.
                </p>
                <p hwp:id="p-15">First, we evaluated X-Pression using a single-pair approach on a specimen (L2210926)
                    from the right lung of an mRNA vaccine-immunised mouse, collected 5 days post-challenge (5 dpc) with
                    the SARS-CoV-2 Delta variant. Assessing the ST data with Chrysalis, we identified eight distinct
                    gene expression programs that represent functionally and spatially distinct anatomical regions
                    associated with cellular niches. These expression programs were used as training targets for
                    X-Pression
                    <bold>(<xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-1" hwp:rel-id="F2">Fig.2a</xref>)
                    </bold>
                    . We characterised these key expression programs by analysing their most representative and
                    distinctly expressed genes
                    <bold>(<xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-2" hwp:rel-id="F2">Fig.2b-c</xref>)
                    </bold>
                    , and by correlating them with various cell types via deconvolution relying on our single-cell
                    reference data, histopathological annotations, and functional characterisation <bold>(Supplementary
                        Note 1-2)</bold>. Further analysis conducted on the ST datasets (spatiotemporal assessment of
                    the expression programs and pathway activity inference) of mouse lungs infected with either
                    wild-type (Wuhan strain) SARS-CoV-2 and OTS206, or challenged with the Delta variant
                    post-immunisation with an mRNA vaccine and OTS206 are described in <bold>Supplementary Note 2</bold>.
                </p>
                <fig id="fig2" position="float" fig-type="figure" orientation="portrait" hwp:id="F2"
                     hwp:rev-id="xref-fig-2-1 xref-fig-2-2 xref-fig-2-3 xref-fig-2-4 xref-fig-2-5 xref-fig-2-6 xref-fig-2-7 xref-fig-2-8">
                    <object-id pub-id-type="other" hwp:sub-type="pisa">biorxiv;2025.03.21.644627v1/FIG2</object-id>
                    <object-id pub-id-type="other" hwp:sub-type="slug">F2</object-id>
                    <object-id pub-id-type="publisher-id">fig2</object-id>
                    <label>Fig. 2.</label>
                    <caption hwp:id="caption-2">
                        <title hwp:id="title-7">X-Pression infers gene expression programs on the organ level using a
                            single ST section.
                        </title>
                        <p hwp:id="p-16">Data originated from a specimen (L2210926) obtained from the right lung of an
                            mRNA-vaccinated mouse five days after challenge with the SARS-CoV-2 Delta variant. <bold>
                                a</bold>, Maximum intensity projection (MIP) of the gene expression programs. <bold>
                                b</bold>, Highest-weighted genes in each expression program. <bold>c</bold>, Heatmap
                            showing the Z-scored contribution of genes to the expression programs. <bold>d</bold>,
                            Pathologist annotations based on the H&amp;E image. <bold>e</bold>, Capture spots classified
                            based on the highest gene expression program for stratified sampling (left) and spots
                            belonging to training, test, and validation sets (right). <bold>f</bold>, True values for
                            each gene expression signature per capture spot next to the values predicted by X-Pression. <bold>
                                g</bold>, Changes in loss and MAE during the training process. <bold>h</bold>, MIP of
                            the predictions on an axial section of the whole FFPE block. The dashed-lined rectangle
                            corresponds to the position and area of the tissue section measured with ST.
                        </p>
                    </caption>
                    <graphic xlink:href="644627v1_fig2" position="float" orientation="portrait" hwp:id="graphic-2"/>
                </fig>
                <p hwp:id="p-17">The characterisation of the eight different gene expression programs revealed distinct
                    patterns associated with lung architecture and cellular processes
                    <bold>(<xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-3" hwp:rel-id="F2">Fig.2a-b</xref>)
                    </bold>
                    . The expression program linked to lung parenchyma (0) is characterised by genes primarily expressed
                    in pulmonary alveolar cells. Additionally, parenchyma infiltrated with resident immune cells (3) was
                    identified. Moreover, distinct expression signatures suggest the presence of a fibrotic and
                    immune-modulated microenvironment (2), bronchioles (4), larger airways featuring contractile tissue
                    (6), and bronchial epithelium (7). The signature of viral replication sites (5) was defined based on
                    the expression of SARS-CoV-2 <italic toggle="yes">N, M, ORF1, E</italic>, and <italic toggle="yes">
                        S
                    </italic> genes. Additionally, the presence of immune markers such as <italic toggle="yes">Cxcl10,
                        Ccl7, Ifi205, Socs3</italic>, and <italic toggle="yes">Il1rn</italic> indicated a robust
                    antiviral response driven by interferons in the replication sites. The expression of <italic
                            toggle="yes">Cd274 (PD-L1)
                    </italic> and Gzmb implied the involvement of immune regulatory and activation processes, possibly
                    through cytotoxic T cells or NK cells, alongside tissue remodelling and an oxidative stress response
                    indicated by Spp1 and Mt2 expression. This was further confirmed by the upregulation of JAK-STAT and
                    NfkB pathways <bold>(Supplementary Note 2)</bold>. An additional expression program, termed
                    inflammation-induced atelectasis (IIA) (1), corresponding to areas of severe immune infiltration,
                    which expanded the alveolar septa, occluded the alveolar lumina, and led to associated alveolar
                    collapse was further identified. Genes associated with this signature are indicative of the presence
                    of macrophages (Ctl8, Ctss), neutrophils (Aif1, Ccl5) and complement activation (C1 genes). In
                    further correlation with TGF-β, these areas are consistent with severe histiocytic and neutrophilic
                    inflammation resulting in airway remodelling and alveolar collapse. These major expression programs
                    were in accordance with annotations based on histopathological analysis (
                    <bold>
                        <xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-4" hwp:rel-id="F2">Fig.2d</xref>
                    </bold>
                    ).
                </p>
                <p hwp:id="p-18">After characterising the gene expression programs in the tissue sample, we trained
                    X-Pression to infer these programs from the micro-CT data. Our model training strategy involved
                    first extracting the capture spot-3D tile pairs that were subsequently classified for stratified
                    sampling and data augmentation. Classes were defined based on the highest compartment score for each
                    capture spot and split to train, test, and validation sets (
                    <bold>
                        <xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-5" hwp:rel-id="F2">Fig.2e</xref>
                    </bold>
                    ). We evaluated the performance of X-Pression by calculating the coefficient of determination (
                    <italic toggle="yes">R</italic>
                    <sup>2</sup>) for these sets (
                    <bold>
                        <xref rid="tbl1" ref-type="table" hwp:id="xref-table-wrap-1-1" hwp:rel-id="T1">Table 1</xref>
                    </bold>
                    ). Overall, the total average
                    <italic toggle="yes">R</italic>
                    <sup>2</sup>
                    was 0.860, from which 0.643 was achieved on the validation and 0.583 on the test set. In the test
                    set, predictions for multiple expression programs exceeded 0.7. Visualising the inferred values in
                    the tissue space shows robust spatial patterns with high similarity to the ST data (
                    <bold>
                        <xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-6" hwp:rel-id="F2">Fig.2f</xref>
                    </bold>
                    ). Training strategy involved monitoring the mean absolute error (MAE) in the validation set, which
                    was concluded if no further improvements were measured in the last five epochs (
                    <bold>
                        <xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-7" hwp:rel-id="F2">Fig.2g</xref>
                    </bold>
                    ).
                </p>
                <table-wrap id="tbl1" orientation="portrait" position="float" hwp:id="T1"
                            hwp:rev-id="xref-table-wrap-1-1">
                    <object-id pub-id-type="other" hwp:sub-type="pisa">biorxiv;2025.03.21.644627v1/TBL1</object-id>
                    <object-id pub-id-type="other" hwp:sub-type="slug">T1</object-id>
                    <object-id pub-id-type="publisher-id">tbl1</object-id>
                    <label>Table 1.</label>
                    <caption hwp:id="caption-3">
                        <title hwp:id="title-8">
                            <italic toggle="yes">R</italic>
                            <sup>2</sup>
                            for different splits.
                        </title>
                    </caption>
                    <graphic xlink:href="644627v1_tbl1" position="float" orientation="portrait" hwp:id="graphic-3"/>
                </table-wrap>
                <p hwp:id="p-19">To comprehensively map gene expression programs, we extracted 3D tiles from the
                    micro-CT volume using a sliding window with a stride of 16 pixels (26 µm). This approach reduces
                    aliasing artefacts and edge effects, resulting in capturing finer variations in gene expression.
                    Reconstruction of the predicted signatures allowed us to visualise them on the whole organ level
                    that reaches beyond the boundaries of the ST capture area (
                    <bold>
                        <xref ref-type="fig" rid="fig2" hwp:id="xref-fig-2-8" hwp:rel-id="F2">Fig.2h</xref>
                    </bold>
                    ). These results highlight the strong predictive performance of X-Pression, with inferred gene
                    expression programs that align closely with ST data. Our approach enables the mapping of spatial
                    gene expression patterns, offering a robust framework for studying complex tissue environments.
                </p>
            </sec>
            <sec id="s2c" hwp:id="sec-5">
                <title hwp:id="title-9">X-Pression reveals 3D infection patterns during SARS-CoV-2 infection</title>
                <p hwp:id="p-20">Next, we reconstructed the inferred gene expression programs in 3D, revealing complex
                    spatial features across the lung tissue. Examination of an axial cross-section revealed the
                    widespread presence of the IIA signature (1) in all major lobes of the right lung (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-1" hwp:rel-id="F3">Fig.3a</xref>
                    </bold>
                    ). These structures were often surrounded by active viral replication sites, as indicated by the
                    predictions of compartment 5, which is defined by SARS-CoV-2 genes and cytokine expression.
                    Replication sites were also observed further away from atelectatic immune-infiltrated areas,
                    indicating a more complex temporal relationship between active viral replication and lung collapse.
                    In addition to the prominent virusassociated compartments, we observed strong signals of immune
                    cell-infiltrated parenchyma (3) in the caudal lobe. Further examination of multiple axial sections
                    revealed prominent viral replication sites and IIA throughout the entire lung (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-2" hwp:rel-id="F3">Fig.3b</xref>
                    </bold>
                    ), with more foci of IIA in the centre of active viral replication. Cross-sectional and parasagittal
                    volume slices (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-3" hwp:rel-id="F3">Fig.3c-d</xref>
                    </bold>
                    ) further demonstrated this.
                </p>
                <fig id="fig3" position="float" fig-type="figure" orientation="portrait" hwp:id="F3"
                     hwp:rev-id="xref-fig-3-1 xref-fig-3-2 xref-fig-3-3 xref-fig-3-4 xref-fig-3-5 xref-fig-3-6 xref-fig-3-7 xref-fig-3-8 xref-fig-3-9">
                    <object-id pub-id-type="other" hwp:sub-type="pisa">biorxiv;2025.03.21.644627v1/FIG3</object-id>
                    <object-id pub-id-type="other" hwp:sub-type="slug">F3</object-id>
                    <object-id pub-id-type="publisher-id">fig3</object-id>
                    <label>Fig. 3.</label>
                    <caption hwp:id="caption-4">
                        <title hwp:id="title-10">Gene expression signatures revealed with X-Pression in 3D during
                            SARS-CoV-2 infection.
                        </title>
                        <p hwp:id="p-21"><bold>a</bold>, Axial section of SARS-CoV-2-infected mouse lung (L2210926, mRNA
                            vaccine, 5dpc) with MIP colour overlays showing the gene expression signatures inferred by
                            X-Pression. The original tomogram section is shown in the bottom left inset. Dashed-lined
                            square with a white arrow: sampling site for panel f. Scale bar: 1000 µm. CrL: cranial lobe,
                            ML: medial lobe, CaL: caudal lobe. <bold>b</bold>, Serial axial sections with colour
                            overlays showing gene expression changes across the volume. Scale bar: 1000 µm. <bold>
                                c</bold>, Serial transverse sections with colour overlays showing gene expression
                            changes across the volume. Scale bar: 1000 µm. <bold>d</bold>, Serial parasagittal sections
                            with colour overlays showing gene expression changes across the volume. Scale bar: 1000 µm. <bold>
                                e</bold>, Demonstrative high-resolution ROIs extracted from the axial section shown in
                            panel a with colour overlays showing the gene expression signatures and the original
                            tomogram. Scale bar: 200 µm. <bold>f</bold>, Cuboid sampled from the lung at the location
                            indicated in panel a, where the drawn square corresponds to its base. <bold>g</bold>, 3D
                            whole organ renders of expression programs associated with IIA (1) and viral replication (5)
                            (left) and bronchioles (4) (right). Grid spacing: 500 µm. <bold>h</bold>, UMAP plots of IG
                            attributions. On the left, individual data points are represented by their corresponding
                            middle slices from the extracted 3D patches. On the right, points are coloured according to
                            their predicted compartment scores. <bold>i</bold>, Heatmap of correlation matrix displaying
                            the Pearson correlation coefficients between micro-CT morphological features and predicted
                            tissue compartments.
                        </p>
                    </caption>
                    <graphic xlink:href="644627v1_fig3" position="float" orientation="portrait" hwp:id="graphic-4"/>
                </fig>
                <p hwp:id="p-22">We then focused on examining the microstructure in more detail (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-4" hwp:rel-id="F3">Fig.3e</xref>
                    </bold>
                    ). On 500 µm wide square-shaped region-of-interests (ROIs), X-Pression accurately delineated areas
                    of IIA, which were primarily visible on the tomogram as consolidated structures of high
                    radiodensity. Additionally, viral replication sites were distinguished from the surrounding
                    parenchyma. Contrary to normal alveolar structures, these areas correlated with distorted alveolar
                    spaces demarcated by irregularly thickened alveolar septa corresponding to the interstitial immune
                    cell infiltration in histology.
                </p>
                <p hwp:id="p-23">To demonstrate the flexibility of our approach, we extracted a square prism with a
                    height of 1000 µm from the volume (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-5" hwp:rel-id="F3">Fig.3f</xref>
                    </bold>
                    ). X-Pression revealed intricate gene expression changes across the prism, further exemplifying the
                    method’s capacity to visualise complex spatial relationships between viral replication, immune cell
                    infiltration, and tissue architecture.
                </p>
                <p hwp:id="p-24">Building on these analyses, we generated whole-organ 3D renders of the inferred
                    expression programs (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-6" hwp:rel-id="F3">Fig.3g</xref>
                    </bold>
                    ). The dual 3D render of lung collapse and viral replication revealed a prominent enrichment of the
                    IIA-associated signature in the caudal lobe. It also highlighted the overall pattern of IIA
                    following the major airways at the centre of active viral infection. Additionally, we created a
                    separate render of the bronchus-associated expression signature, which effectively visualised the
                    branching pattern of the airways.
                </p>
                <p hwp:id="p-25">Furthermore, we computed feature attributions using Integrated Gradients (IG) (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-7" hwp:rel-id="F3">Fig.3h</xref>
                    </bold>
                    ). The attributions were then projected into a 2D UMAP space to visualise the relationship between
                    tissue structure and model predictions. The resulting UMAP visualisation revealed distinct clusters
                    that correspond to patches with varying textures and intensities, which are associated with
                    different expression programs.
                </p>
                <p hwp:id="p-26">Finally, we computed Pearson correlation coefficients between micro-CT-derived
                    morphological features and the predicted compartment values using patches from the entire micro-CT
                    volume. Our analysis revealed that different expression signatures identified by the model align
                    with specific morphological patterns (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-8" hwp:rel-id="F3">Fig.3i</xref>
                    </bold>
                    ). Although the correlations were not pronounced, we observed that different compartments showed
                    distinct morphological feature compositions. For example, compartment 7, which corresponds to a
                    bronchial epithelium, displayed a negative correlation with both mean intensity and Fourier energy.
                    This pattern is consistent with the structural properties of airways in micro-CT, which typically
                    appear as low-intensity regions due to their thinner structure and reduced X-ray attenuation. In
                    contrast, compartment 1, representing regions affected by IIA, showed a positive correlation with
                    both mean intensity and Fourier energy, alongside more pronounced negative correlations with
                    skewness, Laplacian of Gaussian (LoG), and standard deviation (STD) of intensity. IIA-affected
                    regions appear denser and more homogeneous on micro-CT scans. This is consistent with the positive
                    correlation between mean intensity and Fourier energy, as these areas may exhibit higher frequency
                    patterns and more structured textures due to fluid accumulation and cellular infiltration. The
                    negative correlation with skewness indicates that the intensity distribution in these regions is
                    more symmetrical compared to other tissue areas. In IIA regions, tissue collapse and fluid
                    accumulation result in a more uniform and less variable intensity distribution, leading to a reduced
                    skewness. Additionally, the denser and more homogeneous structure results in fewer sharp boundaries
                    or edges in the micro-CT images, which corresponds to lower LoG values and less intensity variation,
                    resulting in lower STD values.
                </p>
                <p hwp:id="p-27">These results demonstrate that X-Pression accurately infers gene expression programs
                    from micro-CT data, providing a scalable and non-destructive approach to explore ST in three
                    dimensions.
                </p>
            </sec>
            <sec id="s2d" hwp:id="sec-6">
                <title hwp:id="title-11">3D transcriptional comparison of different vaccine strategies</title>
                <p hwp:id="p-28">In addition, we applied X-Pression to another lung specimen (L2210914) from a mouse
                    that had been immunised with the OTS206 live-attenuated SARS-CoV-2 vaccine and challenged with the
                    SARS-CoV-2 Delta variant for 5 days. By analysing the predicted signature composition of the sample
                    and comparing it with the mRNA vaccineimmunised specimen, we uncovered substantial changes in the
                    effectiveness of the different vaccines. First, we classified each voxel based on the gene
                    expression program with the highest predicted value and assessed their spatial prevalence (
                    <bold>
                        <xref ref-type="fig" rid="fig4" hwp:id="xref-fig-4-1" hwp:rel-id="F4">Fig.4a</xref>
                    </bold>
                    ). In the lung of the OTS206-immunised mouse, the gene expression program indicative of active viral
                    replication (5) was substantially reduced across the whole organ. IIA signature (1), though present
                    in both conditions, was diminished in the OTS206 sample, indicative of less severe infection and
                    tissue damage. We compared the distribution of the predicted signatures, which further indicated
                    deviations in compartments 1 and 5, as well as compartment 3 (
                    <bold>
                        <xref ref-type="fig" rid="fig4" hwp:id="xref-fig-4-2" hwp:rel-id="F4">Fig.4b</xref>
                    </bold>
                    ). Signatures expressed by other structural parts of the tissue were found to show no appreciable
                    difference.
                </p>
                <fig id="fig4" position="float" fig-type="figure" orientation="portrait" hwp:id="F4"
                     hwp:rev-id="xref-fig-4-1 xref-fig-4-2 xref-fig-4-3 xref-fig-4-4 xref-fig-4-5">
                    <object-id pub-id-type="other" hwp:sub-type="pisa">biorxiv;2025.03.21.644627v1/FIG4</object-id>
                    <object-id pub-id-type="other" hwp:sub-type="slug">F4</object-id>
                    <object-id pub-id-type="publisher-id">fig4</object-id>
                    <label>Fig. 4.</label>
                    <caption hwp:id="caption-5">
                        <title hwp:id="title-12">X-Pression reveals reduced viral replication and infection-associated
                            tissue damage in a mouse lung immunised with the OTS206 live-attenuated SARS-CoV-2 vaccine.
                        </title>
                        <p hwp:id="p-29"><bold>a</bold>, Area plots showing the proportions of the inferred 3D gene
                            expression programs in the mouse lung immunised with mRNA vaccine or OTS206 vaccine and 28
                            days later challenged with the SARS-CoV-2 Delta variant. Analysed lungs were harvested 5
                            days post-challenge (5 dpc). Ground truth ST section (L2210926) is visualised as a stacked
                            barplot on the left. <bold>b</bold>, Distribution of the predicted gene expression program
                            scores compared across conditions, visualised as histograms and boxplots below (centre line:
                            median, box limits: upper and lower quartiles, whiskers: 1.5x interquartile range). <bold>
                                c</bold>, Serial axial sections of OTS206-immunised mouse lung with MIP colour overlays
                            showing the gene expression signatures inferred by X-Pression. Scale bar: 1000 µm. CrL:
                            cranial lobe, ML: medial lobe, CaL: caudal lobe. <bold>d</bold>, Demonstrative
                            high-resolution ROIs with colour overlays showing the gene expression signatures and the
                            original tomograms. Scale bar: 200 µm. <bold>e</bold>, Serial transverse sections with
                            colour overlays showing gene expression changes across the volume. Scale bar: 1000
                            µm. <bold>f</bold>, Serial parasagittal sections with colour overlays showing gene
                            expression changes across the volume. Scale bar: 1000 µm. <bold>g</bold>, 3D whole organ
                            renders of expression programs associated with IIA (1) and viral replication (5) (left) and
                            bronchioles (4) (right). Grid spacing: 500 µm.
                        </p>
                    </caption>
                    <graphic xlink:href="644627v1_fig4" position="float" orientation="portrait" hwp:id="graphic-5"/>
                </fig>
                <p hwp:id="p-30">The visualisation of the signatures further corroborated a milder infection by the
                    SARS-CoV-2 Delta variant in the lung of the OTS206-vaccinated mouse. In this case, most of the
                    parenchyma remains intact, while it is largely inferred to express a signature indicative of active
                    immune infiltration (3). Although active viral replication sites were present, they were much less
                    abundant compared to the lung of the mRNA-vaccinated animal (
                    <bold>
                        <xref ref-type="fig" rid="fig4" hwp:id="xref-fig-4-3" hwp:rel-id="F4">Fig.4c</xref>
                    </bold>
                    ). Expression programs were consistent with the microstructure, showing mostly normal and moderately
                    elevated radiodensity in the parenchyma and mild lung collapse in a few focal points (
                    <bold>
                        <xref ref-type="fig" rid="fig4" hwp:id="xref-fig-4-4" hwp:rel-id="F4">Fig.4d</xref>
                    </bold>
                    ). These findings were further confirmed by visualisation of transverse and parasagittal sections (
                    <bold>
                        <xref ref-type="fig" rid="fig4" hwp:id="xref-fig-4-5" hwp:rel-id="F4">Fig.4e-f</xref>
                    </bold>
                    ). 3D rendering of the whole lung with active viral replication sites and lung collapse high-lights
                    the reduced infection severity and tissue damage compared to the mRNA-vaccinated lung (
                    <bold>
                        <xref ref-type="fig" rid="fig3" hwp:id="xref-fig-3-9" hwp:rel-id="F3">Fig.3g</xref>
                    </bold>
                    ).
                </p>
                <p hwp:id="p-31">In summary, we show that X-Pression enables high-resolution 3D reconstruction of gene
                    expression programs, revealing spatially distinct transcriptional landscapes within the lung tissue.
                    By capturing complex interactions between viral replication, immune responses and tissue
                    remodelling, X-Pression provides a powerful tool for studying gene expression dynamics across
                    multiple scales, offering new insights into infection progression and vaccine efficacy.
                </p>
            </sec>
        </sec>
        <sec id="s3" hwp:id="sec-7">
            <title hwp:id="title-13">Discussion</title>
            <p hwp:id="p-32">In this study, we introduced X-Pression, a deep learning-based framework that enables
                scalable and cost-effective inference of 3D gene expression programs from micro-CT data. A 3D
                perspective is essential for a deeper understanding of tissue function and disease progression, yet most
                transcriptomic profiling techniques are constrained to two dimensions. While recent technological
                advances have significantly improved 2D ST, these two-dimensional approaches may not fully capture the
                complexity of three-dimensional tissue architecture, potentially leading to incomplete or inaccurate
                interpretations. Additionally, conventional sectioning of tissues inherently results in the loss of
                useful spatial information, limiting our ability to study tissue architecture and cellular interactions
                in their native 3D context.
            </p>
            <p hwp:id="p-33">To address these limitations, X-Pression presents a solution by offering high-resolution
                transcriptomic profiling of 3D tissue structures through an integrated approach that combines ST and
                micro-CT. Our method leverages deep learning-based modelling with micro-CT imaging to overcome the
                challenges of traditional 2D methods, resulting in a scalable and cost-effective approach. Furthermore,
                the non-destructive nature of micro-CT enables subsequent, targeted investigation of the tissue sample
                based on this predictive modelling. In addition, X-Pression allows for whole-organ analysis, thus
                permitting the study of gene expression patterns and a more detailed understanding of disease
                progression. In summary, our results demonstrate that X-Pression successfully captures information
                relevant to predicting gene expression from microtomograms with high accuracy and minimal data
                requirements from a single ST section.
            </p>
            <p hwp:id="p-34">Integrating higher-quality and more diverse training data will further improve the
                generalizability of our model, resulting in more accurate and robust predictions across a broad range of
                tissue types. To improve the performance of our model, we propose performing micro-CT imaging prior to
                ST, which would enhance the registration accuracy between H&amp;E and CT slices, therefore improving the
                accuracy of gene expression predictions. Furthermore, integrating advanced model architectures, such as
                vision transformers (e.g., Eff-CTNet [<xref ref-type="bibr" rid="c33" hwp:id="xref-ref-33-1"
                                                            hwp:rel-id="ref-33">33</xref>], Swin transformer [<xref
                        ref-type="bibr" rid="c34" hwp:id="xref-ref-34-1" hwp:rel-id="ref-34">34</xref>]), may boost the
                model’s ability to generalise across different tissues. These architectures utilise attention mechanisms
                to capture long-range dependencies and multi-scale features, allowing the model to incorporate the
                global context rather than relying solely on extracted 3D patch patterns. Another promising approach
                would be to implement local self-attention mechanisms to improve the learning of weak textures, as
                demonstrated in SwinMatcher [<xref ref-type="bibr" rid="c35" hwp:id="xref-ref-35-1" hwp:rel-id="ref-35">
                    35</xref>]. Expanding beyond micro-CT, X-Pression could also be applied to other non-destructive
                imaging techniques, such as regular light sheet microscopy. While these methods are less powerful than
                micro-CT, they remain more accessible and still provide valuable insights into tissue structure and
                pathology. This versatility increases X-Pression’s potential for application in diverse research
                settings.
            </p>
            <p hwp:id="p-35">To showcase the effectiveness of X-Pression, it was applied to a cohort of
                SARS-CoV-2-infected mice, revealing complex host-pathogen interactions. Although traditional 2D imaging
                and transcriptomic approaches have provided valuable insights [<xref ref-type="bibr" rid="c36"
                                                                                     hwp:id="xref-ref-36-1"
                                                                                     hwp:rel-id="ref-36">36</xref>],
                they fall short of capturing the full complexity of 3D tissue architecture and the spatial dynamics of
                viral replication and immune responses. Our integrated approach allowed for a detailed characterisation
                of viral replication sites, immune cell infiltration, and lung tissue damage at the cellular scale and
                provided new insights into the protective effects of different vaccine strategies.
            </p>
            <p hwp:id="p-36">In particular, we used X-Pression to assess how an mRNA and a live-attenuated vaccine
                modulate immune responses during a SARS-CoV-2 challenge infection. By analysing the spatial distribution
                of immune cell markers and viral replication sites, we observed distinct patterns of immune activation
                and protection in vaccinated animals. Our data suggest that both vaccine strategies, while differing in
                their mechanisms of action, are capable of promoting localised immune responses that limit viral
                replication in the lungs and mitigate tissue damage. The spatial separation between replication sites
                and collapsed regions due to inflammation suggests that immune response kinetics, rather than direct
                viral damage, are shaping lung pathology. Our approach highlighted that lesions (increased IIA
                percentage) in the lungs of a mouse previously vaccinated with an mRNA vaccine and subsequently
                challenged with the SARS-CoV-2 Delta variant were elevated in comparison to those in the lungs of an
                animal vaccinated with OTS206. The more frequent detection of these IIA lesions suggests that the
                live-attenuated vaccination was slightly better in preventing virus spread or effectively controlling
                the infection. Despite the observed differences, it is important to highlight that both the mRNA and
                OTS206 vaccine strategies demonstrated efficacy in activating a genetic program in the lungs that
                controlled the infection with the challenge of the SARS-CoV-2 Delta variant. This integrated approach
                provided a deeper understanding of how immune cells are mobilised within lung tissue, how vaccines
                influence these dynamics, and how viral infection disrupts tissue homeostasis.
            </p>
            <p hwp:id="p-37">By mapping immune responses and viral dynamics at an unprecedented resolution, our study
                highlights the potential of this framework to reveal novel therapeutic targets and inform future vaccine
                development. We believe that X-Pression is a powerful tool for uncovering gene expression of tissues in
                3D, encompassing viral infections, cancer, and neurodegenerative diseases, among others.
            </p>
        </sec>
        <sec id="s4" hwp:id="sec-8">
            <title hwp:id="title-14">Methods</title>
            <sec id="s4a" hwp:id="sec-9">
                <title hwp:id="title-15">Experimental work</title>
                <sec id="s4a1" hwp:id="sec-10">
                    <title hwp:id="title-16">Biosafety statement</title>
                    <p hwp:id="p-38">All experiments with SARS-CoV-2 and attenuated OTS viruses were performed in
                        biosafety level 3 (BSL3) containment laboratories at the Institute of Virology and Immunology in
                        Mittelhäusern, Switzerland. The standard operating procedures of the BSL3 facilities have been
                        approved by the competent local authorities.
                    </p>
                </sec>
                <sec id="s4a2" hwp:id="sec-11">
                    <title hwp:id="title-17">Mouse experiments</title>
                    <p hwp:id="p-39">A well-characterised model of SARS-CoV-2 was utilised, specifically, hACE2-K18Tg
                        mice (Tg(K18-hACE2)2Prlmn), which were bred in the specific-pathogen-free facility of the
                        Institute of Virology and Immunology in Mittelhäusern, Switzerland. For infection, 7-16-week-old
                        female and male mice were anaesthetised with isoflurane and inoculated intranasally with 5,000
                        TCID50 (50% tissue culture infectious dose) of the SARS-CoV-2 WTD614G
                        (BetaCoV/Germany/BavPat1/2020, Acc. No. EPI_ISL_406862). The inoculum volume was 20 µl per
                        nostril per mouse. For vaccination experiments, mice were injected intramuscularly with a single
                        dose of 1 µg mRNA vaccine Spikevax (Moderna) or inoculated intranasally with 5,000 TCID50 of the
                        live-attenuated SARS-CoV-2 vaccine OTS206 [<xref ref-type="bibr" rid="c32"
                                                                         hwp:id="xref-ref-32-2" hwp:rel-id="ref-32">
                            32</xref>]. Mice were challenged intranasally with the SARS-CoV-2 Delta variant
                        (hCoV-19/Germany/BW-FR1407/2021, Acc. No. EPI_ISL_2535433) 4 weeks after vaccination. All mice
                        were monitored daily for weight loss and clinical signs. Whole lungs were harvested for
                        histology, micro-CT, ST, and single-cell RNA sequencing (scRNA-seq).
                    </p>
                </sec>
                <sec id="s4a3" hwp:id="sec-12">
                    <title hwp:id="title-18">Histopathological analysis of the lungs</title>
                    <p hwp:id="p-40">Histological analysis was performed on whole mouse lungs harvested at 2 and 5 days
                        post-infection/challenge. The lungs were perfused with 4% formalin, routinary processed for
                        histology, including embedding in paraffin, sectioned at 5 µm sections and stained with H&amp;E.
                    </p>
                </sec>
                <sec id="s4a4" hwp:id="sec-13">
                    <title hwp:id="title-19">Spatial transcriptomics, custom probes and gene expression analysis</title>
                    <p hwp:id="p-41">Lung tissue sections of 5 µm thick were sectioned directly onto Visium Spatial for
                        FFPE Gene Expression Mouse Transcriptome slides (6.5 × 6.5 mm) containing four capture areas
                        each and processed according to the manufacturer’s recommendations (10X Genomics, Pleasanton,
                        CA). In addition to the mouse transcriptome probes, we used a set of customised probes for the
                        SARS-CoV-2 virus targeting <italic toggle="yes">ORF1ab, ORF3a, ORF10</italic> and the genes
                        encoding the structural proteins spike (S), envelope (E), membrane (M), and nucleocapsid (N).
                        The custom SARS-CoV-2 probes and protocol have been previously published [<xref ref-type="bibr"
                                                                                                        rid="c32"
                                                                                                        hwp:id="xref-ref-32-3"
                                                                                                        hwp:rel-id="ref-32">
                            32</xref>]. The cDNA libraries were loaded onto the NovaSeq 6000 system (Illumina) and
                        sequenced with a minimum of 50,000 reads per covered spot. Reads contained in Illumina FASTQ
                        files were aligned to a custom multispecies reference transcriptome generated with Space Ranger
                        using the GRCm38 (v.mm10-2020-A_build, 10X Genomics) mouse and NC_045512.2 SARS-CoV-2
                        references.
                    </p>
                </sec>
                <sec id="s4a5" hwp:id="sec-14">
                    <title hwp:id="title-20">SR Micro-CT acquisition</title>
                    <p hwp:id="p-42">We conducted synchrotron radiation-based X-ray phase contrast microtomography (SR
                        Micro-CT) on paraffin blocks at the TOMCAT beamline (X02DA) of the Swiss Light Source (Paul
                        Scherrer Institute, Switzerland). This imaging technique leverages the highly coherent
                        synchrotron X-rays to detect phase shifts in a sample, enabling high-resolution,
                        three-dimensional visualisation of internal structures with enhanced contrast. In order to image
                        the samples completely, several microtomography scans were collected and stitched together to
                        cover the maximum size of the sample. To localise precisely the area of interest in the paraffin
                        block, beeswax pillars were used [<xref ref-type="bibr" rid="c37" hwp:id="xref-ref-37-1"
                                                                hwp:rel-id="ref-37">37</xref>]. For the scan parameters,
                        the TOMCAT beamline was set at a monochromatic beam energy of 21 keV, with a 100 µm Al filter
                        and a 10 µm Fe filter. The X-ray microscope used for imaging had an effective pixel size of
                        1.625 µm (a PCO Edge 5.5 combined with an x4 magnification objective and a LuAG:Ce 150 µm
                        scintillator to convert X-ray to visible light). The propagation distance used was 200 mm. The
                        exposure time was set to 30 ms. For each scan, 3001 projections were captured, along with 30
                        dark images and 2×50 flat-field images. The acquisition was done using a 360° rotation of the
                        sample to artificially increase the total field of view to 8×8×3.5 mm. The scans were completed
                        in less than 3 minutes each. An average of 3 scans was necessary to cover one sample. Before
                        reconstruction, the phase-retrieval algorithm [<xref ref-type="bibr" rid="c38"
                                                                             hwp:id="xref-ref-38-1" hwp:rel-id="ref-38">
                            38</xref>] was used with delta = 3.7e-8 and beta = 1.7e-10 combined with an unsharp mask
                        (stabilizer = 0.3 and width = 1). The tomographic reconstruction was then performed using the
                        Gridrec algorithm, with output slices in an 8-bit format. The ring correction of Vo <italic
                                toggle="yes">et al</italic>. [<xref ref-type="bibr" rid="c39" hwp:id="xref-ref-39-1"
                                                                    hwp:rel-id="ref-39">39</xref>] was used (Window size
                        of 81 - 31 and SNR = 3.0).
                    </p>
                </sec>
            </sec>
            <sec id="s4b" hwp:id="sec-15">
                <title hwp:id="title-21">ST computational work</title>
                <sec id="s4b1" hwp:id="sec-16">
                    <title hwp:id="title-22">ST preprocessing</title>
                    <p hwp:id="p-43">FastQ files were processed with Space Ranger (10x Genomics). Sequence reads were
                        mapped to the mm10 reference transcriptome using the Mouse Transcriptome v1 probe set file.
                        Manual fiducial frame alignment and selection of tissue-covered spots for each sample were
                        performed using Loupe Browser (10x Genomics). We performed basic quality control (QC) on the ST
                        data with SCANPY [<xref ref-type="bibr" rid="c40" hwp:id="xref-ref-40-1" hwp:rel-id="ref-40">
                            40</xref>] by checking the spatial and count distributions. To filter out low-quality spots,
                        we picked manually defined thresholds based on the overall distribution of the total counts and
                        the number of detected genes in the capture spots. We performed normalisation using
                        scanpy.pp.normalize_total with parameters target_sum equals to 1e4 and exclude_highly_expressed
                        set to True. To correct for skewness in gene expression data, we applied log transformation
                        [log(x + 1)] using scanpy.pp.log1p.
                    </p>
                </sec>
                <sec id="s4b2" hwp:id="sec-17">
                    <title hwp:id="title-23">Histopathological annotations</title>
                    <p hwp:id="p-44">Histopathological annotations were manually assessed by trained veterinary
                        pathologists. Three distinct lung tissue regions were identified: inflammation-induced
                        atelectasis, inflammation, and lung parenchyma. Annotation polygons were extracted from
                        Visiopharm software (Visiopharm A/S). These polygons were mapped to ST capture spots using the
                        Shapely Python package [<xref ref-type="bibr" rid="c41" hwp:id="xref-ref-41-1"
                                                      hwp:rel-id="ref-41">41</xref>] based on whether the centroid of
                        each capture spot fell within a given polygon.
                    </p>
                </sec>
                <sec id="s4b3" hwp:id="sec-18">
                    <title hwp:id="title-24">Cross-talk correction between samples</title>
                    <p hwp:id="p-45">During the initial stages of analysis, an issue with the indexing primers was
                        identified, preventing the use of the i5 index for demultiplexing. As a result, demultiplexing
                        was performed using only the i7 index (10 bp). This raised concerns about potential
                        cross-contamination between samples, as the demultiplexing process allows for a 1bp mismatch per
                        index by default, which could contribute to the misassignment of reads.
                    </p>
                    <p hwp:id="p-46">To evaluate the extent of cross-contamination, we focused on the expression of
                        SARS-CoV-2-specific genes, which should be present only in infected samples. However, we
                        detected SARS-CoV-2 marker reads in the mock-treated samples, with spatial expression patterns
                        closely resembling those in the infected samples. This pattern suggested that these reads in the
                        mock-treated samples were not random and likely represent contamination introduced during sample
                        collection.
                    </p>
                    <p hwp:id="p-47">To address this cross-talk between samples, we quantified the expression levels of
                        known SARS-CoV-2 genes (<italic toggle="yes">ORF1ab, S, ORF3a, E, M, N, ORF10</italic>) by
                        estimating background contamination fraction from mock-treated control samples. We calculated
                        gene contamination fractions by dividing the observed expression values by the total gene counts
                        across all datasets. Using these values, we determined the average contamination fraction from
                        the mock samples and computed an upper bound using a 95% confidence interval.
                    </p>
                    <p hwp:id="p-48">To eliminate contamination, we estimated per-gene contamination counts by
                        multiplying the total gene counts by the calculated contamination fraction. We then subtracted
                        these estimated contamination values from the expression matrix, ensuring that any resulting
                        negative values were set to zero.
                    </p>
                </sec>
                <sec id="s4b4" hwp:id="sec-19">
                    <title hwp:id="title-25">Gene expression program inference</title>
                    <p hwp:id="p-49">We used Chrysalis to infer 8 gene expression programs from ST datasets by
                        performing archetypal analysis on spatially variable genes (SVGs). Chrysalis identifies distinct
                        tissue compartments by fitting a simplex to the latent representation, which is derived from the
                        principal components (PCA) of the gene expression matrix. PCA is used to reduce the
                        dimensionality of the gene expression data, capturing the most significant patterns of variation
                        across genes. The simplex is then fitted to these principal components, allowing Chrysalis to
                        find distinct tissue compartments based on key patterns of gene expression. Here, Chrysalis was
                        trained on the 5 dpc mRNA-vaccinated sample (L2210926), instead of the whole cohort, and and the
                        learned transformations were applied to the remaining samples. This strategy minimised the
                        influence of signatures from other samples, preventing external noise from affecting the
                        X-Pression’s training and performance. To infer the gene expression programs, SVGs were
                        calculated on the normalised and logtransformed count matrix (chrysalis.detect_svgs, parameters:
                        min_morans = 0.0, min_spots = 0.05). PCA was performed on the SVG matrix (chrysalis.pca),
                        followed by archetypal analysis (chrysalis.aa), with the number of compartments selected based
                        on the elbow method on the reconstruction error curve.
                    </p>
                </sec>
                <sec id="s4b5" hwp:id="sec-20">
                    <title hwp:id="title-26">Functional characterization and downstream analysis</title>
                    <p hwp:id="p-50">Pathway activity scores for the different cellular pathways and the Hallmarks
                        MsigDB collection [<xref ref-type="bibr" rid="c42" hwp:id="xref-ref-42-1" hwp:rel-id="ref-42">
                            42</xref>] were calculated using PROGENy [<xref ref-type="bibr" rid="c43"
                                                                            hwp:id="xref-ref-43-1" hwp:rel-id="ref-43">
                            43</xref>] and decoupleR [<xref ref-type="bibr" rid="c44" hwp:id="xref-ref-44-1"
                                                            hwp:rel-id="ref-44">44</xref>]. Pathway activities were
                        inferred with a multivariate linear model (decoupler.run_mlm), using the top 500 genes ranked by
                        pathway responses in mice. To compare pathway activity across conditions, mean pathway
                        activities and Hallmarks were calculated by averaging the activity scores of all capture spots
                        across each condition and visualised as heatmaps. To infer associations with cellular niches,
                        pairwise Pearson correlation was calculated with the tissue compartment scores. Cell-type
                        deconvolution was performed with cell2location [<xref ref-type="bibr" rid="c45"
                                                                              hwp:id="xref-ref-45-1"
                                                                              hwp:rel-id="ref-45">45</xref>]. Cell
                        type-specific expression signatures were generated using our scRNA-seq dataset. Genes were
                        initially filtered (adata_ref, cell_count_cutoff = 5, cell_percentage_cutoff2 = 0.03,
                        nonz_mean_cutoff = 1.12) followed by the training of the regression model with a maximum of 1000
                        epochs. We trained the cell2location model (N_cells_per_location = 30, detection_alpha = 20)
                        with epocs = 3000. Cell densities were calculated by dividing the cell type abundance values by
                        the total number of inferred cells.
                    </p>
                </sec>
            </sec>
            <sec id="s4c" hwp:id="sec-21">
                <title hwp:id="title-27">Micro-CT computational work</title>
                <sec id="s4c1" hwp:id="sec-22">
                    <title hwp:id="title-28">Volume handling</title>
                    <p hwp:id="p-51">We utilised Zarr [<xref ref-type="bibr" rid="c46" hwp:id="xref-ref-46-1"
                                                             hwp:rel-id="ref-46">46</xref>] to store large 3D tissue
                        image volumes in a chunked format, enabling us to store and retrieve them without loading the
                        entire volume into memory. After extracting slices and storing them in Zarr, the 3D volumes were
                        loaded into Dask arrays [<xref ref-type="bibr" rid="c47" hwp:id="xref-ref-47-1"
                                                       hwp:rel-id="ref-47">47</xref>]. Dask was used to convert those
                        large 3D volumes (slices from a TIFF file) into a distributed array format, allowing parallel
                        processing to handle all data simultaneously. This approach reduced memory consumption and
                        enhanced performance.
                    </p>
                </sec>
                <sec id="s4c2" hwp:id="sec-23">
                    <title hwp:id="title-29">Volume segmentation</title>
                    <p hwp:id="p-52">To segment the tissue from the background, we used a custom U-Net architecture
                        implemented in Keras [<xref ref-type="bibr" rid="c48" hwp:id="xref-ref-48-1"
                                                    hwp:rel-id="ref-48">48</xref>]. This architecture was trained on
                        axial slices of tomograms, for which tissue masks were manually annotated by a pathologist and
                        used as ground truth for supervised learning. Raw images were subsequently preprocessed by
                        extracting 128×128 patches from the annotated slices with the corresponding binary masks. Data
                        normalisation was performed by scaling intensity values to [0,<xref ref-type="bibr" rid="c1"
                                                                                            hwp:id="xref-ref-1-2"
                                                                                            hwp:rel-id="ref-1">1</xref>].
                    </p>
                    <p hwp:id="p-53">The dataset was split into training, validation, and test sets (80%/10%/10%). The
                        U-Net model was trained using the Adam optimiser (learning_rate = 1e-4) with (1 - dice
                        coefficient) as the loss function. Performance metrics included dice coefficient, intersection
                        over union, recall, and precision. Training was conducted for 200 epochs with early stopping and
                        learning rate reduction to 1e-7.
                    </p>
                    <p hwp:id="p-54">The U-Net used for this task follows an encoder-decoder structure. The encoder
                        consists of four convolutional blocks, each containing two 3×3 convolutional layers with ReLU
                        activation, followed by batch normalisation and 2×2 max pooling for downsampling. The number of
                        filters in the convolutional layers increases progressively from 8 to 64. Each encoder block
                        generates a set of feature maps that will be used as skip connections later.
                    </p>
                    <p hwp:id="p-55">At the bottleneck, two convolutional layers with 3×3 kernels, 128 filters, and ReLU
                        activation are applied, followed by batch normalisation to prevent overfitting. The decoder
                        mirrors the encoder structure with four upsampling blocks. Each block consists of a 2×2
                        transposed convolution for upsampling, followed by concatenation with the corresponding feature
                        map from the encoder via skip connections. Skip connections are in the decoder function, where
                        the upsampled output from the previous decoder block is concatenated with the feature map from
                        the encoder. This allows the decoder to access high-resolution features from the encoder,
                        helping to recover fine spatial details. Two 3×3 convolutional layers with ReLU activation and
                        batch normalisation refine the upsampled features at each stage. The number of filters decreases
                        symmetrically from 64 to 8.
                    </p>
                    <p hwp:id="p-56">The final output layer applies a 1×1 convolution with a sigmoid activation function
                        to generate a segmentation mask, where pixel values represent the probability of belonging to
                        the tissue class. The predicted patches were then stitched together to reconstruct the segmented
                        volume. To separate tissue from the background, we applied Otsu thresholding and removed small
                        objects from the resulting binary mask to eliminate artefacts.
                    </p>
                </sec>
                <sec id="s4c3" hwp:id="sec-24">
                    <title hwp:id="title-30">Multimodal coregistration</title>
                    <p hwp:id="p-57">To infer gene expression profiles, we coregistered the micro-CT volumes with the ST
                        data using BigWarp [<xref ref-type="bibr" rid="c49" hwp:id="xref-ref-49-1" hwp:rel-id="ref-49">
                            49</xref>], an interactive tool in Fiji/ImageJ for landmark-based image alignment. We
                        decided to use Big-Warp as it provides real-time visualisation of transformations. First, we
                        loaded the slice from micro-CT volume and the corresponding H&amp;E image from ST data into
                        BigWarp. The micro-CT volume was set as the fixed reference image and the H&amp;E image as the
                        moving image. We manually selected anatomical landmarks (from 20 to 30 landmarks) that were
                        clearly visible in both images, ensuring an accurate mapping.
                    </p>
                    <p hwp:id="p-58">To compute the spatial transformation, we extracted the landmark coordinates, used
                        a least squares affine transformation, and applied it to the original ST spatial coordinates to
                        align them with the micro-CT volume.
                    </p>
                </sec>
                <sec id="s4c4" hwp:id="sec-25">
                    <title hwp:id="title-31">Convolutional neural network</title>
                    <p hwp:id="p-59">We built a custom 3D CNN to predict expression programs using 3D patches extracted
                        from the preprocessed and coregistered volumes. Our CNN architecture consists of several layers,
                        starting with 3D convolutional layers for feature extraction. These layers use increasing filter
                        sizes of 32, 64, 128, and 256, followed by ReLU activation. After convolution, max pooling
                        layers were applied to downsample the feature maps. Then, the model flattens the output and
                        passes it through a dense layer with 64 units and ReLU activation, followed by a dropout layer
                        with a rate of 0.3 to prevent overfitting. The final output layer consists of 8 units, with a
                        softmax activation function for multi-output regression. For model training, we used the
                        Kullback-Leibler divergence (kl_divergence) as the loss function and the Mean Absolute Error
                        (MAE) as the evaluation metric. The model was optimised using the Adam optimiser with a learning
                        rate of 1e-4. Early stopping was implemented to prevent overfitting, and training was conducted
                        for 75 epochs with a batch size of 32.
                    </p>
                    <p hwp:id="p-60">For multi-output regression, we utilised eight gene expression programs inferred by
                        Chrysalis from the corresponding ST data. We conducted a stratified split of the dataset to
                        address the underrepresentation of certain gene expression programs by defining stratification
                        bins based on the highest expression program value per spot. This ensured that both the training
                        and validation datasets had a balanced representation. The data was split into 80% for training
                        and 20% for validation and testing. The validation and test sets were further divided into 70%
                        for validation and 30% for testing. Augmentation techniques, including 90-degree, 180-degree,
                        and 270-degree rotations, horizontal and vertical flips, as well as combinations of horizontal
                        flips with each rotation, were applied to improve the model’s generalisation ability. Augmented
                        samples were removed from the validation and test sets. The model was trained with 128×128×21
                        patches centred around the spatial capture spots after registration. R<sup>2</sup> scores were
                        computed to assess the model’s performance on the validation, test, and training datasets.
                    </p>
                    <p hwp:id="p-61">During inference, we extracted 3D patches from the entire volumes using a stride of
                        16 to predict the gene expression profiles. We reconstructed the predictions as a matrix, where
                        each element is of the size of 16×16 pixels (26×26 µm), corresponding to the central part of
                        each capture spot, to minimise the effect of transcript diffusion.
                    </p>
                </sec>
                <sec id="s4c5" hwp:id="sec-26">
                    <title hwp:id="title-32">Compartment visualisation for volumetric data</title>
                    <p hwp:id="p-62">To visualise the tissue compartments, we used MIP. We preselected slices from the
                        micro-CT volume at different depths (coronal, sagittal, and axial). Then, for each slice, MIP
                        was applied to the individual compartments and summed together with the corresponding grayscale
                        tomogram slice to visualise the predicted gene expression programs.
                    </p>
                    <p hwp:id="p-63">To visualise each compartment separately in 3D across the entire tissue volume, we
                        used the Imaris Viewer software by Oxford Instruments.
                    </p>
                </sec>
                <sec id="s4c6" hwp:id="sec-27">
                    <title hwp:id="title-33">Correlation of compartments with micro-CT features</title>
                    <p hwp:id="p-64">We calculated Pearson correlation coefficients between morphological features
                        extracted from the micro-CT per patch and the predicted tissue compartments. The morphological
                        features included texture-related measurements (skewness, dissimilarity, correlation, LoG) and
                        intensity-related measurements (edge density, contrast, homogeneity, Angular Second Moment
                        (ASM), energy, elongation, mean intensity, Fourier energy, STD intensity, wavelet energy).
                        Pearson correlation coefficients were computed using the pearsonr function from scipy.stats. A
                        correlation matrix was generated, where each entry represented the Pearson correlation between a
                        specific morphological feature and a compartment class.
                    </p>
                </sec>
                <sec id="s4c7" hwp:id="sec-28">
                    <title hwp:id="title-34">Integrated gradients</title>
                    <p hwp:id="p-65">We applied the IG method to attribute model predictions to input features derived
                        from a 3D patch extracted from the micro-CT image. A 3D patch centred around the X and Y
                        coordinates was extracted and passed through the pre-trained model, and IG attributions were
                        calculated to identify which regions of the image contributed most to the prediction. To achieve
                        this, we first created a baseline input 3D patch with all pixel values set to zero. The input
                        patch was then interpolated between the baseline and the actual image using alpha values ranging
                        from 0 to 1, spaced in 50 steps. For each interpolated image, the gradients of the model’s
                        prediction with respect to the input image were computed. Given that the model’s output is a
                        softmax prediction, the gradients were calculated based on the argmax of the softmax output. The
                        gradients were then averaged between consecutive pairs of interpolated images. Finally, the
                        averaged gradients were scaled by the difference between the input image and the baseline,
                        yielding the importance of each pixel in the image with respect to the model’s prediction. We
                        applied PCA on the resulting attributions and performed UMAP on the first 70 PCs (59% explained
                        variance) to visualise the structure of attribution patterns in a low-dimensional space. For
                        UMAP visualisation, we plotted the corresponding 2D patches by extracting the middle slice from
                        each 3D patch used in the analysis.
                    </p>
                </sec>
                <sec id="s4c8" hwp:id="sec-29">
                    <title hwp:id="title-35">Out-of-sample predictions</title>
                    <p hwp:id="p-66">For the out-of-sample predictions, we used a micro-CT volume (OTS206, 5dpc) that
                        the model had not seen during training. To minimise inconsistencies in contrast across various
                        scans, we applied histogram matching, using the slice employed for model training as a reference
                        slice. For each slice in the micro-CT volume, we adjusted the pixel intensity distribution using
                        skimage.exposure.match_histograms to match that of the reference slice.
                    </p>
                </sec>
            </sec>
            <sec id="s4d" hwp:id="sec-30">
                <title hwp:id="title-36">ScRNA-seq computational work</title>
                <sec id="s4d1" hwp:id="sec-31">
                    <title hwp:id="title-37">ScRNA-seq preprocessing</title>
                    <p hwp:id="p-67">Raw data alignment of FASTQ files and UMIs counting were performed using Cell
                        Ranger (10x Genomics). SCANPY was used to analyse the count matrices. Cells were marked as
                        outliers following the single cell best practices by using rather lenient thresholds to avoid
                        excluding smaller subpopulations, filtering cells out being on the left side (&lt;) or the right
                        side (&gt;) of the median, median absolute deviations (MAD). We marked cells as outliers for
                        log1p_total_counts, log1p_n_genes_by_counts, pct_counts_in_top_20_genes, MAD &lt; 3 or &gt; 5,
                        and cells with a percentage of mitochondrial genes &gt; 5 and MAD &lt; 5 or &gt; 5, and for
                        cells with a percentage of haemoglobin genes &gt; 3 and MAD &lt; 3 or &gt; 3 [<xref
                                ref-type="bibr" rid="c50" hwp:id="xref-ref-50-1" hwp:rel-id="ref-50">50</xref>].
                        Following this, each sample was individually reassessed and processed for removal of doublets
                        using scrublet [<xref ref-type="bibr" rid="c51" hwp:id="xref-ref-51-1" hwp:rel-id="ref-51">
                            51</xref>], which simulates doublets from the cells in each sample and automatically
                        establishes a threshold based on a k-nearest-neighbours classifier that determines which cells
                        are estimated as doublets. PCA was applied to each sample to visualise the scores and results of
                        the filtering steps before integration.
                    </p>
                </sec>
                <sec id="s4d2" hwp:id="sec-32">
                    <title hwp:id="title-38">ScRNA-seq integration, clustering, and annotation</title>
                    <p hwp:id="p-68">scVI [<xref ref-type="bibr" rid="c52" hwp:id="xref-ref-52-1" hwp:rel-id="ref-52">
                        52</xref>] was used to integrate and batch-correct the data based on the batch covariate
                        (sample) with standard parameters besides, the n_latent = 30, n_layers = 2, batch_size = 64, and
                        n_hidden = 64 arguments, for obtaining the embeddings. These results served as input for UMAP,
                        and clustering was conducted at various resolutions to analyse the granularity of the biological
                        populations using the Leiden algorithm, along with the ranking of highly variable genes in each
                        cluster at different stages. For the first round of automatic annotations, we used scimilarity [<xref
                                ref-type="bibr" rid="c53" hwp:id="xref-ref-53-1" hwp:rel-id="ref-53">53</xref>] followed
                        by a second round of annotations using celltypist [<xref ref-type="bibr" rid="c54"
                                                                                 hwp:id="xref-ref-54-1"
                                                                                 hwp:rel-id="ref-54">54</xref>] to
                        impute the best-scoring cell types. The assigned labels were subsequently revised manually.
                    </p>
                </sec>
            </sec>
            <sec id="s4e" hwp:id="sec-33">
                <title hwp:id="title-39">Statistics and reproducibility</title>
                <p hwp:id="p-69">Statistical methods for each analysis are detailed in their respective sections.
                    Pearson correlation coefficients were computed with the pearsonr function from SciPy’s stats module.
                    Visualisations, including scatterplots, box plots, bar plots, stack plots, histograms, KDE plots,
                    heatmaps, and line plots, were created using matplotlib [<xref ref-type="bibr" rid="c55"
                                                                                   hwp:id="xref-ref-55-1"
                                                                                   hwp:rel-id="ref-55">55</xref>],
                    SCANPY, chrysalis, and seaborn[<xref ref-type="bibr" rid="c56" hwp:id="xref-ref-56-1"
                                                         hwp:rel-id="ref-56">56</xref>]. We utilised the
                    sklearn.metrics.r2_score function to calculate the R2 metric. For the CNN model, KL divergence was
                    computed using TensorFlow’s reduce_mean, reduce_sum, and math.log functions. The investigators were
                    blinded to treatment status during the experiments.
                </p>
            </sec>
        </sec>
        <sec sec-type="supplementary-material" hwp:id="sec-34">
            <title hwp:id="title-40">Supporting information</title>
            <supplementary-material position="float" orientation="portrait" hwp:id="DC1">
                <object-id pub-id-type="other" hwp:sub-type="slug">DC1</object-id>
                <label>Supplementary Information</label>
                <media xlink:href="supplements/644627_file06.pdf" position="float" orientation="portrait"
                       hwp:id="media-1"/>
            </supplementary-material>
        </sec>
    </body>
    <back>
        <sec id="s5" sec-type="data-availability" hwp:id="sec-35">
            <title hwp:id="title-41">Data availability</title>
            <p hwp:id="p-70">Raw and processed RNA sequencing data generated by Cell Ranger and Space Ranger will be
                available on the Gene Expression Omnibus (GEO) database in the next version of the manuscript. All
                processed single-cell, ST, and micro-CT data necessary to replicate the analysis presented in this work
                are available at the Zenodo data archive (<ext-link l:rel="related" l:ref-type="uri"
                                                                    l:ref="https://doi.org/10.5281/zenodo.15064778"
                                                                    ext-link-type="uri"
                                                                    xlink:href="https://doi.org/10.5281/zenodo.15064778"
                                                                    hwp:id="ext-link-1">
                    https://doi.org/10.5281/zenodo.15064778</ext-link>). The uploaded dataset includes count matrices,
                AnnData objects, as well as results for cell type deconvolution, tissue compartment inference, and
                additional metadata.
            </p>
        </sec>
        <sec id="s6" hwp:id="sec-36">
            <title hwp:id="title-42">Code availability</title>
            <p hwp:id="p-71">X-Pression is available at <ext-link l:rel="related" l:ref-type="uri"
                                                                  l:ref="https://github.com/rockdeme/x-pression"
                                                                  ext-link-type="uri"
                                                                  xlink:href="https://github.com/rockdeme/x-pression"
                                                                  hwp:id="ext-link-2">
                https://github.com/rockdeme/x-pression</ext-link>.
            </p>
        </sec>
        <ack hwp:id="ack-1">
            <title hwp:id="title-43">Acknowledgements</title>
            <p hwp:id="p-72">We thank H.Y. Stoller-Kwan from the Institute of Hospital Pharmacy at the University
                Hospital of Bern and U. Romanelli from the Department of Infectious Diseases, Tropical Medicine and
                Travel Medicine at the University Hospital of Bern for providing the mRNA vaccine. We thank the Next
                Generation Sequencing Platform of the University of Bern for performing the high-throughput sequencing
                experiments and the COMPATH platform of the Institute of Pathology and the Institute of Animal Pathology
                of the University of Bern for performing the pathological analyses. We acknowledge the Paul Scherrer
                Institut, Villigen, Switzerland for the provision of synchrotron radiation beamtime at the TOMCAT
                beamline X02DA of the SLS. This project was supported by the Novartis Foundation for Medical-Biological
                Research (grant 22B099 to E.A.M.).
            </p>
        </ack>
        <sec id="s7" hwp:id="sec-37">
            <title hwp:id="title-44">Author Information</title>
            <p hwp:id="p-73">
                <bold>Contributions.</bold>
                D.T., L.G., V.T. and E.A.M. designed the study. D.T. and L.G. designed computational models, algorithms,
                and validation. E.A.M. developed the experimental methodology and validation. E.A.M., N.E. and G.T.B.
                performed the mice experiments. M.B. performed scRNA-seq analysis. I.B.V., L.GR. and C.H. performed
                histopathological validation and tissue annotations. A.B. acquired the micro-CT images. A.C. performed
                tissue sectioning and ST experiments. D.T., L.G. and E.A.M. drafted the manuscript. A.V., S.R., V.T.,
                and E.A.M. supervised the research. All authors revised and approved the published version of the
                manuscript.
            </p>
        </sec>
        <sec id="s8" hwp:id="sec-38">
            <title hwp:id="title-45">Ethics declarations</title>
            <p hwp:id="p-74">Mouse studies were approved by the Animal Experiments Committee of the Cantonal Veterinary
                Office of Bern and conducted in accordance with Swiss animal welfare legislation under licence BE43/20.
            </p>
        </sec>
        <sec id="s9" hwp:id="sec-39">
            <title hwp:id="title-46">Competing interests</title>
            <p hwp:id="p-75">A.V. and I.B.V. are currently employed by F. Hoffmann-La Roche Ltd. The University of Bern
                has patented the utilisation of OTS206 as a vaccine, with G.T.B., N.E., and V.T. listed as inventors.
                The development of OTS206 involved collaboration between the University of Bern and Rocketvax AG, with
                financial support being provided to the University by Rocketvax AG. V.T. is currently providing
                consultancy services to Rocketvax AG. The remaining authors have declared no competing interests.
            </p>
        </sec>
        <ref-list hwp:id="ref-list-1">
            <title hwp:id="title-47">Bibliography</title>
            <ref id="c1" hwp:id="ref-1" hwp:rev-id="xref-ref-1-1 xref-ref-1-2">
                <label>1.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.1"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-1">
                    <string-name name-style="western" hwp:sortable="Liu Longqi">
                        <given-names>Longqi</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen Ao">
                        <given-names>Ao</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Yuxiang">
                        <given-names>Yuxiang</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mulder Jan">
                        <given-names>Jan</given-names>
                        <surname>Mulder</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Heyn Holger">
                        <given-names>Holger</given-names>
                        <surname>Heyn</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Xu Xun">
                        <given-names>Xun</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-2">Spatiotemporal omics for biology and
                    medicine</article-title>. <source hwp:id="source-1">Cell</source>, <volume>187</volume>(<issue>
                    17</issue>):<fpage>4488</fpage>–<lpage>4519</lpage>, <year>2024</year>. ISSN <issn>0092-8674</issn>.
                    doi: <pub-id pub-id-type="doi">10.1016/j.cell.2024.07.040</pub-id>.
                </citation>
            </ref>
            <ref id="c2" hwp:id="ref-2">
                <label>2.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.2"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-2">
                    <string-name name-style="western" hwp:sortable="Wu Yingcheng">
                        <given-names>Yingcheng</given-names>
                        <surname>Wu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cheng Yifei">
                        <given-names>Yifei</given-names>
                        <surname>Cheng</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Xiangdong">
                        <given-names>Xiangdong</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fan Jia">
                        <given-names>Jia</given-names>
                        <surname>Fan</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Gao Qiang">
                        <given-names>Qiang</given-names>
                        <surname>Gao</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-3">Spatial omics: Navigating to the golden era of cancer
                    research</article-title>. <source hwp:id="source-2">Clinical and Translational
                    Medicine</source>, <volume>12</volume>(<issue>1</issue>): <fpage>e696</fpage>, <year>2022</year>.
                    ISSN <issn>2001-1326</issn>. doi: <pub-id pub-id-type="doi">10.1002/ctm2.696</pub-id>.
                </citation>
            </ref>
            <ref id="c3" hwp:id="ref-3" hwp:rev-id="xref-ref-3-1">
                <label>3.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.3"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-3">
                    <string-name name-style="western" hwp:sortable="Choe Kyongho">
                        <given-names>Kyongho</given-names>
                        <surname>Choe</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pak Unil">
                        <given-names>Unil</given-names>
                        <surname>Pak</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pang Yu">
                        <given-names>Yu</given-names>
                        <surname>Pang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hao Wanjun">
                        <given-names>Wanjun</given-names>
                        <surname>Hao</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Yang Xiuqin">
                        <given-names>Xiuqin</given-names>
                        <surname>Yang</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-4">Advances and Challenges in Spatial Transcriptomics for
                    Developmental Biology</article-title>. <source hwp:id="source-3">Biomolecules</source>, <volume>
                    13</volume>(<issue>1</issue>):<fpage>156</fpage>, <year>2023</year>. doi: <pub-id pub-id-type="doi">
                    10.3390/biom13010156</pub-id>.
                </citation>
            </ref>
            <ref id="c4" hwp:id="ref-4" hwp:rev-id="xref-ref-4-1">
                <label>4.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.4"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-4">
                    <string-name name-style="western" hwp:sortable="Bressan Dario">
                        <given-names>Dario</given-names>
                        <surname>Bressan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Battistoni Giorgia">
                        <given-names>Giorgia</given-names>
                        <surname>Battistoni</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Hannon Gregory J.">
                        <given-names>Gregory J.</given-names>
                        <surname>Hannon</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-5">The dawn of spatial omics</article-title>. <source
                        hwp:id="source-4">Science</source>, <volume>381</volume>(<issue>6657</issue>):<fpage>
                    eabq4964</fpage>–<lpage>eabq4964</lpage>, <year>2023</year>. ISSN <issn>0036-8075</issn>.
                    doi: <pub-id pub-id-type="doi">10.1126/science.abq4964</pub-id>.
                </citation>
            </ref>
            <ref id="c5" hwp:id="ref-5">
                <label>5.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.5"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-5">
                    <string-name name-style="western" hwp:sortable="Tian Luyi">
                        <given-names>Luyi</given-names>
                        <surname>Tian</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen Fei">
                        <given-names>Fei</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Macosko Evan Z.">
                        <given-names>Evan Z.</given-names>
                        <surname>Macosko</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-6">The expanding vistas of spatial
                    transcriptomics</article-title>. <source hwp:id="source-5">Nature Biotechnology</source>,
                    pages <fpage>1</fpage>–<lpage>10</lpage>, <year>2022</year>. ISSN <issn>1087-0156</issn>.
                    doi: <pub-id pub-id-type="doi">10.1038/s41587-022-01448-2</pub-id>.
                </citation>
            </ref>
            <ref id="c6" hwp:id="ref-6" hwp:rev-id="xref-ref-6-1">
                <label>6.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.6"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-6">
                    <string-name name-style="western" hwp:sortable="Cheng Mengnan">
                        <given-names>Mengnan</given-names>
                        <surname>Cheng</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jiang Yujia">
                        <given-names>Yujia</given-names>
                        <surname>Jiang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xu Jiangshan">
                        <given-names>Jiangshan</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mentis Alexios-Fotios A.">
                        <given-names>Alexios-Fotios A.</given-names>
                        <surname>Mentis</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Shuai">
                        <given-names>Shuai</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zheng Huiwen">
                        <given-names>Huiwen</given-names>
                        <surname>Zheng</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sahu Sunil Kumar">
                        <given-names>Sunil Kumar</given-names>
                        <surname>Sahu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Liu Longqi">
                        <given-names>Longqi</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Xu Xun">
                        <given-names>Xun</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-7">Spatially resolved transcriptomics: a comprehensive review
                    of their technological advances, applications, and challenges</article-title>. <source
                        hwp:id="source-6">Journal of Genetics and Genomics</source>, <volume>50</volume>(<issue>
                    9</issue>):<fpage>625</fpage>–<lpage>640</lpage>, <year>2023</year>. ISSN <issn>1673-8527</issn>.
                    doi: <pub-id pub-id-type="doi">10.1016/j.jgg.2023.03.011</pub-id>.
                </citation>
            </ref>
            <ref id="c7" hwp:id="ref-7" hwp:rev-id="xref-ref-7-1">
                <label>7.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.7"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-7">
                    <string-name name-style="western" hwp:sortable="Moses Lambda">
                        <given-names>Lambda</given-names>
                        <surname>Moses</surname>
                    </string-name>
                    and
                    <string-name name-style="western" hwp:sortable="Pachter Lior">
                        <given-names>Lior</given-names>
                        <surname>Pachter</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-8">Museum of spatial transcriptomics</article-title>. <source
                        hwp:id="source-7">Nature Methods</source>, <volume>19</volume> (<issue>5</issue>):<fpage>
                    534</fpage>–<lpage>546</lpage>, <year>2022</year>. ISSN <issn>1548-7091</issn>. doi: <pub-id
                        pub-id-type="doi">10.1038/s41592-022-01409-2</pub-id>.
                </citation>
            </ref>
            <ref id="c8" hwp:id="ref-8" hwp:rev-id="xref-ref-8-1">
                <label>8.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.8"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-8">
                    <string-name name-style="western" hwp:sortable="Schott Marie">
                        <given-names>Marie</given-names>
                        <surname>Schott</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="León-Periñán Daniel">
                        <given-names>Daniel</given-names>
                        <surname>León-Periñán</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Splendiani Elena">
                        <given-names>Elena</given-names>
                        <surname>Splendiani</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Strenger Leon">
                        <given-names>Leon</given-names>
                        <surname>Strenger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Licha Jan Robin">
                        <given-names>Jan Robin</given-names>
                        <surname>Licha</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pentimalli Tancredi Massimo">
                        <given-names>Tancredi Massimo</given-names>
                        <surname>Pentimalli</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schallenberg Simon">
                        <given-names>Simon</given-names>
                        <surname>Schallenberg</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Alles Jonathan">
                        <given-names>Jonathan</given-names>
                        <surname>Alles</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tagliaferro Sarah Samut">
                        <given-names>Sarah Samut</given-names>
                        <surname>Tagliaferro</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Boltengagen Anastasiya">
                        <given-names>Anastasiya</given-names>
                        <surname>Boltengagen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ehrig Sebastian">
                        <given-names>Sebastian</given-names>
                        <surname>Ehrig</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Abbiati Stefano">
                        <given-names>Stefano</given-names>
                        <surname>Abbiati</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dommerich Steffen">
                        <given-names>Steffen</given-names>
                        <surname>Dommerich</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pagani Massimiliano">
                        <given-names>Massimiliano</given-names>
                        <surname>Pagani</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ferretti Elisabetta">
                        <given-names>Elisabetta</given-names>
                        <surname>Ferretti</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Macino Giuseppe">
                        <given-names>Giuseppe</given-names>
                        <surname>Macino</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Karaiskos Nikos">
                        <given-names>Nikos</given-names>
                        <surname>Karaiskos</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Rajewsky Nikolaus">
                        <given-names>Nikolaus</given-names>
                        <surname>Rajewsky</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-9">Open-ST: High-resolution spatial transcriptomics in
                    3D</article-title>. <source hwp:id="source-8">Cell</source>, <volume>187</volume>(<issue>15</issue>):<fpage>
                    3953</fpage>–<lpage>3972</lpage>.e26, <year>2024</year>. ISSN <issn>0092-8674</issn>. doi: <pub-id
                        pub-id-type="doi">10.1016/j.cell.2024.05.055</pub-id>.
                </citation>
            </ref>
            <ref id="c9" hwp:id="ref-9">
                <label>9.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.9"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-9">
                    <string-name name-style="western" hwp:sortable="Zeira Ron">
                        <given-names>Ron</given-names>
                        <surname>Zeira</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Land Max">
                        <given-names>Max</given-names>
                        <surname>Land</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Strzalkowski Alexander">
                        <given-names>Alexander</given-names>
                        <surname>Strzalkowski</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Raphael Benjamin J.">
                        <given-names>Benjamin J.</given-names>
                        <surname>Raphael</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-10">Alignment and integration of spatial transcriptomics
                    data</article-title>. <source hwp:id="source-9">Nature Methods</source>, pages <fpage>
                    1</fpage>–<lpage>9</lpage>, <year>2022</year>. ISSN <issn>1548-7091</issn>. doi: <pub-id
                        pub-id-type="doi">10.1038/s41592-022-01459-6</pub-id>.
                </citation>
            </ref>
            <ref id="c10" hwp:id="ref-10">
                <label>10.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.10"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-10">
                    <string-name name-style="western" hwp:sortable="Mir Clara Martínez">
                        <given-names>Clara Martínez</given-names>
                        <surname>Mir</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pisterzi Paola">
                        <given-names>Paola</given-names>
                        <surname>Pisterzi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="De Poorter Isabel">
                        <given-names>Isabel</given-names>
                        <surname>De Poorter</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rilou Maria">
                        <given-names>Maria</given-names>
                        <surname>Rilou</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="van Kranenburg Melissa">
                        <given-names>Melissa</given-names>
                        <surname>van Kranenburg</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Heijs Bram">
                        <given-names>Bram</given-names>
                        <surname>Heijs</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Alemany Anna">
                        <given-names>Anna</given-names>
                        <surname>Alemany</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sage Fanny">
                        <given-names>Fanny</given-names>
                        <surname>Sage</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Geijsen Niels">
                        <given-names>Niels</given-names>
                        <surname>Geijsen</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-11">Spatial multi-omics in whole skeletal muscle reveals
                    complex tissue architecture</article-title>. <source hwp:id="source-10">Communications
                    Biology</source>, <volume>7</volume>(<issue>1</issue>):<fpage>1272</fpage>, <year>2024</year>. doi: <pub-id
                        pub-id-type="doi">10.1038/s42003-024-06949-1</pub-id>.
                </citation>
            </ref>
            <ref id="c11" hwp:id="ref-11">
                <label>11.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.11"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-11">
                    <string-name name-style="western" hwp:sortable="Xu Hao">
                        <given-names>Hao</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Shuyan">
                        <given-names>Shuyan</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fang Minghao">
                        <given-names>Minghao</given-names>
                        <surname>Fang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Luo Songwen">
                        <given-names>Songwen</given-names>
                        <surname>Luo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen Chunpeng">
                        <given-names>Chunpeng</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wan Siyuan">
                        <given-names>Siyuan</given-names>
                        <surname>Wan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Rirui">
                        <given-names>Rirui</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tang Meifang">
                        <given-names>Meifang</given-names>
                        <surname>Tang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xue Tian">
                        <given-names>Tian</given-names>
                        <surname>Xue</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Bin">
                        <given-names>Bin</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lin Jun">
                        <given-names>Jun</given-names>
                        <surname>Lin</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Qu Kun">
                        <given-names>Kun</given-names>
                        <surname>Qu</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-12">SPACEL: deep learning-based characterization of spatial
                    transcriptome architectures</article-title>. <source hwp:id="source-11">Nature
                    Communications</source>, <volume>14</volume> (<issue>1</issue>):<fpage>7603</fpage>, <year>
                    2023</year>. doi: <pub-id pub-id-type="doi">10.1038/s41467-023-43220-3</pub-id>.
                </citation>
            </ref>
            <ref id="c12" hwp:id="ref-12" hwp:rev-id="xref-ref-12-1">
                <label>12.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.12"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-12">
                    <string-name name-style="western" hwp:sortable="Xia Chen-Rui">
                        <given-names>Chen-Rui</given-names>
                        <surname>Xia</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cao Zhi-Jie">
                        <given-names>Zhi-Jie</given-names>
                        <surname>Cao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tu Xin-Ming">
                        <given-names>Xin-Ming</given-names>
                        <surname>Tu</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Gao Ge">
                        <given-names>Ge</given-names>
                        <surname>Gao</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-13">Spatial-linked alignment tool (SLAT) for aligning
                    heterogenous slices</article-title>. <source hwp:id="source-12">Nature
                    Communications</source>, <volume>14</volume>(<issue>1</issue>):<fpage>7236</fpage>, <year>
                    2023</year>. doi: <pub-id pub-id-type="doi">10.1038/s41467-023-43105-5</pub-id>.
                </citation>
            </ref>
            <ref id="c13" hwp:id="ref-13" hwp:rev-id="xref-ref-13-1">
                <label>13.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.13"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-13">
                    <string-name name-style="western" hwp:sortable="Kuett Laura">
                        <given-names>Laura</given-names>
                        <surname>Kuett</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Catena Raúl">
                        <given-names>Raúl</given-names>
                        <surname>Catena</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Özcan Alaz">
                        <given-names>Alaz</given-names>
                        <surname>Özcan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Plüss Alex">
                        <given-names>Alex</given-names>
                        <surname>Plüss</surname>
                    </string-name>
                    , <collab hwp:id="collab-1">Cancer Grand Challenges IMAXT Consortium</collab>,
                    <string-name name-style="western" hwp:sortable="Ali H R">
                        <given-names>H R</given-names>
                        <surname>Ali</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sa’d M Al">
                        <given-names>M Al</given-names>
                        <surname>Sa’d</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Alon S">
                        <given-names>S</given-names>
                        <surname>Alon</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Aparicio S">
                        <given-names>S</given-names>
                        <surname>Aparicio</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Battistoni G">
                        <given-names>G</given-names>
                        <surname>Battistoni</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Balasubramanian S">
                        <given-names>S</given-names>
                        <surname>Balasubramanian</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Becker R">
                        <given-names>R</given-names>
                        <surname>Becker</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bodenmiller B">
                        <given-names>B</given-names>
                        <surname>Bodenmiller</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Boyden E S">
                        <given-names>E S</given-names>
                        <surname>Boyden</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bressan D">
                        <given-names>D</given-names>
                        <surname>Bressan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bruna A">
                        <given-names>A</given-names>
                        <surname>Bruna</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Burger Marcel">
                        <given-names>Marcel</given-names>
                        <surname>Burger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Caldas C">
                        <given-names>C</given-names>
                        <surname>Caldas</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Callari M">
                        <given-names>M</given-names>
                        <surname>Callari</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cannell I G">
                        <given-names>I G</given-names>
                        <surname>Cannell</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Casbolt H">
                        <given-names>H</given-names>
                        <surname>Casbolt</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chornay N">
                        <given-names>N</given-names>
                        <surname>Chornay</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cui Y">
                        <given-names>Y</given-names>
                        <surname>Cui</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dariush A">
                        <given-names>A</given-names>
                        <surname>Dariush</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dinh K">
                        <given-names>K</given-names>
                        <surname>Dinh</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Emenari A">
                        <given-names>A</given-names>
                        <surname>Emenari</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Eyal-Lubling Y">
                        <given-names>Y</given-names>
                        <surname>Eyal-Lubling</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fan J">
                        <given-names>J</given-names>
                        <surname>Fan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fatemi A">
                        <given-names>A</given-names>
                        <surname>Fatemi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fisher E">
                        <given-names>E</given-names>
                        <surname>Fisher</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="González-Solares E A">
                        <given-names>E A</given-names>
                        <surname>González-Solares</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="González-Fernández C">
                        <given-names>C</given-names>
                        <surname>González-Fernández</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Goodwin D">
                        <given-names>D</given-names>
                        <surname>Goodwin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Greenwood W">
                        <given-names>W</given-names>
                        <surname>Greenwood</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Grimaldi F">
                        <given-names>F</given-names>
                        <surname>Grimaldi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hannon G J">
                        <given-names>G J</given-names>
                        <surname>Hannon</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Harris S">
                        <given-names>S</given-names>
                        <surname>Harris</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jauset C">
                        <given-names>C</given-names>
                        <surname>Jauset</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Joyce J A">
                        <given-names>J A</given-names>
                        <surname>Joyce</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Karagiannis E D">
                        <given-names>E D</given-names>
                        <surname>Karagiannis</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kovac?evic T">
                        <given-names>T</given-names>
                        <surname>Kovac?evic</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kuett L">
                        <given-names>L</given-names>
                        <surname>Kuett</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kunes R">
                        <given-names>R</given-names>
                        <surname>Kunes</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yoldas A Küpcü">
                        <given-names>A Küpcü</given-names>
                        <surname>Yoldas</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lai D">
                        <given-names>D</given-names>
                        <surname>Lai</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Laks E">
                        <given-names>E</given-names>
                        <surname>Laks</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lee H">
                        <given-names>H</given-names>
                        <surname>Lee</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lee M">
                        <given-names>M</given-names>
                        <surname>Lee</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lerda G">
                        <given-names>G</given-names>
                        <surname>Lerda</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Y">
                        <given-names>Y</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="McPherson A">
                        <given-names>A</given-names>
                        <surname>McPherson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Millar N">
                        <given-names>N</given-names>
                        <surname>Millar</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mulvey C M">
                        <given-names>C M</given-names>
                        <surname>Mulvey</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Nugent I">
                        <given-names>I</given-names>
                        <surname>Nugent</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="O’Flanagan C H">
                        <given-names>C H</given-names>
                        <surname>O’Flanagan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Paez-Ribes M">
                        <given-names>M</given-names>
                        <surname>Paez-Ribes</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pearsall I">
                        <given-names>I</given-names>
                        <surname>Pearsall</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Qosaj F">
                        <given-names>F</given-names>
                        <surname>Qosaj</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Roth A J">
                        <given-names>A J</given-names>
                        <surname>Roth</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rueda O M">
                        <given-names>O M</given-names>
                        <surname>Rueda</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ruiz T">
                        <given-names>T</given-names>
                        <surname>Ruiz</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sawicka K">
                        <given-names>K</given-names>
                        <surname>Sawicka</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sepúlveda L A">
                        <given-names>L A</given-names>
                        <surname>Sepúlveda</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Shah S P">
                        <given-names>S P</given-names>
                        <surname>Shah</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Shea A">
                        <given-names>A</given-names>
                        <surname>Shea</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sinha A">
                        <given-names>A</given-names>
                        <surname>Sinha</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Smith A">
                        <given-names>A</given-names>
                        <surname>Smith</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tavaré S">
                        <given-names>S</given-names>
                        <surname>Tavaré</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tietscher S">
                        <given-names>S</given-names>
                        <surname>Tietscher</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Vázquez-García I">
                        <given-names>I</given-names>
                        <surname>Vázquez-García</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Vogl S L">
                        <given-names>S L</given-names>
                        <surname>Vogl</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Walton N A">
                        <given-names>N A</given-names>
                        <surname>Walton</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wassie A T">
                        <given-names>A T</given-names>
                        <surname>Wassie</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Watson S S">
                        <given-names>S S</given-names>
                        <surname>Watson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Weselak J">
                        <given-names>J</given-names>
                        <surname>Weselak</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wild S A">
                        <given-names>S A</given-names>
                        <surname>Wild</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Williams E">
                        <given-names>E</given-names>
                        <surname>Williams</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Windhager J">
                        <given-names>J</given-names>
                        <surname>Windhager</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xia C">
                        <given-names>C</given-names>
                        <surname>Xia</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zheng P">
                        <given-names>P</given-names>
                        <surname>Zheng</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhuang X">
                        <given-names>X</given-names>
                        <surname>Zhuang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schraml Peter">
                        <given-names>Peter</given-names>
                        <surname>Schraml</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Moch Holger">
                        <given-names>Holger</given-names>
                        <surname>Moch</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="de Souza Natalie">
                        <given-names>Natalie</given-names>
                        <surname>de Souza</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Bodenmiller Bernd">
                        <given-names>Bernd</given-names>
                        <surname>Bodenmiller</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-14">Three-dimensional imaging mass cytometry for highly
                    multiplexed molecular and cellular mapping of tissues and the tumor microenvironment</article-title>. <source
                        hwp:id="source-13">Nature Cancer</source>, <volume>3</volume>(<issue>1</issue>):<fpage>
                    122</fpage>–<lpage>133</lpage>, <year>2022</year>. doi: <pub-id pub-id-type="doi">
                    10.1038/s43018-021-00301-w</pub-id>.
                </citation>
            </ref>
            <ref id="c14" hwp:id="ref-14" hwp:rev-id="xref-ref-14-1 xref-ref-14-2">
                <label>14.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.14"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-14">
                    <string-name name-style="western" hwp:sortable="Fang Rongxin">
                        <given-names>Rongxin</given-names>
                        <surname>Fang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Halpern Aaron">
                        <given-names>Aaron</given-names>
                        <surname>Halpern</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rahman Mohammed Mostafizur">
                        <given-names>Mohammed Mostafizur</given-names>
                        <surname>Rahman</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Huang Zhengkai">
                        <given-names>Zhengkai</given-names>
                        <surname>Huang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lei Zhiyun">
                        <given-names>Zhiyun</given-names>
                        <surname>Lei</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hell Sebastian J">
                        <given-names>Sebastian J</given-names>
                        <surname>Hell</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dulac Catherine">
                        <given-names>Catherine</given-names>
                        <surname>Dulac</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Zhuang Xiaowei">
                        <given-names>Xiaowei</given-names>
                        <surname>Zhuang</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-15">Three-dimensional single-cell transcriptome imaging of
                    thick tissues</article-title>. <source hwp:id="source-14">eLife, 12(Pt 1):RP90029</source>, <year>
                    2024</year>. ISSN <issn>0021-8782</issn>. doi: <pub-id pub-id-type="doi">
                    10.7554/elife.90029</pub-id>.
                </citation>
            </ref>
            <ref id="c15" hwp:id="ref-15" hwp:rev-id="xref-ref-15-1">
                <label>15.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.15"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-15">
                    <string-name name-style="western" hwp:sortable="Fiorelli Roberto">
                        <given-names>Roberto</given-names>
                        <surname>Fiorelli</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sidhu Gurpaul S.">
                        <given-names>Gurpaul S.</given-names>
                        <surname>Sidhu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cebrián-Silla Arantxa">
                        <given-names>Arantxa</given-names>
                        <surname>Cebrián-Silla</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Melendez Ernesto Luna">
                        <given-names>Ernesto Luna</given-names>
                        <surname>Melendez</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mehta Shwetal">
                        <given-names>Shwetal</given-names>
                        <surname>Mehta</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Garcia-Verdugo Jose M.">
                        <given-names>Jose M.</given-names>
                        <surname>Garcia-Verdugo</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Sanai Nader">
                        <given-names>Nader</given-names>
                        <surname>Sanai</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-16">Enhanced tissue penetration of antibodies through
                    pressurized immunohistochemistry</article-title>. <source hwp:id="source-15">bioRxiv</source>, page
                    2020.09.25.311936, <year>2020</year>. doi: <pub-id pub-id-type="doi">
                    10.1101/2020.09.25.311936</pub-id>.
                </citation>
            </ref>
            <ref id="c16" hwp:id="ref-16" hwp:rev-id="xref-ref-16-1">
                <label>16.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.16"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-16">
                    <string-name name-style="western" hwp:sortable="Ijsselsteijn Marieke E.">
                        <given-names>Marieke E.</given-names>
                        <surname>Ijsselsteijn</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="van der Breggen Ruud">
                        <given-names>Ruud</given-names>
                        <surname>van der Breggen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sarasqueta Arantza Farina">
                        <given-names>Arantza Farina</given-names>
                        <surname>Sarasqueta</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Koning Frits">
                        <given-names>Frits</given-names>
                        <surname>Koning</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="de Miranda Noel F. C. C.">
                        <given-names>Noel F. C. C.</given-names>
                        <surname>de Miranda</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-17">A 40-Marker Panel for High Dimensional Characterization
                    of Cancer Immune Microenvironments by Imaging Mass Cytometry</article-title>. <source
                        hwp:id="source-16">Frontiers in Immunology</source>, <volume>10</volume>:<issue>
                    2534</issue>, <year>2019</year>. doi: <pub-id pub-id-type="doi">10.3389/fimmu.2019.02534</pub-id>.
                </citation>
            </ref>
            <ref id="c17" hwp:id="ref-17" hwp:rev-id="xref-ref-17-1">
                <label>17.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.17"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-17">
                    <string-name name-style="western" hwp:sortable="Liu Jonathan T. C.">
                        <given-names>Jonathan T. C.</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Glaser Adam K.">
                        <given-names>Adam K.</given-names>
                        <surname>Glaser</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bera Kaustav">
                        <given-names>Kaustav</given-names>
                        <surname>Bera</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="True Lawrence D.">
                        <given-names>Lawrence D.</given-names>
                        <surname>True</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Reder Nicholas P.">
                        <given-names>Nicholas P.</given-names>
                        <surname>Reder</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Eliceiri Kevin W.">
                        <given-names>Kevin W.</given-names>
                        <surname>Eliceiri</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Madabhushi Anant">
                        <given-names>Anant</given-names>
                        <surname>Madabhushi</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-18">Harnessing non-destructive 3D pathology</article-title>. <source
                        hwp:id="source-17">Nature Biomedical Engineering</source>, <volume>5</volume>(<issue>3</issue>):<fpage>
                    203</fpage>–<lpage>218</lpage>, <year>2021</year>. doi: <pub-id pub-id-type="doi">
                    10.1038/s41551-020-00681-x</pub-id>.
                </citation>
            </ref>
            <ref id="c18" hwp:id="ref-18" hwp:rev-id="xref-ref-18-1">
                <label>18.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.18"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-18">
                    <string-name name-style="western" hwp:sortable="Liu Jonathan TC">
                        <given-names>Jonathan TC</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chow Sarah SL">
                        <given-names>Sarah SL</given-names>
                        <surname>Chow</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Colling Richard">
                        <given-names>Richard</given-names>
                        <surname>Colling</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Downes Michelle R">
                        <given-names>Michelle R</given-names>
                        <surname>Downes</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Farré Xavier">
                        <given-names>Xavier</given-names>
                        <surname>Farré</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Humphrey Peter">
                        <given-names>Peter</given-names>
                        <surname>Humphrey</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Janowczyk Andrew">
                        <given-names>Andrew</given-names>
                        <surname>Janowczyk</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mirtti Tuomas">
                        <given-names>Tuomas</given-names>
                        <surname>Mirtti</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Verrill Clare">
                        <given-names>Clare</given-names>
                        <surname>Verrill</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zlobec Inti">
                        <given-names>Inti</given-names>
                        <surname>Zlobec</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="True Lawrence D">
                        <given-names>Lawrence D</given-names>
                        <surname>True</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-19">Engineering the future of 3D
                    pathology</article-title>. <source hwp:id="source-18">The Journal of Pathology: Clinical
                    Research</source>, <volume>10</volume>(<issue>1</issue>):<fpage>e347</fpage>, <year>2024</year>.
                    ISSN <issn>2056-4538</issn>. doi: <pub-id pub-id-type="doi">10.1002/cjp2.347</pub-id>.
                </citation>
            </ref>
            <ref id="c19" hwp:id="ref-19" hwp:rev-id="xref-ref-19-1">
                <label>19.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.19"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-19">
                    <string-name name-style="western" hwp:sortable="Glaser Adam K.">
                        <given-names>Adam K.</given-names>
                        <surname>Glaser</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Reder Nicholas P.">
                        <given-names>Nicholas P.</given-names>
                        <surname>Reder</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen Ye">
                        <given-names>Ye</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="McCarty Erin F.">
                        <given-names>Erin F.</given-names>
                        <surname>McCarty</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yin Chengbo">
                        <given-names>Chengbo</given-names>
                        <surname>Yin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wei Linpeng">
                        <given-names>Linpeng</given-names>
                        <surname>Wei</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Yu">
                        <given-names>Yu</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="True Lawrence D.">
                        <given-names>Lawrence D.</given-names>
                        <surname>True</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Liu Jonathan T. C.">
                        <given-names>Jonathan T. C.</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-20">Light-sheet microscopy for slide-free non-destructive
                    pathology of large clinical specimens</article-title>. <source hwp:id="source-19">Nature Biomedical
                    Engineering</source>, <volume>1</volume> (<issue>7</issue>):<fpage>0084</fpage>, <year>2017</year>.
                    ISSN <issn>2157-846X</issn>. doi: <pub-id pub-id-type="doi">10.1038/s41551-017-0084</pub-id>.
                </citation>
            </ref>
            <ref id="c20" hwp:id="ref-20" hwp:rev-id="xref-ref-20-1">
                <label>20.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.20"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-20">
                    <string-name name-style="western" hwp:sortable="Bouma Brett E.">
                        <given-names>Brett E.</given-names>
                        <surname>Bouma</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="de Boer Johannes F.">
                        <given-names>Johannes F.</given-names>
                        <surname>de Boer</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Huang David">
                        <given-names>David</given-names>
                        <surname>Huang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jang Ik-Kyung">
                        <given-names>Ik-Kyung</given-names>
                        <surname>Jang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yonetsu Taishi">
                        <given-names>Taishi</given-names>
                        <surname>Yonetsu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Leggett Cadman L.">
                        <given-names>Cadman L.</given-names>
                        <surname>Leggett</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Leitgeb Rainer">
                        <given-names>Rainer</given-names>
                        <surname>Leitgeb</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sampson David D.">
                        <given-names>David D.</given-names>
                        <surname>Sampson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Suter Melissa">
                        <given-names>Melissa</given-names>
                        <surname>Suter</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Vakoc Ben J.">
                        <given-names>Ben J.</given-names>
                        <surname>Vakoc</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Villiger Martin">
                        <given-names>Martin</given-names>
                        <surname>Villiger</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Wojtkowski Maciej">
                        <given-names>Maciej</given-names>
                        <surname>Wojtkowski</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-21">Optical coherence tomography</article-title>. <source
                        hwp:id="source-20">Nature Reviews Methods Primers</source>, <volume>2</volume>(<issue>1</issue>):<fpage>
                    79</fpage>, <year>2022</year>. doi: <pub-id pub-id-type="doi">10.1038/s43586-022-00162-2</pub-id>.
                </citation>
            </ref>
            <ref id="c21" hwp:id="ref-21" hwp:rev-id="xref-ref-21-1">
                <label>21.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.21"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-21">
                    <string-name name-style="western" hwp:sortable="Lin Li">
                        <given-names>Li</given-names>
                        <surname>Lin</surname>
                    </string-name>
                    and
                    <string-name name-style="western" hwp:sortable="Wang Lihong V.">
                        <given-names>Lihong V.</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-22">The emerging role of photoacoustic imaging in clinical
                    oncology</article-title>. <source hwp:id="source-21">Nature Reviews Clinical
                    Oncology</source>, <volume>19</volume>(<issue>6</issue>):<fpage>365</fpage>–<lpage>
                    384</lpage>, <year>2022</year>. ISSN <issn>1759-4774</issn>. doi: <pub-id pub-id-type="doi">
                    10.1038/s41571-022-00615-3</pub-id>.
                </citation>
            </ref>
            <ref id="c22" hwp:id="ref-22" hwp:rev-id="xref-ref-22-1">
                <label>22.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.22"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-22">
                    <string-name name-style="western" hwp:sortable="Katsamenis Orestis L.">
                        <given-names>Orestis L.</given-names>
                        <surname>Katsamenis</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Olding Michael">
                        <given-names>Michael</given-names>
                        <surname>Olding</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Warner Jane A.">
                        <given-names>Jane A.</given-names>
                        <surname>Warner</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chatelet David S.">
                        <given-names>David S.</given-names>
                        <surname>Chatelet</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jones Mark G.">
                        <given-names>Mark G.</given-names>
                        <surname>Jones</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sgalla Giacomo">
                        <given-names>Giacomo</given-names>
                        <surname>Sgalla</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Smit Bennie">
                        <given-names>Bennie</given-names>
                        <surname>Smit</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Larkin Oliver J.">
                        <given-names>Oliver J.</given-names>
                        <surname>Larkin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Haig Ian">
                        <given-names>Ian</given-names>
                        <surname>Haig</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Richeldi Luca">
                        <given-names>Luca</given-names>
                        <surname>Richeldi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sinclair Ian">
                        <given-names>Ian</given-names>
                        <surname>Sinclair</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lackie Peter M.">
                        <given-names>Peter M.</given-names>
                        <surname>Lackie</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Schneider Philipp">
                        <given-names>Philipp</given-names>
                        <surname>Schneider</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-23">X-ray Micro-Computed Tomography for Nondestructive
                    Three-Dimensional (3D) X-ray Histology</article-title>. <source hwp:id="source-22">The American
                    Journal of Pathology</source>, <volume>189</volume>(<issue>8</issue>): <fpage>1608</fpage>–<lpage>
                    1620</lpage>, <year>2019</year>. ISSN <issn>0002-9440</issn>. doi: <pub-id pub-id-type="doi">
                    10.1016/j.ajpath.2019.05.004</pub-id>.
                </citation>
            </ref>
            <ref id="c23" hwp:id="ref-23" hwp:rev-id="xref-ref-23-1">
                <label>23.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.23"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-23">
                    <string-name name-style="western" hwp:sortable="Tang Rong">
                        <given-names>Rong</given-names>
                        <surname>Tang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Buckley Julliette M.">
                        <given-names>Julliette M.</given-names>
                        <surname>Buckley</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fernandez Leopoldo">
                        <given-names>Leopoldo</given-names>
                        <surname>Fernandez</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Coopey Suzanne">
                        <given-names>Suzanne</given-names>
                        <surname>Coopey</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Aftreth Owen">
                        <given-names>Owen</given-names>
                        <surname>Aftreth</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Michaelson James">
                        <given-names>James</given-names>
                        <surname>Michaelson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Saksena Mansi">
                        <given-names>Mansi</given-names>
                        <surname>Saksena</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lei Lan">
                        <given-names>Lan</given-names>
                        <surname>Lei</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Specht Michelle">
                        <given-names>Michelle</given-names>
                        <surname>Specht</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Gadd Michele">
                        <given-names>Michele</given-names>
                        <surname>Gadd</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yagi Yukako">
                        <given-names>Yukako</given-names>
                        <surname>Yagi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rafferty Elizabeth">
                        <given-names>Elizabeth</given-names>
                        <surname>Rafferty</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Brachtel Elena">
                        <given-names>Elena</given-names>
                        <surname>Brachtel</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Smith Barbara L.">
                        <given-names>Barbara L.</given-names>
                        <surname>Smith</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-24">Micro-computed tomography (Micro-CT): a novel approach
                    for intraoperative breast cancer specimen imaging</article-title>. <source hwp:id="source-23">Breast
                    Cancer Research and Treatment</source>, <volume>139</volume>(<issue>2</issue>):<fpage>
                    311</fpage>–<lpage>316</lpage>, <year>2013</year>. ISSN <issn>0167-6806</issn>. doi: <pub-id
                        pub-id-type="doi">10.1007/s10549-013-2554-6</pub-id>.
                </citation>
            </ref>
            <ref id="c24" hwp:id="ref-24" hwp:rev-id="xref-ref-24-1">
                <label>24.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.24"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-24">
                    <string-name name-style="western" hwp:sortable="Tajbakhsh Kiarash">
                        <given-names>Kiarash</given-names>
                        <surname>Tajbakhsh</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Neels Antonia">
                        <given-names>Antonia</given-names>
                        <surname>Neels</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fadeeva Elena">
                        <given-names>Elena</given-names>
                        <surname>Fadeeva</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Larsson Jakob C">
                        <given-names>Jakob C</given-names>
                        <surname>Larsson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Stanowska Olga">
                        <given-names>Olga</given-names>
                        <surname>Stanowska</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Perren Aurel">
                        <given-names>Aurel</given-names>
                        <surname>Perren</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zboray Robert">
                        <given-names>Robert</given-names>
                        <surname>Zboray</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Zboray Robert">
                        <given-names>Robert</given-names>
                        <surname>Zboray</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-25">A Comprehensive Study of Laboratory-Based Micro-CT for 3D
                    Virtual Histology of Human FFPE Tissue Blocks</article-title>. <source hwp:id="source-24">IEEE
                    Access</source>, <volume>12</volume>:<fpage>78304</fpage>–<lpage>78316</lpage>, <year>2024</year>.
                    ISSN <issn>2169-3536</issn>. doi: <pub-id pub-id-type="doi">10.1109/access.2024.3407733</pub-id>.
                </citation>
            </ref>
            <ref id="c25" hwp:id="ref-25" hwp:rev-id="xref-ref-25-1">
                <label>25.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.25"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-25">
                    <string-name name-style="western" hwp:sortable="Reiser Johanna">
                        <given-names>Johanna</given-names>
                        <surname>Reiser</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Albers Jonas">
                        <given-names>Jonas</given-names>
                        <surname>Albers</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Svetlove Angelika">
                        <given-names>Angelika</given-names>
                        <surname>Svetlove</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mertiny Mara">
                        <given-names>Mara</given-names>
                        <surname>Mertiny</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kommoss Felix K.F.">
                        <given-names>Felix K.F.</given-names>
                        <surname>Kommoss</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schwab Constantin">
                        <given-names>Constantin</given-names>
                        <surname>Schwab</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schneemann Anna">
                        <given-names>Anna</given-names>
                        <surname>Schneemann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tromba Giuliana">
                        <given-names>Giuliana</given-names>
                        <surname>Tromba</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wacker Irene">
                        <given-names>Irene</given-names>
                        <surname>Wacker</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Curticean Ronald E.">
                        <given-names>Ronald E.</given-names>
                        <surname>Curticean</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schroeder Rasmus R.">
                        <given-names>Rasmus R.</given-names>
                        <surname>Schroeder</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kauczor Hans-Ulrich">
                        <given-names>Hans-Ulrich</given-names>
                        <surname>Kauczor</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wielpütz Mark O.">
                        <given-names>Mark O.</given-names>
                        <surname>Wielpütz</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dullin Christian">
                        <given-names>Christian</given-names>
                        <surname>Dullin</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Wagner Willi L.">
                        <given-names>Willi L.</given-names>
                        <surname>Wagner</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-26">Integrative Imaging of Lung Micro Structure: Amplifying
                    Classical Histology by Paraffin Block µCT and same-slide Scanning Electron
                    Microscopy</article-title>. <source hwp:id="source-25">bioRxiv</source>, page
                    2024.06.29.601332, <year>2024</year>. doi: <pub-id pub-id-type="doi">
                    10.1101/2024.06.29.601332</pub-id>.
                </citation>
            </ref>
            <ref id="c26" hwp:id="ref-26" hwp:rev-id="xref-ref-26-1">
                <label>26.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.26"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-26">
                    <string-name name-style="western" hwp:sortable="He Bryan">
                        <given-names>Bryan</given-names>
                        <surname>He</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bergenstråhle Ludvig">
                        <given-names>Ludvig</given-names>
                        <surname>Bergenstråhle</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Stenbeck Linnea">
                        <given-names>Linnea</given-names>
                        <surname>Stenbeck</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Abid Abubakar">
                        <given-names>Abubakar</given-names>
                        <surname>Abid</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Andersson Alma">
                        <given-names>Alma</given-names>
                        <surname>Andersson</surname>
                    </string-name>
                    , Å
                    <string-name name-style="western" hwp:sortable="Borg ke">
                        <given-names>ke</given-names>
                        <surname>Borg</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Maaskola Jonas">
                        <given-names>Jonas</given-names>
                        <surname>Maaskola</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lundeberg Joakim">
                        <given-names>Joakim</given-names>
                        <surname>Lundeberg</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Zou James">
                        <given-names>James</given-names>
                        <surname>Zou</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-27">Integrating spatial gene expression and breast tumour
                    morphology via deep learning</article-title>. <source hwp:id="source-26">Nature Biomedical
                    Engineering</source>, <volume>4</volume>(<issue>8</issue>):<fpage>827</fpage>–<lpage>
                    834</lpage>, <year>2020</year>. doi: <pub-id pub-id-type="doi">10.1038/s41551-020-0578-x</pub-id>.
                </citation>
            </ref>
            <ref id="c27" hwp:id="ref-27" hwp:rev-id="xref-ref-27-1">
                <label>27.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.27"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-27">
                    <string-name name-style="western" hwp:sortable="Bergenstråhle Ludvig">
                        <given-names>Ludvig</given-names>
                        <surname>Bergenstråhle</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="He Bryan">
                        <given-names>Bryan</given-names>
                        <surname>He</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bergenstråhle Joseph">
                        <given-names>Joseph</given-names>
                        <surname>Bergenstråhle</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Abalo Xesús">
                        <given-names>Xesús</given-names>
                        <surname>Abalo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mirzazadeh Reza">
                        <given-names>Reza</given-names>
                        <surname>Mirzazadeh</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Thrane Kim">
                        <given-names>Kim</given-names>
                        <surname>Thrane</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ji Andrew L.">
                        <given-names>Andrew L.</given-names>
                        <surname>Ji</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Andersson Alma">
                        <given-names>Alma</given-names>
                        <surname>Andersson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Larsson Ludvig">
                        <given-names>Ludvig</given-names>
                        <surname>Larsson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Stakenborg Nathalie">
                        <given-names>Nathalie</given-names>
                        <surname>Stakenborg</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Boeckxstaens Guy">
                        <given-names>Guy</given-names>
                        <surname>Boeckxstaens</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Khavari Paul">
                        <given-names>Paul</given-names>
                        <surname>Khavari</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zou James">
                        <given-names>James</given-names>
                        <surname>Zou</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lundeberg Joakim">
                        <given-names>Joakim</given-names>
                        <surname>Lundeberg</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Maaskola Jonas">
                        <given-names>Jonas</given-names>
                        <surname>Maaskola</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-28">Super-resolved spatial transcriptomics by deep data
                    fusion</article-title>. <source hwp:id="source-27">Nature Biotechnology</source>, <volume>
                    40</volume>(<issue>4</issue>):<fpage>476</fpage>–<lpage>479</lpage>, <year>2022</year>. ISSN <issn>
                    1087-0156</issn>. doi: <pub-id pub-id-type="doi">10.1038/s41587-021-01075-3</pub-id>.
                </citation>
            </ref>
            <ref id="c28" hwp:id="ref-28" hwp:rev-id="xref-ref-28-1">
                <label>28.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.28"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-28">
                    <string-name name-style="western" hwp:sortable="Zhang Daiwei">
                        <given-names>Daiwei</given-names>
                        <surname>Zhang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schroeder Amelia">
                        <given-names>Amelia</given-names>
                        <surname>Schroeder</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yan Hanying">
                        <given-names>Hanying</given-names>
                        <surname>Yan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yang Haochen">
                        <given-names>Haochen</given-names>
                        <surname>Yang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hu Jian">
                        <given-names>Jian</given-names>
                        <surname>Hu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lee Michelle Y. Y.">
                        <given-names>Michelle Y. Y.</given-names>
                        <surname>Lee</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cho Kyung S.">
                        <given-names>Kyung S.</given-names>
                        <surname>Cho</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Susztak Katalin">
                        <given-names>Katalin</given-names>
                        <surname>Susztak</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xu George X.">
                        <given-names>George X.</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Feldman Michael D.">
                        <given-names>Michael D.</given-names>
                        <surname>Feldman</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lee Edward B.">
                        <given-names>Edward B.</given-names>
                        <surname>Lee</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Furth Emma E.">
                        <given-names>Emma E.</given-names>
                        <surname>Furth</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Linghua">
                        <given-names>Linghua</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Li Mingyao">
                        <given-names>Mingyao</given-names>
                        <surname>Li</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-29">Inferring super-resolution tissue architecture by
                    integrating spatial transcriptomics with histology</article-title>. <source hwp:id="source-28">
                    Nature Biotechnology</source>, <volume>42</volume>(<issue>9</issue>): <fpage>1372</fpage>–<lpage>
                    1377</lpage>, <year>2024</year>. ISSN <issn>1087-0156</issn>. doi: <pub-id pub-id-type="doi">
                    10.1038/s41587-023-02019-9</pub-id>.
                </citation>
            </ref>
            <ref id="c29" hwp:id="ref-29" hwp:rev-id="xref-ref-29-1">
                <label>29.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.29"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-29">
                    <string-name name-style="western" hwp:sortable="Francis Karl">
                        <given-names>Karl</given-names>
                        <surname>Francis</surname>
                    </string-name>
                    and
                    <string-name name-style="western" hwp:sortable="Palsson Bernhard O.">
                        <given-names>Bernhard O.</given-names>
                        <surname>Palsson</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-30">Effective intercellular communication distances are
                    determined by the relative time constants for cyto/chemokine secretion and diffusion</article-title>. <source
                        hwp:id="source-29">Proceedings of the National Academy of Sciences</source>, <volume>94</volume>(<issue>
                    23</issue>):<fpage>12258</fpage>–<lpage>12262</lpage>, <year>1997</year>. ISSN <issn>
                    0027-8424</issn>. doi: <pub-id pub-id-type="doi">10.1073/pnas.94.23.12258</pub-id>.
                </citation>
            </ref>
            <ref id="c30" hwp:id="ref-30" hwp:rev-id="xref-ref-30-1">
                <label>30.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.30"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-30">
                    <string-name name-style="western" hwp:sortable="Jing Hui">
                        <given-names>Hui</given-names>
                        <surname>Jing</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="He Xiaomin">
                        <given-names>Xiaomin</given-names>
                        <surname>He</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Zheng Jinghao">
                        <given-names>Jinghao</given-names>
                        <surname>Zheng</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-31">Exosomes and regenerative medicine: state of the art and
                    perspectives</article-title>. <source hwp:id="source-30">Translational Research</source>, <volume>
                    196</volume>:<fpage>1</fpage>–<lpage>16</lpage>, <year>2018</year>. ISSN <issn>1931-5244</issn>.
                    doi: <pub-id pub-id-type="doi">10.1016/j.trsl.2018.01.005</pub-id>.
                </citation>
            </ref>
            <ref id="c31" hwp:id="ref-31" hwp:rev-id="xref-ref-31-1">
                <label>31.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.31"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-31">
                    <string-name name-style="western" hwp:sortable="Túrós Demeter">
                        <given-names>Demeter</given-names>
                        <surname>Túrós</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Vasiljevic Jelica">
                        <given-names>Jelica</given-names>
                        <surname>Vasiljevic</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hahn Kerstin">
                        <given-names>Kerstin</given-names>
                        <surname>Hahn</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rottenberg Sven">
                        <given-names>Sven</given-names>
                        <surname>Rottenberg</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Valdeolivas Alberto">
                        <given-names>Alberto</given-names>
                        <surname>Valdeolivas</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-32">Chrysalis: decoding tissue compartments in spatial
                    transcriptomics with archetypal analysis</article-title>. <source hwp:id="source-31">Communications
                    Biology</source>, <volume>7</volume>(<issue>1</issue>):<fpage>1520</fpage>, <year>2024</year>. doi: <pub-id
                        pub-id-type="doi">10.1038/s42003-024-07165-7</pub-id>.
                </citation>
            </ref>
            <ref id="c32" hwp:id="ref-32" hwp:rev-id="xref-ref-32-1 xref-ref-32-2 xref-ref-32-3">
                <label>32.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.32"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-32">
                    <string-name name-style="western" hwp:sortable="Schön Jacob">
                        <given-names>Jacob</given-names>
                        <surname>Schön</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Barut G. Tuba">
                        <given-names>G. Tuba</given-names>
                        <surname>Barut</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Salome Trüeb Bettina">
                        <given-names>Bettina</given-names>
                        <surname>Salome Trüeb</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Halwe Nico Joel">
                        <given-names>Nico Joel</given-names>
                        <surname>Halwe</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Veiga Inês Berenguer">
                        <given-names>Inês Berenguer</given-names>
                        <surname>Veiga</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kratzel Annika">
                        <given-names>Annika</given-names>
                        <surname>Kratzel</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ulrich Lorenz">
                        <given-names>Lorenz</given-names>
                        <surname>Ulrich</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kelly Jenna N.">
                        <given-names>Jenna N.</given-names>
                        <surname>Kelly</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Brügger Melanie">
                        <given-names>Melanie</given-names>
                        <surname>Brügger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wylezich Claudia">
                        <given-names>Claudia</given-names>
                        <surname>Wylezich</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Taddeo Adriano">
                        <given-names>Adriano</given-names>
                        <surname>Taddeo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Moreira Etori Aguiar">
                        <given-names>Etori Aguiar</given-names>
                        <surname>Moreira</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Túrós Demeter">
                        <given-names>Demeter</given-names>
                        <surname>Túrós</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Grau-Roma Llorenç">
                        <given-names>Llorenç</given-names>
                        <surname>Grau-Roma</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ahrens Ann Kathrin">
                        <given-names>Ann Kathrin</given-names>
                        <surname>Ahrens</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schlottau Kore">
                        <given-names>Kore</given-names>
                        <surname>Schlottau</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Britzke Tobias">
                        <given-names>Tobias</given-names>
                        <surname>Britzke</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Breithaupt Angele">
                        <given-names>Angele</given-names>
                        <surname>Breithaupt</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Corleis Björn">
                        <given-names>Björn</given-names>
                        <surname>Corleis</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kochmann Jana">
                        <given-names>Jana</given-names>
                        <surname>Kochmann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Esteves Blandina I. Oliveira">
                        <given-names>Blandina I. Oliveira</given-names>
                        <surname>Esteves</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Almeida Lea">
                        <given-names>Lea</given-names>
                        <surname>Almeida</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Thomann Lisa">
                        <given-names>Lisa</given-names>
                        <surname>Thomann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Devisme Christelle">
                        <given-names>Christelle</given-names>
                        <surname>Devisme</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Stalder Hanspeter">
                        <given-names>Hanspeter</given-names>
                        <surname>Stalder</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Steiner Silvio">
                        <given-names>Silvio</given-names>
                        <surname>Steiner</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ochsenbein Sarah">
                        <given-names>Sarah</given-names>
                        <surname>Ochsenbein</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schmied Kimberly">
                        <given-names>Kimberly</given-names>
                        <surname>Schmied</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Labroussaa Fabien">
                        <given-names>Fabien</given-names>
                        <surname>Labroussaa</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jores Jörg">
                        <given-names>Jörg</given-names>
                        <surname>Jores</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="V’kovski Philip">
                        <given-names>Philip</given-names>
                        <surname>V’kovski</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cmiljanovic Vladimir">
                        <given-names>Vladimir</given-names>
                        <surname>Cmiljanovic</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Alves Marco P.">
                        <given-names>Marco P.</given-names>
                        <surname>Alves</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Benarafa Charaf">
                        <given-names>Charaf</given-names>
                        <surname>Benarafa</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ebert Nadine">
                        <given-names>Nadine</given-names>
                        <surname>Ebert</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hoffmann Donata">
                        <given-names>Donata</given-names>
                        <surname>Hoffmann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Beer Martin">
                        <given-names>Martin</given-names>
                        <surname>Beer</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Thiel Volker">
                        <given-names>Volker</given-names>
                        <surname>Thiel</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-33">A safe, effective and adaptable live-attenuated
                    SARS-CoV-2 vaccine to reduce disease and transmission using one-to-stop genome
                    modifications</article-title>. <source hwp:id="source-32">Nature Microbiology</source>, <volume>
                    9</volume>(<issue>8</issue>):<fpage>2099</fpage>–<lpage>2112</lpage>, <year>2024</year>.
                    doi: <pub-id pub-id-type="doi">10.1038/s41564-024-01755-1</pub-id>.
                </citation>
            </ref>
            <ref id="c33" hwp:id="ref-33" hwp:rev-id="xref-ref-33-1">
                <label>33.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.33"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-33">
                    <string-name name-style="western" hwp:sortable="Liu Shiwei">
                        <given-names>Shiwei</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yue Wenwen">
                        <given-names>Wenwen</given-names>
                        <surname>Yue</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Guo Zhiqing">
                        <given-names>Zhiqing</given-names>
                        <surname>Guo</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Wang Liejun">
                        <given-names>Liejun</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-34">Multi-branch CNN and grouping cascade attention for
                    medical image classification</article-title>. <source hwp:id="source-33">Scientific Reports</source>, <volume>
                    14</volume>(<issue>1</issue>):<fpage>15013</fpage>, <year>2024</year>. doi: <pub-id
                        pub-id-type="doi">10.1038/s41598-024-64982-w</pub-id>.
                </citation>
            </ref>
            <ref id="c34" hwp:id="ref-34" hwp:rev-id="xref-ref-34-1">
                <label>34.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.34"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-34">
                    <string-name name-style="western" hwp:sortable="Liu Ze">
                        <given-names>Ze</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lin Yutong">
                        <given-names>Yutong</given-names>
                        <surname>Lin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cao Yue">
                        <given-names>Yue</given-names>
                        <surname>Cao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hu Han">
                        <given-names>Han</given-names>
                        <surname>Hu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wei Yixuan">
                        <given-names>Yixuan</given-names>
                        <surname>Wei</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhang Zheng">
                        <given-names>Zheng</given-names>
                        <surname>Zhang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lin Stephen">
                        <given-names>Stephen</given-names>
                        <surname>Lin</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Guo Baining">
                        <given-names>Baining</given-names>
                        <surname>Guo</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-35">Swin Transformer: Hierarchical Vision Transformer using
                    Shifted Windows</article-title>. <source hwp:id="source-34">arXiv</source>, <year>2021</year>. doi: <pub-id
                        pub-id-type="doi">10.48550/arxiv.2103.14030</pub-id>.
                </citation>
            </ref>
            <ref id="c35" hwp:id="ref-35" hwp:rev-id="xref-ref-35-1">
                <label>35.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.35"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-35">
                    <string-name name-style="western" hwp:sortable="Guo Yuan">
                        <given-names>Yuan</given-names>
                        <surname>Guo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Wenpeng">
                        <given-names>Wenpeng</given-names>
                        <surname>Li</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Zhai Ping">
                        <given-names>Ping</given-names>
                        <surname>Zhai</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-36">Swin-transformer for weak feature
                    matching</article-title>. <source hwp:id="source-35">Scientific Reports</source>, <volume>
                    15</volume>(<issue>1</issue>):<fpage>2961</fpage>, <year>2025</year>. doi: <pub-id
                        pub-id-type="doi">10.1038/s41598-025-87309-9</pub-id>.
                </citation>
            </ref>
            <ref id="c36" hwp:id="ref-36" hwp:rev-id="xref-ref-36-1">
                <label>36.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.36"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-36">
                    <string-name name-style="western" hwp:sortable="Wang Si">
                        <given-names>Si</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yao Xiaohong">
                        <given-names>Xiaohong</given-names>
                        <surname>Yao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ma Shuai">
                        <given-names>Shuai</given-names>
                        <surname>Ma</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ping Yifang">
                        <given-names>Yifang</given-names>
                        <surname>Ping</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fan Yanling">
                        <given-names>Yanling</given-names>
                        <surname>Fan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sun Shuhui">
                        <given-names>Shuhui</given-names>
                        <surname>Sun</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="He Zhicheng">
                        <given-names>Zhicheng</given-names>
                        <surname>He</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Shi Yu">
                        <given-names>Yu</given-names>
                        <surname>Shi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sun Liang">
                        <given-names>Liang</given-names>
                        <surname>Sun</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xiao Shiqi">
                        <given-names>Shiqi</given-names>
                        <surname>Xiao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Song Moshi">
                        <given-names>Moshi</given-names>
                        <surname>Song</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cai Jun">
                        <given-names>Jun</given-names>
                        <surname>Cai</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Jiaming">
                        <given-names>Jiaming</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tang Rui">
                        <given-names>Rui</given-names>
                        <surname>Tang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhao Liyun">
                        <given-names>Liyun</given-names>
                        <surname>Zhao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Chaofu">
                        <given-names>Chaofu</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Qiaoran">
                        <given-names>Qiaoran</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhao Lei">
                        <given-names>Lei</given-names>
                        <surname>Zhao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hu Huifang">
                        <given-names>Huifang</given-names>
                        <surname>Hu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Liu Xindong">
                        <given-names>Xindong</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sun Guoqiang">
                        <given-names>Guoqiang</given-names>
                        <surname>Sun</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen Lu">
                        <given-names>Lu</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pan Guoqing">
                        <given-names>Guoqing</given-names>
                        <surname>Pan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen Huaiyong">
                        <given-names>Huaiyong</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Qingrui">
                        <given-names>Qingrui</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhang Peipei">
                        <given-names>Peipei</given-names>
                        <surname>Zhang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xu Yuanyuan">
                        <given-names>Yuanyuan</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Feng Huyi">
                        <given-names>Huyi</given-names>
                        <surname>Feng</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhao Guo-Guang">
                        <given-names>Guo-Guang</given-names>
                        <surname>Zhao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wen Tianzi">
                        <given-names>Tianzi</given-names>
                        <surname>Wen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yang Yungui">
                        <given-names>Yungui</given-names>
                        <surname>Yang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Huang Xuequan">
                        <given-names>Xuequan</given-names>
                        <surname>Huang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Wei">
                        <given-names>Wei</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Liu Zhenhua">
                        <given-names>Zhenhua</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wang Hongmei">
                        <given-names>Hongmei</given-names>
                        <surname>Wang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wu Haibo">
                        <given-names>Haibo</given-names>
                        <surname>Wu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hu Baoyang">
                        <given-names>Baoyang</given-names>
                        <surname>Hu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ren Yong">
                        <given-names>Yong</given-names>
                        <surname>Ren</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhou Qi">
                        <given-names>Qi</given-names>
                        <surname>Zhou</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Qu Jing">
                        <given-names>Jing</given-names>
                        <surname>Qu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zhang Weiqi">
                        <given-names>Weiqi</given-names>
                        <surname>Zhang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Liu Guang-Hui">
                        <given-names>Guang-Hui</given-names>
                        <surname>Liu</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Bian Xiu-Wu">
                        <given-names>Xiu-Wu</given-names>
                        <surname>Bian</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-37">A single-cell transcriptomic landscape of the lungs of
                    patients with COVID-19</article-title>. <source hwp:id="source-36">Nature Cell
                    Biology</source>, <volume>23</volume>(<issue>12</issue>):<fpage>1314</fpage>–<lpage>
                    1328</lpage>, <year>2021</year>. ISSN <issn>1465-7392</issn>. doi: <pub-id pub-id-type="doi">
                    10.1038/s41556-021-00796-6</pub-id>.
                </citation>
            </ref>
            <ref id="c37" hwp:id="ref-37" hwp:rev-id="xref-ref-37-1">
                <label>37.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.37"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-37">
                    <string-name name-style="western" hwp:sortable="Norvik Christian">
                        <given-names>Christian</given-names>
                        <surname>Norvik</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Karl Christian">
                        <given-names>Christian</given-names>
                        <surname>Karl</surname>
                    </string-name>
                    <string-name name-style="western" hwp:sortable="ö Westö">
                        <given-names>Westö</given-names>
                        <surname>ö</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Peruzzi Niccolò">
                        <given-names>Niccolò</given-names>
                        <surname>Peruzzi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lovric Goran">
                        <given-names>Goran</given-names>
                        <surname>Lovric</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="van der Have Oscar">
                        <given-names>Oscar</given-names>
                        <surname>van der Have</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mokso Rajmund">
                        <given-names>Rajmund</given-names>
                        <surname>Mokso</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jeremiasen Ida">
                        <given-names>Ida</given-names>
                        <surname>Jeremiasen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Brunnström Hans">
                        <given-names>Hans</given-names>
                        <surname>Brunnström</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Galambos Csaba">
                        <given-names>Csaba</given-names>
                        <surname>Galambos</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bech Martin">
                        <given-names>Martin</given-names>
                        <surname>Bech</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Tran-Lundmark Karin">
                        <given-names>Karin</given-names>
                        <surname>Tran-Lundmark</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-38">Synchrotron-based phase-contrast micro-CT as a tool for
                    understanding pulmonary vascular pathobiology and the 3-D microanatomy of alveolar capillary
                    dysplasia</article-title>. <source hwp:id="source-37">American Journal of Physiology-Lung Cellular
                    and Molecular Physiology</source>, <volume>318</volume>(<issue>1</issue>): <fpage>
                    L65</fpage>–<lpage>L75</lpage>, <year>2020</year>. ISSN <issn>1040-0605</issn>. doi: <pub-id
                        pub-id-type="doi">10.1152/ajplung.00103.2019</pub-id>.
                </citation>
            </ref>
            <ref id="c38" hwp:id="ref-38" hwp:rev-id="xref-ref-38-1">
                <label>38.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.38"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-38">
                    <string-name name-style="western" hwp:sortable="Paganin D.">
                        <given-names>D.</given-names>
                        <surname>Paganin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mayo S. C.">
                        <given-names>S. C.</given-names>
                        <surname>Mayo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Gureyev T. E.">
                        <given-names>T. E.</given-names>
                        <surname>Gureyev</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Miller P. R.">
                        <given-names>P. R.</given-names>
                        <surname>Miller</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Wilkins S. W.">
                        <given-names>S. W.</given-names>
                        <surname>Wilkins</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-39">Simultaneous phase and amplitude extraction from a single
                    defocused image of a homogeneous object</article-title>. <source hwp:id="source-38">Journal of
                    Microscopy</source>, <volume>206</volume>(<issue>1</issue>):<fpage>33</fpage>–<lpage>
                    40</lpage>, <year>2002</year>. ISSN <issn>0022-2720</issn>. doi: <pub-id pub-id-type="doi">
                    10.1046/j.1365-2818.2002.01010.x</pub-id>.
                </citation>
            </ref>
            <ref id="c39" hwp:id="ref-39" hwp:rev-id="xref-ref-39-1">
                <label>39.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.39"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-39">
                    <string-name name-style="western" hwp:sortable="Vo Nghia T">
                        <given-names>Nghia T</given-names>
                        <surname>Vo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Atwood Robert C">
                        <given-names>Robert C</given-names>
                        <surname>Atwood</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Drakopoulos Michael">
                        <given-names>Michael</given-names>
                        <surname>Drakopoulos</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-40">Superior techniques for eliminating ring artifacts in
                    X-ray micro-tomography</article-title>. <source hwp:id="source-39">Optics Express</source>, <volume>
                    26</volume>(<issue>22</issue>):<fpage>28396</fpage>, <year>2018</year>. doi: <pub-id
                        pub-id-type="doi">10.1364/oe.26.028396</pub-id>.
                </citation>
            </ref>
            <ref id="c40" hwp:id="ref-40" hwp:rev-id="xref-ref-40-1">
                <label>40.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.40"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-40">
                    <string-name name-style="western" hwp:sortable="Wolf F. Alexander">
                        <given-names>F. Alexander</given-names>
                        <surname>Wolf</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Angerer Philipp">
                        <given-names>Philipp</given-names>
                        <surname>Angerer</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Theis Fabian J.">
                        <given-names>Fabian J.</given-names>
                        <surname>Theis</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-41">SCANPY: large-scale single-cell gene expression data
                    analysis</article-title>. <source hwp:id="source-40">Genome Biology</source>, <volume>
                    19</volume>(<issue>1</issue>):<fpage>15</fpage>, <year>2018</year>. ISSN <issn>1474-7596</issn>.
                    doi: <pub-id pub-id-type="doi">10.1186/s13059-017-1382-0</pub-id>.
                </citation>
            </ref>
            <ref id="c41" hwp:id="ref-41" hwp:rev-id="xref-ref-41-1">
                <label>41.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.41"
                          ref:linkable="no" ref:use-reference-as-is="yes" hwp:id="citation-41">
                    <string-name name-style="western" hwp:sortable="Gillies Sean">
                        <given-names>Sean</given-names>
                        <surname>Gillies</surname>
                    </string-name>
                    <etal>et al.</etal>
                    <source hwp:id="source-41">Shapely: manipulation and analysis of geometric objects</source>, <year>
                    2007</year>–.
                </citation>
            </ref>
            <ref id="c42" hwp:id="ref-42" hwp:rev-id="xref-ref-42-1">
                <label>42.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.42"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-42">
                    <string-name name-style="western" hwp:sortable="Liberzon Arthur">
                        <given-names>Arthur</given-names>
                        <surname>Liberzon</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Birger Chet">
                        <given-names>Chet</given-names>
                        <surname>Birger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Thorvaldsdóttir Helga">
                        <given-names>Helga</given-names>
                        <surname>Thorvaldsdóttir</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ghandi Mahmoud">
                        <given-names>Mahmoud</given-names>
                        <surname>Ghandi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mesirov Jill P.">
                        <given-names>Jill P.</given-names>
                        <surname>Mesirov</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Tamayo Pablo">
                        <given-names>Pablo</given-names>
                        <surname>Tamayo</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-42">The Molecular Signatures Database Hallmark Gene Set
                    Collection</article-title>. <source hwp:id="source-42">Cell Systems</source>, <volume>
                    1</volume>(<issue>6</issue>):<fpage>417</fpage>–<lpage>425</lpage>, <year>2015</year>. ISSN <issn>
                    2405-4712</issn>. doi: <pub-id pub-id-type="doi">10.1016/j.cels.2015.12.004</pub-id>.
                </citation>
            </ref>
            <ref id="c43" hwp:id="ref-43" hwp:rev-id="xref-ref-43-1">
                <label>43.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.43"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-43">
                    <string-name name-style="western" hwp:sortable="Schubert Michael">
                        <given-names>Michael</given-names>
                        <surname>Schubert</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Klinger Bertram">
                        <given-names>Bertram</given-names>
                        <surname>Klinger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Klünemann Martina">
                        <given-names>Martina</given-names>
                        <surname>Klünemann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sieber Anja">
                        <given-names>Anja</given-names>
                        <surname>Sieber</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Uhlitz Florian">
                        <given-names>Florian</given-names>
                        <surname>Uhlitz</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sauer Sascha">
                        <given-names>Sascha</given-names>
                        <surname>Sauer</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Garnett Mathew J.">
                        <given-names>Mathew J.</given-names>
                        <surname>Garnett</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Blüthgen Nils">
                        <given-names>Nils</given-names>
                        <surname>Blüthgen</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Saez-Rodriguez Julio">
                        <given-names>Julio</given-names>
                        <surname>Saez-Rodriguez</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-43">Perturbation-response genes reveal signaling footprints
                    in cancer gene expression</article-title>. <source hwp:id="source-43">Nature Communications</source>, <volume>
                    9</volume>(<issue>1</issue>): <fpage>20</fpage>, <year>2018</year>. doi: <pub-id pub-id-type="doi">
                    10.1038/s41467-017-02391-6</pub-id>.
                </citation>
            </ref>
            <ref id="c44" hwp:id="ref-44" hwp:rev-id="xref-ref-44-1">
                <label>44.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.44"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-44">
                    <string-name name-style="western" hwp:sortable="Mompel Pau Badia-i">
                        <given-names>Pau Badia-i</given-names>
                        <surname>Mompel</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Santiago Jesús Vélez">
                        <given-names>Jesús Vélez</given-names>
                        <surname>Santiago</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Braunger Jana">
                        <given-names>Jana</given-names>
                        <surname>Braunger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Geiss Celina">
                        <given-names>Celina</given-names>
                        <surname>Geiss</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dimitrov Daniel">
                        <given-names>Daniel</given-names>
                        <surname>Dimitrov</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Müller-Dott Sophia">
                        <given-names>Sophia</given-names>
                        <surname>Müller-Dott</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Taus Petr">
                        <given-names>Petr</given-names>
                        <surname>Taus</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dugourd Aurelien">
                        <given-names>Aurelien</given-names>
                        <surname>Dugourd</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Holland Christian H">
                        <given-names>Christian H</given-names>
                        <surname>Holland</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Flores Ricardo O Ramirez">
                        <given-names>Ricardo O Ramirez</given-names>
                        <surname>Flores</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Saez-Rodriguez Julio">
                        <given-names>Julio</given-names>
                        <surname>Saez-Rodriguez</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-44">decoupleR: ensemble of computational methods to infer
                    biological activities from omics data</article-title>. <source hwp:id="source-44">Bioinformatics
                    Advances</source>, <volume>2</volume>(<issue>1</issue>):<fpage>vbac016</fpage>, <year>2022</year>.
                    doi: <pub-id pub-id-type="doi">10.1093/bioadv/vbac016</pub-id>.
                </citation>
            </ref>
            <ref id="c45" hwp:id="ref-45" hwp:rev-id="xref-ref-45-1">
                <label>45.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.45"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-45">
                    <string-name name-style="western" hwp:sortable="Kleshchevnikov Vitalii">
                        <given-names>Vitalii</given-names>
                        <surname>Kleshchevnikov</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Shmatko Artem">
                        <given-names>Artem</given-names>
                        <surname>Shmatko</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dann Emma">
                        <given-names>Emma</given-names>
                        <surname>Dann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Aivazidis Alexander">
                        <given-names>Alexander</given-names>
                        <surname>Aivazidis</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="King Hamish W.">
                        <given-names>Hamish W.</given-names>
                        <surname>King</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li Tong">
                        <given-names>Tong</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Elmentaite Rasa">
                        <given-names>Rasa</given-names>
                        <surname>Elmentaite</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lomakin Artem">
                        <given-names>Artem</given-names>
                        <surname>Lomakin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kedlian Veronika">
                        <given-names>Veronika</given-names>
                        <surname>Kedlian</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Gayoso Adam">
                        <given-names>Adam</given-names>
                        <surname>Gayoso</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jain Mika Sarkin">
                        <given-names>Mika Sarkin</given-names>
                        <surname>Jain</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Park Jun Sung">
                        <given-names>Jun Sung</given-names>
                        <surname>Park</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ramona Lauma">
                        <given-names>Lauma</given-names>
                        <surname>Ramona</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tuck Elizabeth">
                        <given-names>Elizabeth</given-names>
                        <surname>Tuck</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Arutyunyan Anna">
                        <given-names>Anna</given-names>
                        <surname>Arutyunyan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Vento-Tormo Roser">
                        <given-names>Roser</given-names>
                        <surname>Vento-Tormo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Gerstung Moritz">
                        <given-names>Moritz</given-names>
                        <surname>Gerstung</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="James Louisa">
                        <given-names>Louisa</given-names>
                        <surname>James</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Stegle Oliver">
                        <given-names>Oliver</given-names>
                        <surname>Stegle</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Bayraktar Omer Ali">
                        <given-names>Omer Ali</given-names>
                        <surname>Bayraktar</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-45">Cell2location maps fine-grained cell types in spatial
                    transcriptomics</article-title>. <source hwp:id="source-45">Nature Biotechnology</source>,
                    pages <fpage>1</fpage>–<lpage>11</lpage>, <year>2022</year>. ISSN <issn>1087-0156</issn>.
                    doi: <pub-id pub-id-type="doi">10.1038/s41587-021-01139-4.cell2loc</pub-id>.
                </citation>
            </ref>
            <ref id="c46" hwp:id="ref-46" hwp:rev-id="xref-ref-46-1">
                <label>46.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.46"
                          ref:linkable="no" ref:use-reference-as-is="yes" hwp:id="citation-46">
                    <string-name name-style="western" hwp:sortable="Miles jakirkham Alistair">
                        <given-names>Alistair</given-names>
                        <surname>Miles jakirkham</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hamman Joe">
                        <given-names>Joe</given-names>
                        <surname>Hamman</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Orfanos Dimitri Papadopoulos">
                        <given-names>Dimitri Papadopoulos</given-names>
                        <surname>Orfanos</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Stansby David">
                        <given-names>David</given-names>
                        <surname>Stansby</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bussonnier M">
                        <given-names>M</given-names>
                        <surname>Bussonnier</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Moore Josh">
                        <given-names>Josh</given-names>
                        <surname>Moore</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bennett Davis">
                        <given-names>Davis</given-names>
                        <surname>Bennett</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Augspurger Tom">
                        <given-names>Tom</given-names>
                        <surname>Augspurger</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rzepka Norman">
                        <given-names>Norman</given-names>
                        <surname>Rzepka</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cherian Deepak">
                        <given-names>Deepak</given-names>
                        <surname>Cherian</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Verma Sanket">
                        <given-names>Sanket</given-names>
                        <surname>Verma</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bourbeau James">
                        <given-names>James</given-names>
                        <surname>Bourbeau</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fulton Andrew">
                        <given-names>Andrew</given-names>
                        <surname>Fulton</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Abernathey Ryan">
                        <given-names>Ryan</given-names>
                        <surname>Abernathey</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lee Gregory">
                        <given-names>Gregory</given-names>
                        <surname>Lee</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Spitz Hannes">
                        <given-names>Hannes</given-names>
                        <surname>Spitz</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kristensen Mads R. B.">
                        <given-names>Mads R. B.</given-names>
                        <surname>Kristensen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jones Max">
                        <given-names>Max</given-names>
                        <surname>Jones</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Patel Zain">
                        <given-names>Zain</given-names>
                        <surname>Patel</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chopra Saransh">
                        <given-names>Saransh</given-names>
                        <surname>Chopra</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rocklin Matthew">
                        <given-names>Matthew</given-names>
                        <surname>Rocklin</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Awa Awa Brandon">
                        <given-names>Awa Brandon</given-names>
                        <surname>Awa</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zimmerman Nathan">
                        <given-names>Nathan</given-names>
                        <surname>Zimmerman</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Durant Martin">
                        <given-names>Martin</given-names>
                        <surname>Durant</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Nunez-Iglesias Juan">
                        <given-names>Juan</given-names>
                        <surname>Nunez-Iglesias</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="de Andrade Elliott Sales">
                        <given-names>Elliott Sales</given-names>
                        <surname>de Andrade</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Schut Vincent">
                        <given-names>Vincent</given-names>
                        <surname>Schut</surname>
                    </string-name>
                    . <source hwp:id="source-46">zarr-developers/zarr-python: v3.0.5 (v3.0.5)</source>, <year>
                    2025</year>.
                </citation>
            </ref>
            <ref id="c47" hwp:id="ref-47" hwp:rev-id="xref-ref-47-1">
                <label>47.</label>
                <citation publication-type="other" citation-type="journal" ref:id="2025.03.21.644627v1.47"
                          ref:linkable="no" ref:use-reference-as-is="yes" hwp:id="citation-47">
                    <string-name name-style="western" hwp:sortable="Rocklin Matthew">
                        <given-names>Matthew</given-names>
                        <surname>Rocklin</surname>
                    </string-name>
                    . <source hwp:id="source-47">Dask: Parallel computation with blocked algorithms and task
                    scheduling</source>, <year>2015</year>.
                </citation>
            </ref>
            <ref id="c48" hwp:id="ref-48" hwp:rev-id="xref-ref-48-1">
                <label>48.</label>
                <citation publication-type="website" citation-type="web" ref:id="2025.03.21.644627v1.48"
                          ref:linkable="no" ref:use-reference-as-is="yes" hwp:id="citation-48">
                    <string-name name-style="western" hwp:sortable="Chollet François">
                        <given-names>François</given-names>
                        <surname>Chollet</surname>
                    </string-name>
                    <etal>et al.</etal>
                    <source hwp:id="source-48">Keras</source>. <ext-link l:rel="related" l:ref-type="uri"
                                                                         l:ref="https://keras.io" ext-link-type="uri"
                                                                         xlink:href="https://keras.io"
                                                                         hwp:id="ext-link-3">https://keras.io</ext-link>, <year>
                    2015</year>.
                </citation>
            </ref>
            <ref id="c49" hwp:id="ref-49" hwp:rev-id="xref-ref-49-1">
                <label>49.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.49"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-49">
                    <string-name name-style="western" hwp:sortable="Bogovic John A.">
                        <given-names>John A.</given-names>
                        <surname>Bogovic</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hanslovsky Philipp">
                        <given-names>Philipp</given-names>
                        <surname>Hanslovsky</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wong Allan">
                        <given-names>Allan</given-names>
                        <surname>Wong</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Saalfeld Stephan">
                        <given-names>Stephan</given-names>
                        <surname>Saalfeld</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-46">Robust Registration of Calcium Images by Learned Contrast
                    Synthesis</article-title>. <source hwp:id="source-49">2016 IEEE 13th International Symposium on
                    Biomedical Imaging (ISBI)</source>, pages <fpage>1123</fpage>–<lpage>1126</lpage>, <year>2016</year>.
                    doi: <pub-id pub-id-type="doi">10.1109/isbi.2016.7493463</pub-id>.
                </citation>
            </ref>
            <ref id="c50" hwp:id="ref-50" hwp:rev-id="xref-ref-50-1">
                <label>50.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.50"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-50">
                    <string-name name-style="western" hwp:sortable="Heumos Lukas">
                        <given-names>Lukas</given-names>
                        <surname>Heumos</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schaar Anna C">
                        <given-names>Anna C</given-names>
                        <surname>Schaar</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lance Christopher">
                        <given-names>Christopher</given-names>
                        <surname>Lance</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Litinetskaya Anastasia">
                        <given-names>Anastasia</given-names>
                        <surname>Litinetskaya</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Drost Felix">
                        <given-names>Felix</given-names>
                        <surname>Drost</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Zappia Luke">
                        <given-names>Luke</given-names>
                        <surname>Zappia</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lücken Malte D">
                        <given-names>Malte D</given-names>
                        <surname>Lücken</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Strobl Daniel C">
                        <given-names>Daniel C</given-names>
                        <surname>Strobl</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Henao Juan">
                        <given-names>Juan</given-names>
                        <surname>Henao</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Curion Fabiola">
                        <given-names>Fabiola</given-names>
                        <surname>Curion</surname>
                    </string-name>
                    , <collab hwp:id="collab-2">Single-cell Best Practices Consortium</collab>,
                    <string-name name-style="western" hwp:sortable="Aliee Hananeh">
                        <given-names>Hananeh</given-names>
                        <surname>Aliee</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ansari Meshal">
                        <given-names>Meshal</given-names>
                        <surname>Ansari</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mompel Pau Badia-i">
                        <given-names>Pau Badia-i</given-names>
                        <surname>Mompel</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Büttner Maren">
                        <given-names>Maren</given-names>
                        <surname>Büttner</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dann Emma">
                        <given-names>Emma</given-names>
                        <surname>Dann</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dimitrov Daniel">
                        <given-names>Daniel</given-names>
                        <surname>Dimitrov</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Dony Leander">
                        <given-names>Leander</given-names>
                        <surname>Dony</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Frishberg Amit">
                        <given-names>Amit</given-names>
                        <surname>Frishberg</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="He Dongze">
                        <given-names>Dongze</given-names>
                        <surname>He</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hediyeh-zadeh Soroor">
                        <given-names>Soroor</given-names>
                        <surname>Hediyeh-zadeh</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Hetzel Leon">
                        <given-names>Leon</given-names>
                        <surname>Hetzel</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ibarra Ignacio L">
                        <given-names>Ignacio L</given-names>
                        <surname>Ibarra</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jones Matthew G">
                        <given-names>Matthew G</given-names>
                        <surname>Jones</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lotfollahi Mohammad">
                        <given-names>Mohammad</given-names>
                        <surname>Lotfollahi</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Martens Laura D">
                        <given-names>Laura D</given-names>
                        <surname>Martens</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Müller Christian L">
                        <given-names>Christian L</given-names>
                        <surname>Müller</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Nitzan Mor">
                        <given-names>Mor</given-names>
                        <surname>Nitzan</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ostner Johannes">
                        <given-names>Johannes</given-names>
                        <surname>Ostner</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Palla Giovanni">
                        <given-names>Giovanni</given-names>
                        <surname>Palla</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Patro Rob">
                        <given-names>Rob</given-names>
                        <surname>Patro</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Piran Zoe">
                        <given-names>Zoe</given-names>
                        <surname>Piran</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Ramírez-Suástegui Ciro">
                        <given-names>Ciro</given-names>
                        <surname>Ramírez-Suástegui</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Saez-Rodriguez Julio">
                        <given-names>Julio</given-names>
                        <surname>Saez-Rodriguez</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sarkar Hirak">
                        <given-names>Hirak</given-names>
                        <surname>Sarkar</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schubert Benjamin">
                        <given-names>Benjamin</given-names>
                        <surname>Schubert</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sikkema Lisa">
                        <given-names>Lisa</given-names>
                        <surname>Sikkema</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Srivastava Avi">
                        <given-names>Avi</given-names>
                        <surname>Srivastava</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tanevski Jovan">
                        <given-names>Jovan</given-names>
                        <surname>Tanevski</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Virshup Isaac">
                        <given-names>Isaac</given-names>
                        <surname>Virshup</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Weiler Philipp">
                        <given-names>Philipp</given-names>
                        <surname>Weiler</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Schiller Herbert B">
                        <given-names>Herbert B</given-names>
                        <surname>Schiller</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Theis Fabian J">
                        <given-names>Fabian J</given-names>
                        <surname>Theis</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-47">Best practices for single-cell analysis across
                    modalities</article-title>. <source hwp:id="source-50">Nature Reviews Genetics</source>, <volume>
                    24</volume>(<issue>8</issue>):<fpage>550</fpage>–<lpage>572</lpage>, <year>2023</year>. ISSN <issn>
                    1471-0056</issn>. doi: <pub-id pub-id-type="doi">10.1038/s41576-023-00586-w</pub-id>.
                </citation>
            </ref>
            <ref id="c51" hwp:id="ref-51" hwp:rev-id="xref-ref-51-1">
                <label>51.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.51"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-51">
                    <string-name name-style="western" hwp:sortable="Wolock Samuel L.">
                        <given-names>Samuel L.</given-names>
                        <surname>Wolock</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Lopez Romain">
                        <given-names>Romain</given-names>
                        <surname>Lopez</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Klein Allon M.">
                        <given-names>Allon M.</given-names>
                        <surname>Klein</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-48">Scrublet: Computational Identification of Cell Doublets
                    in Single-Cell Transcriptomic Data</article-title>. <source hwp:id="source-51">Cell Systems</source>, <volume>
                    8</volume>(<issue>4</issue>):<fpage>281</fpage>–<lpage>291</lpage>.e9, <year>2019</year>.
                    ISSN <issn>2405-4712</issn>. doi: <pub-id pub-id-type="doi">10.1016/j.cels.2018.11.005</pub-id>.
                </citation>
            </ref>
            <ref id="c52" hwp:id="ref-52" hwp:rev-id="xref-ref-52-1">
                <label>52.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.52"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-52">
                    <string-name name-style="western" hwp:sortable="Lopez Romain">
                        <given-names>Romain</given-names>
                        <surname>Lopez</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Regier Jeffrey">
                        <given-names>Jeffrey</given-names>
                        <surname>Regier</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Cole Michael B.">
                        <given-names>Michael B.</given-names>
                        <surname>Cole</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jordan Michael I.">
                        <given-names>Michael I.</given-names>
                        <surname>Jordan</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Yosef Nir">
                        <given-names>Nir</given-names>
                        <surname>Yosef</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-49">Deep generative modeling for single-cell
                    transcriptomics</article-title>. <source hwp:id="source-52">Nature Methods</source>, <volume>
                    15</volume>(<issue>12</issue>):<fpage>1053</fpage>–<lpage>1058</lpage>, <year>2018</year>.
                    ISSN <issn>1548-7091</issn>. doi: <pub-id pub-id-type="doi">10.1038/s41592-018-0229-2</pub-id>.
                </citation>
            </ref>
            <ref id="c53" hwp:id="ref-53" hwp:rev-id="xref-ref-53-1">
                <label>53.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.53"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-53">
                    <string-name name-style="western" hwp:sortable="Heimberg Graham">
                        <given-names>Graham</given-names>
                        <surname>Heimberg</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kuo Tony">
                        <given-names>Tony</given-names>
                        <surname>Kuo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="DePianto Daryle J.">
                        <given-names>Daryle J.</given-names>
                        <surname>DePianto</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Salem Omar">
                        <given-names>Omar</given-names>
                        <surname>Salem</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Heigl Tobias">
                        <given-names>Tobias</given-names>
                        <surname>Heigl</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Diamant Nathaniel">
                        <given-names>Nathaniel</given-names>
                        <surname>Diamant</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Scalia Gabriele">
                        <given-names>Gabriele</given-names>
                        <surname>Scalia</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Biancalani Tommaso">
                        <given-names>Tommaso</given-names>
                        <surname>Biancalani</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Turley Shannon J.">
                        <given-names>Shannon J.</given-names>
                        <surname>Turley</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rock Jason R.">
                        <given-names>Jason R.</given-names>
                        <surname>Rock</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bravo Héctor Corrada">
                        <given-names>Héctor Corrada</given-names>
                        <surname>Bravo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Kaminker Josh">
                        <given-names>Josh</given-names>
                        <surname>Kaminker</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Vander Heiden Jason A.">
                        <given-names>Jason A.</given-names>
                        <surname>Vander Heiden</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Regev Aviv">
                        <given-names>Aviv</given-names>
                        <surname>Regev</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-50">A cell atlas foundation model for scalable search of
                    similar human cells</article-title>. <source hwp:id="source-53">Nature</source>, <volume>
                    638</volume>(<issue>8052</issue>):<fpage>1085</fpage>–<lpage>1094</lpage>, <year>2025</year>.
                    ISSN <issn>0028-0836</issn>. doi: <pub-id pub-id-type="doi">10.1038/s41586-024-08411-y</pub-id>.
                </citation>
            </ref>
            <ref id="c54" hwp:id="ref-54" hwp:rev-id="xref-ref-54-1">
                <label>54.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.54"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-54">
                    <string-name name-style="western" hwp:sortable="Conde C. Domínguez">
                        <given-names>C. Domínguez</given-names>
                        <surname>Conde</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Xu C.">
                        <given-names>C.</given-names>
                        <surname>Xu</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jarvis L. B.">
                        <given-names>L. B.</given-names>
                        <surname>Jarvis</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rainbow D. B.">
                        <given-names>D. B.</given-names>
                        <surname>Rainbow</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Wells S. B.">
                        <given-names>S. B.</given-names>
                        <surname>Wells</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Gomes T.">
                        <given-names>T.</given-names>
                        <surname>Gomes</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Howlett S. K.">
                        <given-names>S. K.</given-names>
                        <surname>Howlett</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Suchanek O.">
                        <given-names>O.</given-names>
                        <surname>Suchanek</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Polanski K.">
                        <given-names>K.</given-names>
                        <surname>Polanski</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="King H. W.">
                        <given-names>H. W.</given-names>
                        <surname>King</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mamanova L.">
                        <given-names>L.</given-names>
                        <surname>Mamanova</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Huang N.">
                        <given-names>N.</given-names>
                        <surname>Huang</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Szabo P. A.">
                        <given-names>P. A.</given-names>
                        <surname>Szabo</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Richardson L.">
                        <given-names>L.</given-names>
                        <surname>Richardson</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bolt L.">
                        <given-names>L.</given-names>
                        <surname>Bolt</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Fasouli E. S.">
                        <given-names>E. S.</given-names>
                        <surname>Fasouli</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mahbubani K. T.">
                        <given-names>K. T.</given-names>
                        <surname>Mahbubani</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Prete M.">
                        <given-names>M.</given-names>
                        <surname>Prete</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tuck L.">
                        <given-names>L.</given-names>
                        <surname>Tuck</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Richoz N.">
                        <given-names>N.</given-names>
                        <surname>Richoz</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Tuong Z. K.">
                        <given-names>Z. K.</given-names>
                        <surname>Tuong</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Campos L.">
                        <given-names>L.</given-names>
                        <surname>Campos</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Mousa H. S.">
                        <given-names>H. S.</given-names>
                        <surname>Mousa</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Needham E. J.">
                        <given-names>E. J.</given-names>
                        <surname>Needham</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Pritchard S.">
                        <given-names>S.</given-names>
                        <surname>Pritchard</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Li T.">
                        <given-names>T.</given-names>
                        <surname>Li</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Elmentaite R.">
                        <given-names>R.</given-names>
                        <surname>Elmentaite</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Park J.">
                        <given-names>J.</given-names>
                        <surname>Park</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Rahmani E.">
                        <given-names>E.</given-names>
                        <surname>Rahmani</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Chen D.">
                        <given-names>D.</given-names>
                        <surname>Chen</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Menon D. K.">
                        <given-names>D. K.</given-names>
                        <surname>Menon</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Bayraktar O. A.">
                        <given-names>O. A.</given-names>
                        <surname>Bayraktar</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="James L. K.">
                        <given-names>L. K.</given-names>
                        <surname>James</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Meyer K. B.">
                        <given-names>K. B.</given-names>
                        <surname>Meyer</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Yosef N.">
                        <given-names>N.</given-names>
                        <surname>Yosef</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Clatworthy M. R.">
                        <given-names>M. R.</given-names>
                        <surname>Clatworthy</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Sims P. A.">
                        <given-names>P. A.</given-names>
                        <surname>Sims</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Farber D. L.">
                        <given-names>D. L.</given-names>
                        <surname>Farber</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Saeb-Parsy K.">
                        <given-names>K.</given-names>
                        <surname>Saeb-Parsy</surname>
                    </string-name>
                    ,
                    <string-name name-style="western" hwp:sortable="Jones J. L.">
                        <given-names>J. L.</given-names>
                        <surname>Jones</surname>
                    </string-name>
                    , and
                    <string-name name-style="western" hwp:sortable="Teichmann S. A.">
                        <given-names>S. A.</given-names>
                        <surname>Teichmann</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-51">Cross-tissue immune cell analysis reveals tissue-specific
                    features in humans</article-title>. <source hwp:id="source-54">Science (New York,
                    N.Y</source>.), <volume>376</volume>(<issue>6594</issue>):<fpage>eabl5197</fpage>–<lpage>
                    eabl5197</lpage>, <year>2022</year>. ISSN <issn>0036-8075</issn>. doi: <pub-id pub-id-type="doi">
                    10.1126/science.abl5197</pub-id>.
                </citation>
            </ref>
            <ref id="c55" hwp:id="ref-55" hwp:rev-id="xref-ref-55-1">
                <label>55.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.55"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-55">
                    <string-name name-style="western" hwp:sortable="Hunter John D.">
                        <given-names>John D.</given-names>
                        <surname>Hunter</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-52">Matplotlib: A 2D Graphics
                    Environment</article-title>. <source hwp:id="source-55">Computing in Science &amp;
                    Engineering</source>, <volume>9</volume>(<issue>3</issue>):<fpage>90</fpage>–<lpage>
                    95</lpage>, <year>2007</year>. ISSN <issn>1521-9615</issn>. doi: <pub-id pub-id-type="doi">
                    10.1109/mcse.2007.55</pub-id>.
                </citation>
            </ref>
            <ref id="c56" hwp:id="ref-56" hwp:rev-id="xref-ref-56-1">
                <label>56.</label>
                <citation publication-type="journal" citation-type="journal" ref:id="2025.03.21.644627v1.56"
                          ref:linkable="yes" ref:use-reference-as-is="yes" hwp:id="citation-56">
                    <string-name name-style="western" hwp:sortable="Waskom Michael">
                        <given-names>Michael</given-names>
                        <surname>Waskom</surname>
                    </string-name>
                    . <article-title hwp:id="article-title-53">seaborn: statistical data visualization</article-title>. <source
                        hwp:id="source-56">Journal of Open Source Software</source>, <volume>6</volume>(<issue>
                    60</issue>):<fpage>3021</fpage>, <year>2021</year>. doi: <pub-id pub-id-type="doi">
                    10.21105/joss.03021</pub-id>.
                </citation>
            </ref>
        </ref-list>
    </back>
</article>
