package org.biosino.lf.pds.article.dto;

import lombok.Data;

/**
 * 文献导入DTO
 *
 * <AUTHOR>
 */
@Data
public class ArticleImportDTO {

    private Long customId;

    /**
     * PMID
     */
    private Long pmid;

    /**
     * PMC ID
     */
    private Long pmcId;

    /**
     * DOI
     */
    private String doi;

    /**
     * 来源（用分号分隔）
     */
    private String source;


    /**
     * 标题
     */
    private String title;

    private Long journalId;

    /**
     * 期刊名称
     */
    private String journalName;

    /**
     * 发布年份
     */
    private Integer publishedYear;

    /**
     * 发布月份
     */
    private Integer publishedMonth;

    /**
     * 发布日期
     */
    private Integer publishedDay;

    /**
     * 卷号
     */
    private String volume;

    /**
     * 期号
     */
    private String issue;

    /**
     * 页码
     */
    private String page;

    /**
     * 作者列表（用分号分隔）
     */
    private String author;

    /**
     * 机构列表（用分号分隔）
     */
    private String organization;

    /**
     * 关键词列表（用分号分隔）
     */
    private String keyword;

    /**
     * 摘要
     */
    private String articleAbstract;


}
