package org.biosino.lf.pds.article.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.lf.pds.common.core.domain.BaseQuery;

/**
 * 出版社查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PublisherQueryDTO extends BaseQuery {

    /**
     * 出版社名称
     */
    private String name;

    /**
     * 出版社地址
     */
    private String ioc;

    /**
     * 状态（0-停用，1-正常）
     */
    private Integer status;

    /**
     * 来源类型
     */
    private String sourceType;
}
