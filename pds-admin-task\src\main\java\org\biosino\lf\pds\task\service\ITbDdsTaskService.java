package org.biosino.lf.pds.task.service;

import com.alibaba.fastjson2.JSONObject;
import org.biosino.lf.pds.article.custbean.dto.TaskPublishDTO;
import org.biosino.lf.pds.article.domain.TbDdsTask;
import org.biosino.lf.pds.article.domain.TbDdsTaskPaper;
import org.biosino.lf.pds.article.domain.TbDdsTaskSchedule;
import org.biosino.lf.pds.article.service.CommonService;
import org.biosino.lf.pds.common.enums.task.TaskPaperStatusEnum;
import org.biosino.lf.pds.common.enums.task.TaskSourceEnum;

import java.util.List;

/**
 * pds任务服务层
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
public interface ITbDdsTaskService extends CommonService<TbDdsTask> {
//    AjaxResult uploadIdExcel(MultipartFile file);

    /**
     * 发布文献传递任务
     *
     * @param dto    任务发布数据
     * @param userId
     * @return 创建的任务实体
     */
    TbDdsTask publishTask(TaskPublishDTO dto, Long userId, TaskSourceEnum taskSourceEnum);

    /**
     * 分配任务
     */
    void schedule(String taskId);

    boolean hasPaperByTaskIdAndStatus(String taskId, List<TaskPaperStatusEnum> status);

    TbDdsTaskPaper findTaskPaper(String taskId, Long docId);

    TbDdsTaskSchedule findTaskSchedule(Long paperId, Integer siteId);

}
