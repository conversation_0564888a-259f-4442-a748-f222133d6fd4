package org.biosino.lf.pds.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.io.IOUtils;
import org.apache.pdfbox.io.RandomAccessRead;
import org.apache.pdfbox.io.RandomAccessReadBufferedFile;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;

import java.io.File;

/**
 * PDF文件工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class PDFHelper {
//    private static final int USE_MEMORY_MAX_SIZE = 20 * 1024 * 1024;

    public static boolean checkPDF(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return false;
        }
        return checkPDF(new File(filePath));
    }

    /**
     * 检测文件是否是合法的PDF文件
     */
    public static boolean checkPDF(final File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        RandomAccessRead randomAccessRead = null;
        PDDocument doc = null;
        try {
            randomAccessRead = new RandomAccessReadBufferedFile(file);
            final PDFParser parser = new PDFParser(randomAccessRead, "", null, null, IOUtils.createMemoryOnlyStreamCache());
            doc = parser.parse();
            return doc != null;
        } catch (Exception e) {
            // e.printStackTrace();
            log.warn("非法的pdf：{}, {}", e.getMessage(), file.getAbsoluteFile());
            return false;
        } finally {
            IOUtils.closeQuietly(doc);
            IOUtils.closeQuietly(randomAccessRead);
        }

        // 根据文件大小选择不同的缓存策略
        /*final RandomAccessStreamCache.StreamCacheCreateFunction cacheStrategy;
        if (file.length() < USE_MEMORY_MAX_SIZE) {
            // 小文件，使用内存缓存
            cacheStrategy = IOUtils.createMemoryOnlyStreamCache();
        } else {
            // 大文件，使用临时文件缓存
            cacheStrategy = IOUtils.createTempFileOnlyStreamCache();
        }
        // PDDocument document = Loader.loadPDF(randomAccessRead)
        */
    }

    /**
     * 检测文件是否是合法的PDF文件
     */
    /*public static boolean checkPDF2(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }
        PdfReader pdfReader = null;
        try {
            pdfReader = new PdfReader(new FileInputStream(file));
            return true;
        } catch (Exception e) {
            log.warn("非法的pdf：{}", e.getMessage());
            return false;
        } finally {
            IOUtils.closeQuietly(pdfReader);
        }
    }*/
    public static void main(String[] args) {
        System.out.println(PDFHelper.checkPDF("C:\\Users\\<USER>\\Downloads\\test.pdf"));
        //System.out.println(PDFHelper.checkPDF("C:\\Users\\<USER>\\Downloads\\test.docx"));
    }
}
