---
description: 
globs: 
alwaysApply: true
---
# 接口设计与数据库操作指南

本项目采用基于MyBatis-Plus的接口设计和数据库操作模式，以下是主要的设计模式和使用方法。

## 接口设计模式

### 服务接口继承关系

项目中的服务接口遵循以下继承模式：

```java
public interface IArticleService extends IService<Article> {
    
    // 特定的业务方法
    void merge(Long pubmedArticleId, Long pmcArticleId);

    void saveOne(ArticleInfoDTO dto);
}
```

所有实体服务接口都继承自MyBatis-Plus的`IService<T>`接口，获得通用的CRUD操作能力：
- `save(T entity)` - 保存单个实体
- `saveBatch(Collection<T> entityList)` - 批量保存实体
- `removeById(Serializable id)` - 根据ID删除
- `updateById(T entity)` - 根据ID更新
- `getById(Serializable id)` - 根据ID查询
- `list()` - 查询所有
- `listByIds(Collection<? extends Serializable> idList)` - 根据ID列表查询

### 自定义查询方法

除了基本CRUD操作外，接口中定义了特定的业务查询方法：

```java
public interface IReferenceService extends IService<Reference> {
    // 根据文档ID查询引用
    List<Reference> findByDocId(Long docId);
    
    // 根据文档ID删除引用
    boolean removeByDocId(Long id);
}
```

## 数据库操作语法

### Mapper层

项目使用MyBatis-Plus的Mapper接口进行数据库操作：

```java
public interface ArticleMapper extends BaseMapper<Article> {
    // 可以添加自定义SQL方法
}
```

### 条件构造器

使用`Wrappers`类构建查询条件：

```java
// Lambda表达式方式构建查询条件
Article pubmedArticle = this.getOne(
    Wrappers.<Article>lambdaQuery()
        .eq(Article::getPmid, pmid)
);

// 使用条件构造器进行删除
boolean removed = articleAuthorService.remove(
    Wrappers.<ArticleAuthor>lambdaQuery()
        .eq(ArticleAuthor::getDocId, docId)
);
```

### 批量操作

项目中大量使用批量操作来提高性能：

```java
// 批量保存
if (CollUtil.isNotEmpty(authorList)) {
    authorService.saveBatch(authorList);
}

// 批量删除
authorService.removeBatchByIds(removeAuthorIds);
```

## 服务实现类

服务实现类继承自`ServiceImpl<M, T>`，获得基础实现：

```java
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements IArticleService {
    // 依赖注入其他服务
    private final IArticleDatabankService databankService;
    private final IArticlePubTypeService articlePubTypeService;
    
    // 实现接口方法
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOne(ArticleInfoDTO dto) {
        // 实现代码
    }
}
```

## 事务管理

使用`@Transactional`注解管理事务：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void merge(Long pubmedArticleId, Long pmcArticleId) {
    // 事务内的操作
}
```

## 常用查询模式

### 根据外键查询

```java
public List<ArticleAuthor> findByDocId(Long docId) {
    return this.list(
        Wrappers.<ArticleAuthor>lambdaQuery()
            .eq(ArticleAuthor::getDocId, docId)
    );
}
```

### 自定义条件查询

```java
public List<PubType> findByPubTypeIn(Collection<String> pubTypes) {
    return this.list(
        Wrappers.<PubType>lambdaQuery()
            .in(PubType::getPubType, pubTypes)
    );
}
```

### 复杂条件组合

```java
List<Article> articles = articleService.list(
    Wrappers.<Article>lambdaQuery()
        .eq(Article::getSource, "pubmed")
        .ge(Article::getPublishedYear, 2020)
        .like(Article::getTitle, "%gene%")
        .orderByDesc(Article::getPublishedDate)
);
```

