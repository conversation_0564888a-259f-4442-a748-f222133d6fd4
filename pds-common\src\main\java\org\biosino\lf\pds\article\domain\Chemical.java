package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 化学物质信息表
 */
@Data
@TableName(value = "tb_dds_chemical", autoResultMap = true)
public class Chemical {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 注册号
     */
    @TableField("registry_no")
    private String registryNo;

    /**
     * MeSH ID
     */
    @TableField("mesh_id")
    private String meshId;
}
