---
description: 
globs: 
alwaysApply: true
---
# 项目结构指南

本项目是一个用于解析和处理学术文章数据的Java应用程序。它主要处理来自PubMed和PMC的XML格式文章数据，解析其中的各种信息并存储到数据库中。

## 核心组件

- **主入口**：[Main.java](mdc:src/main/java/org/biosino/lf/pds/article/Main.java) 是应用程序的入口点，展示了如何使用解析器解析XML文件。

- **XML解析服务**：
  - [XmlParseService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/XmlParseService.java) - XML解析的接口定义
  - [PubmedXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/PubmedXmlParse.java) - PubMed XML解析实现
  - [PmcXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/PmcXmlParse.java) - PMC XML解析实现
  - [BioRxivXmlParse.java](mdc:src/main/java/org/biosino/lf/pds/article/service/BioRxivXmlParse.java) - BioRxiv XML解析实现

- **数据传输对象**：
  - [ArticleInfoDTO.java](mdc:src/main/java/org/biosino/lf/pds/article/dto/ArticleInfoDTO.java) - 包含文章所有信息的DTO
  - [ArticleDTO.java](mdc:src/main/java/org/biosino/lf/pds/article/dto/ArticleDTO.java) - 文章数据传输对象

## 领域模型

项目使用以下领域模型来表示文章及其相关信息：

- [Article.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Article.java) - 文章的核心信息
- [Author.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Author.java) - 作者信息
- [Organization.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Organization.java) - 组织/机构信息
- [Reference.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/Reference.java) - 引用文献
- [PubMedPubDate.java](mdc:src/main/java/org/biosino/lf/pds/article/domain/PubMedPubDate.java) - 发布日期信息

## 服务层

项目包含多个服务接口和实现，用于处理文章数据的各个方面：

- [IArticleService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IArticleService.java) - 文章服务接口
- [IAuthorService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IAuthorService.java) - 作者服务接口
- [IReferenceService.java](mdc:src/main/java/org/biosino/lf/pds/article/service/IReferenceService.java) - 引用文献服务接口

## 技术栈

该项目基于以下技术：
- Java
- Spring Boot
- MyBatis-Plus
- JSoup (用于XML解析)
- Dom4j (用于XML处理)
- PostgreSQL (数据库)

