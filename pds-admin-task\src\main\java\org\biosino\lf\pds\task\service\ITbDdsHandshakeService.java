package org.biosino.lf.pds.task.service;

import jakarta.servlet.http.HttpServletRequest;
import org.biosino.lf.pds.article.domain.TbDdsHandshake;
import org.biosino.lf.pds.article.custbean.dto.api.HandshakeDTO;
import org.biosino.lf.pds.article.custbean.vo.api.ApiResultVO;
import org.biosino.lf.pds.article.service.CommonService;

/**
 * 握手信息服务层
 *
 * <AUTHOR>
 */
public interface ITbDdsHandshakeService extends CommonService<TbDdsHandshake> {

    ApiResultVO sendHandshake(HandshakeDTO dto, HttpServletRequest sysInfo);

}
