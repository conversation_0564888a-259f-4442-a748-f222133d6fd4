package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;

import java.util.List;

/**
 * 期刊脚本DTO
 *
 * <AUTHOR>
 */
@Data
public class JournalScriptDTO extends SelectJournalDTO {
    /**
     * 期刊ID列表
     */
    private List<Long> journalIds;

    /**
     * 脚本ID
     */
    private Integer scriptId;

    /**
     * 是否选中所有页
     */
    private Boolean selectAllPage;

    /*public void setJournalIds(List<Long> journalIds) {
        this.journalIds = emptyListToNull(journalIds);
    }*/
}
